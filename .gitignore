# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# Using yarn, avoid package-lock.json
package-lock.json

# testing
/coverage

# next.js
.next/
/out/

# production
/build


# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*



# vercel
.vercel

# IntelliJ
*.iml

# VS Code
.vscode
.idea/

/web-desk-penale/node_modules
/nbproject/private/
nbproject/

patch/build/
distribution/patch/src/main/scripts/web-desk-penale/
/.gradle/
/.project
.gradle
.settings
.classpath
.project
*.iml
/.nb-gradle/
.nb-gradle-properties
/distribution/patch/build/
/web-desk-penale/build/
.yalc
yalc.lock
web-desk-penale/.yalc/
web-desk-penale/yalc.lock

distribution/build/
/gradlew
/gradlew.bat
/gradle/wrapper/gradle-wrapper.jar
/gradle/wrapper/gradle-wrapper.properties
web-desk-penale/__generated__/
.history/
web-desk-penale/next-env.d.ts

web-desk-penale/__generated__/
# local env files non vanno pushati!
web-desk-penale/.env.local
web-desk-penale/.env.develop*
web-desk-penale/.env.test*
web-desk-penale/.env.production*
web-desk-penale/.env
web-desk-penale/yarn.lock
