image: $DOCKER_REGISTRY/cicd/gradle-yarn-image:node20-gradle8

variables:
  CI: ""

stages:
  - audit
  - publishArchives
  - trigImagePusher
  - publishRelease
  - analysis

before_script:
  - "echo CI coordinates: $CI_BUILD_NAME, $CI_BUILD_REF_NAME $CI_BUILD_STAGE"
  - "echo nexus auth: $CI_NEXUS_USERNAME"
  - "export GRADLE_USER_HOME=`pwd`/.gradle"
  - "echo gradle user home: $GRADLE_USER_HOME"

cache:
  paths:
    - .gradle

audit:
  stage: audit
  script:
    - cd web-desk-penale && yarn audit | grep -q '"severity":"high\|moderate"' && echo "Vulnerabilities detected, failing the build." && exit 1 || echo "No vulnerabilities found." && exit 0
  except:
    - tags

publishArchives:
  stage: publishArchives
  script:
    - gradle -PnexusUsername=$CI_NEXUS_USERNAME -PnexusPassword=$CI_NEXUS_PASSWORD -PnsPluginVersion=$NS_PLUGIN_VERSION -PpatchPluginVersion=$PATCH_PLUGIN_VERSION --refresh-dependencies --parallel --no-daemon build --stacktrace
    - cd web-desk-penale/build && zip -r web-desk-penale.zip .
    - curl -v --user $CI_NEXUS_USERNAME:$CI_NEXUS_PASSWORD --upload-file web-desk-penale.zip https://nexuspa.netserv.it/repository/binaries/products/cassazione/cassazione-penale/web-desk-penale/$CI_COMMIT_REF_NAME/web-desk-penale.zip


trigImagePusher:
  stage: trigImagePusher
  script:
    - sh /scripts/trigPusher.sh 667 $TRIGGER_TOKEN $CI_COMMIT_REF_NAME $CI_COMMIT_REF_NAME
    #- curl -i -X POST -d 'payload={"text":"@testers web-desk-penale '"$CI_COMMIT_REF_NAME"' modificato, riavviare il bat startDocker", "username":"GitLab"}' https://nchat.netserv.it/hooks/r6hep5k8sfbudbqj71t5th1tiw
 
publishRelease:
  stage: publishRelease
  script:
    - gradle -PnexusUsername=$CI_NEXUS_USERNAME -PnexusPassword=$CI_NEXUS_PASSWORD -PnsPluginVersion=$NS_PLUGIN_VERSION -PpatchPluginVersion=$PATCH_PLUGIN_VERSION --refresh-dependencies --parallel --no-daemon publishPatch
    - bash /scripts/copyPortale.sh -p "Web Desk Penale" -t "centralizzato" -h $NETSUP_HOST -u $NETSUP_USER -s $SSH_SERVER_PRIVATE_KEY
    - curl -i -X POST -d 'payload={"text":"@all Patch Web Desk Penale in rilascio '"$CI_COMMIT_REF_NAME"' disponibile su Draft --> https://portalegiustiziacivile.netserv.it/web/guest/login  ", "username":"GitLab"}' $MATTERMOST_HOOK_CASSPENALE_HD
  only:
    variables:
      - $CI_COMMIT_TAG =~ /((([0-9]*)(\.))(([0-9]{2})(\.)){2}([0-9]){3}((-RC)([0-9]+))?)|(\w*(\.)([0-9]){3}((-RC)([0-9]+)))/

sonarManual:
  stage: analysis
  when: manual
  script:
    - cd web-desk-penale && sonar-scanner -Dsonar.host.url=$CI_SONAR_URL -Dsonar.login=$CI_SONAR_TOKEN -Dsonar.verbose=true
  except:
    - tags

sonar:
  stage: analysis
  script:
    - cd web-desk-penale && sonar-scanner -Dsonar.host.url=$CI_SONAR_URL -Dsonar.login=$CI_SONAR_TOKEN -Dsonar.verbose=true
  only:
    variables:
      - $CI_COMMIT_TAG =~ /(([0-9]*)(\.))(([0-9]{2})(\.)){2}([0-9]){3}((-RC)([0-9]+))?/
