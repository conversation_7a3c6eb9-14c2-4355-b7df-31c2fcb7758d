## Frontend web del Desk Cassazione Penale

### Documentazione di progetto

* [Architettura](/doc/architettura_cass_penale-Architettura.jpg)
* [Risorse utili](/doc/risorse_formazione.md) per la formazione (guide, tutorial, articoli...)
* [Best practices](/doc/best_practices.md)

### Build

- Installare Node.js (>= `16.15.0`)  
- Installare `yarn` (>= `1.22.19`) con `npm install -g yarn`
- Clonare il repository
- Entrare nella cartella del progetto   
- Eseguire `yarn install` per installare le dipendenze
- yarn add @types/react-
- Fare una copia locale di .env.example e chiamarla .env. Si può modificare in locale la configurazione in base alle esigenze di sviluppo.
- Avviare il progetto in con `yarn dev`
- Nel caso di errore 431 dopo il login su Iam Azure, impostare la variabile d'ambiente `NODE_OPTIONS='--max-http-header-size=40960`

### Gestione librerie

TODO ...

### Gestione stringhe

Le stringhe di testo dell'applicativo vanno inserite in public/locales/it/translation.json. Usare l'hook `useTranslation()` nel codice applicativo per caricare le traduzioni.

### Gestione stato

TODO

### Invocazione servizi graphql

TODO

### Login

La configurazione è nel file [...nextauth].ts

- Utente di test su Keycloak: test:test
- In locale è configurato anche un'utenza
- https://dev2-docker-sigma/auth e entri con admin/admin

### GESTIONE ERRORI

GESTIONE ERRORE CON AXIOS interceptors

- si è utilizzato axios interceptors response per catturare tutte le risposte del server il codice si trova dentro [_app.tsx]

````typescript
    axios.interceptors.response.use(function (response) {
     // Do something with response data
      return response;
     }, function (error) {
        if(error?.response?.status === 401){
        if(error?.response?.data?.message?.startsWith('CODE_')){
        notify({
        message: t('server.errorCode.'+error?.response?.data?.message),
        type: 'error',
        });
     } else {
        notify({
        message: error?.response?.data?.message || t('server.errorCode.genericError'),
        type: 'error',
     });
  }}
     ```
- tutti i codici errori del server devono iniziare con CODE_ e devono essere inseriti nel file di traduzione (translatetion.json) con server.errorCode.[erroredelServer]
-  esempio di traduzione:
```json
   "server": {
      "errorCode": {
         "CODE_TOKEN_EXPIRED": "Autentificazione scaduta riesegui il login",
         "CODE_UNAUTHORIZED": "Non sei autorizzato ad accedere contattare il CED",
         "genericError": "Errore generico contattare il CED"
         }
      },
   }
````

- se il codice di errore non esiste viene sollevato l'errore generico server.errorCode.genericCode
## Miglioramento GRAPHQL 
Miglioramento chiamate con grapgql
* controllare nella rete le chiamate che vengono eseguite al BE si possono unire 
* quando si esegue una chiamata al BE inserire nella relayQuery solo i campi che si utilizzando 
* se si effettua al BE 2 chiamate simile andare sul BE e vedere se si possono inserire nuovi campi o migliorare le query con i resolver
* controllare se ci sono indici sulla tabella per il campo che si esegue la ricerca
#### N.B.B.B 
non si devono inserire nella chiamata graphql campi che non si utilizzano meglio creare delle relayquery simili ma non utilizzare relayQuery utilizzate in altri punti( se sono oggettio campi calcolati)
## GRAPHQL 
* le query  graphql devono iniziare con il nome del file e finire con query 
 esempio ome file ``` relayQueryUdienza```
 ```
  query relayQueriesUdienza_[NomeQuery]Query {
  udienzeWithProvvedimentoDet(idUdien: $idUdin, nrg: $nrg) {
    termineDeposito
```
