plugins {
    id "ns-patch" version "${patchPluginVersion}"
}

patch {
    includeVersionFile true
    basename = 'WEB-DESK-CP'
}



releaseNotes{
    withAs true
    
    // withDb a false per evitare che aggiunga la sezione per script db presenti nello stesso progetto.
	withDb false

    //modalitaApplicazioneDB "senza_fermo"
    modalitaApplicazioneDB "con_fermo"
    applicazionePatchAS {
        descrizione = "Il presente documento descrive la procedura di aggiornamento (patch) del prodotto Desk Penale Penale. La patch è rilasciata sotto forma di file compresso (.zip). Nel documento sono inoltre elencati impatti, modifiche ed interventi oggetto del presente aggiornamento."
        elenco = ["Effettuare il login alla macchina AS come utente root",
                  "Copiare il file zip in una cartella temporanea e scompattarlo",
                  "Stoppare il servizio httpd",
                  "Eseguire lo script 'install.sh' presente nella cartella application_server",                  
                  //"Eseguire le configurazioni del paragrafo 'Modifiche Configurazione', per riavviare il servizio web-deskcp.service ",
                  "Riavviare il servizio httpd"
        ]
    }
    
    
    modificheEvolutive{
        descrizione = "Contratto CIG B2BD866C4A - PLO21 - Interventi migliorativi per l’operatività della Settima Sezione Penale"
    }
    
    
   
    dipendenzeSistemi{
        descrizione = "Installare prima la patch SERVIZI-DESK-CP-1.03.00"
    }
    

    /*
    modificheDB {
        descrizione = "Sono riportate nella patch CASSAZIONE_PENALE_DB_1.04.00_RC"
    }
   


     /*modificheConfigurazione {
        descrizione = "Nel file del servizio /usr/lib/systemd/system/web-deskcp.service modificare la  WorkingDirectory in '/opt/web-desk-penale'. Eseguire il comando 'systemctl daemon-reload' e 'systemctl restart web-deskcp.service'"
    } 
    */


/*     documentazione {

        descrizione = "Rilasciato il manuale utente MG-DESK-MU-CASS-PENALE-001-NS.pdf"
    } */

    
    /* interventiEseguiti {
          descrizione = "Risoluzione ticket 46826 di SERVIZI-DESK-CP"
            //  elenco=["Collegarsi alla macchina client del Portale DESK Cassazione Penale",
            //          "Accedere al file di configurazione .env nella root dell’applicazione (/opt/app)",
            //          "Inserire il seguente parametro per abilitare o meno la firma remota: ",
            //          "FIRMA_REMOTA=true"
            //           ] 
     } */
     
}



task copyBuild(type: Copy) {
    from "$rootDir/web-desk-penale/build"
    into "$buildDir/application_server/web-desk-penale"
}

copyBuild.dependsOn ':web-desk-penale:build'

afterEvaluate{
    tasks['patch'].dependsOn copyBuild
}
