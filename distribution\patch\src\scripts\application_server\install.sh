#!/bin/sh
# Ottiene il path assoluto dove si trova il nostro script
SCRIPT=$(readlink -f "$0")
SCRIPTPATH=$(dirname "$SCRIPT")
INSTALL_FOLDER=/opt/web-desk-penale
PATCH_INFO_FILE=/opt
SHARP_FOLDER=/opt/sharp
TMP_FOLDER=/tmp
BACKUP_FOLDER="$TMP_FOLDER/$(date +"%d-%m-%Y")_backup_conf"
BACKUP_CONFIGURAZIONI=$BACKUP_FOLDER/conf
prettyToday=`date +"%d/%m/%Y %H:%M:%S"`
patchVersion=`cat ./include/.version`


echo "Installazione patch Portale DESK Cassazione Penale"
echo "INSTALL_FOLDER=$INSTALL_FOLDER"
echo "BACKUP_FOLDER=$BACKUP_FOLDER"
echo "BACKUP_CONFIGURAZIONI=$BACKUP_CONFIGURAZIONI"
echo "Verifico lo stato del Portale DESK Cassazione Penale"

# systemctl is-active web-deskcp.service  \
# && echo "Il Portale DESK Cassazione Penale risulta attivo. Fermarlo prima di procedere all'applicazione della patch." \
# && exit 1

echo "Arresto del servizio web-deskcp.service del Portale DESK Cassazione Penale"
systemctl stop  web-deskcp.service


mkdir -p "$BACKUP_CONFIGURAZIONI"
echo "Creazione backup configurazioni in $BACKUP_CONFIGURAZIONI"
cp -i "$INSTALL_FOLDER"/.env* "$BACKUP_CONFIGURAZIONI"
echo "Creazione backup della versione precedente in $BACKUP_FOLDER"



zip -qr "$BACKUP_FOLDER/web-desk-penale_$(date +"%d-%m-%Y").zip" $INSTALL_FOLDER
echo "Rimozione della versione precedente del prodotto"

[ ! -d "$SHARP_FOLDER" ] && mkdir -p "$SHARP_FOLDER" \
&& echo "Inizio unzip della folder sharp." \
&& unzip "$INSTALL_FOLDER"/resources/sharp.zip -d "$SHARP_FOLDER" \
&& echo "La folder sharp e' stata unzippata." [ ! -d "$SHARP_FOLDER" ] && mkdir -p "$SHARP_FOLDER" 

rm -rf $INSTALL_FOLDER
echo "Installazione della nuova versione"
cp -r "$SCRIPTPATH/web-desk-penale" $INSTALL_FOLDER
chown -R node: $INSTALL_FOLDER
echo "Ripristino dei file di configurazione"
yes | cp -i "$BACKUP_CONFIGURAZIONI"/* $INSTALL_FOLDER/ 2> /dev/null
yes | cp -i "$BACKUP_CONFIGURAZIONI"/.env* $INSTALL_FOLDER/ 2> /dev/null

# registrazione dell'applicazione della patch
touch $PATCH_INFO_FILE/patch.log
echo "$prettyToday - $patchVersion" >> $PATCH_INFO_FILE/patch.log

echo "=================================================="
echo "Applicazione della patch terminata."
echo "=================================================="
echo "Avviato il servizio web-deskcp.service e controllare nel file di log /var/log/web-deskcp.log il corretto avvio del servizio"
systemctl start  web-deskcp.service
