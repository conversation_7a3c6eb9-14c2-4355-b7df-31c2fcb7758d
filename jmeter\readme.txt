Guida utilizzo Apache JMeter e definizione dei test

Installazione e avvio JMeter
	- Scaricare JMeter dal seguente link: https://jmeter.apache.org/download_jmeter.cgi e installarlo
	- Una volta installato l'applicativo per avviare JMeter recarsi nel percorso di installazione, quindi entrare nella cartella bin ed eseguire il file ApacheJMeter.jar

Caricare test plan su JMeter
	- Dal menu File -> Open
	- Selezionare il file .jmx che si intende caricare

Aggiungere una nuova Request GraphQL
	- Recupero informazioni della chiamata GraphQL:
		- Dal browser recarsi all'indirizzo dal quale richiamare il servizio da testare
		- Aprire la console e spostarsi nella sezione Rete(Network)
		- Richiamare il servizio che si desidera riportare su JMeter, così da visualizzarlo nella console
		- Dalla chiamata graphql recuperare dalla sezione 'Headers' in 'Request Headers' le informazioni relative al Bearer token (va preso solo il token senza 'Bearer ')
		- Nella sezione Payload copiare il valore contenuto in 'query' e in 'variables' (se presenti)
	- Su Apache JMeter:
		- Dal menu di sinistra del test plan posizionarsi in corrispondenza del Thread (o dal logic controller se deve essere incluso in uno di esso), quindi click col tasto destro e andare su Add -> Sampler -> GraphQL HTTP Request
		- Una volta creata, aggiungere le informazioni necessarie per poter effettuare la chiamata al servizio (Protocol, Server Name, Path), nella sezione 'Query' riportare la query copiata dal campo 'query' del payload della console del browser, in più se presenti riportare le variabili nella sezione 'Variables' (recuperate dal campo 'variables' del payload della console del browser). Per i valori delle Variables è possibile utilizzare delle variabili definite in precedenza, ad esempio possono essere recuperate dalle User Defined Variables, per farlo si utilizza la notazione ${nome_variabile}:
			-es: { "id": ${idUdienza}} , dove idUdienza è una variabile presente nelle User Defined Variables
		- Per la request creata è possibile aggiungere delle assertion (dal menu laterale posizionarsi in corrispondenza della Request aggiunta, quindi click col tasto destro Add -> Assertions) per effettuare dei controlli sulla risposta ricevuta. Inoltre possono essere aggiunti anche dei listener (dal menu laterale posizionarsi in corrispondenza della request aggiunta, quindi click col tasto destro Add -> Listeners) per memorizzare informazioni utili sui test effettuati
		
Aggiungere una nuova Request REST API
	- Recupero informazione dalla chiamata REST:
		- Le informazioni possono essere recuperate direttamente da Swagger UI (in localhost l'indirizzo è http://localhost:3001/api/v1/desk-swagger, accedere ad readme del progetto desk-cassazione-penale-backend per maggiori inforazioni su Swagger)
		- All'interno della pagina cercare la chiamata che si desidera riportare su JMeter
	- Su Apache JMeter:
		- Dal menu di sinistra del test plan posizionarsi in corrispondenza del Thread (o dal logic controller se deve essere incluso in uno di esso), quindi click col tasto destro e andare su Add -> Sampler -> HTTP Request
		Una volta creata, aggiungere le informazioni necessarie per poter effettuare la chiamata al servizio (Protocol, Server Name, Path e tipo di richiesta), inoltre se richiesti: nella sezione 'Parameters' riportare i parametri richiesti per la chiamata (presenti nella sezione 'Parameters' in Swagger), in 'Body Data' riportare le informazioni contenuti nel 'Request Body' di Swagger (andranno poi modificati i campi), infine in 'Files Upload' indicare eventuali file da inviare nella richiesta specificando nome del parametro e path del file. E' possibile utilizzare le variabili definite nella sezione delle 'User Defined Variables' per farlo si utilizza la notazione ${nome_variabile}
			-es: { "id": ${idUdienza}} , dove idUdienza è una variabile presente nelle User Defined Variables
		- Per la request creata è possibile aggiungere delle assertion (dal menu laterale posizionarsi in corrispondenza della Request aggiunta, quindi click col tasto destro Add -> Assertions) per effettuare dei controlli sulla risposta ricevuta. Inoltre possono essere aggiunti anche dei listener (dal menu laterale posizionarsi in corrispondenza della request aggiunta, quindi click col tasto destro Add -> Listeners) per memorizzare informazioni utili sui test effettuati

Effettuare un test
	- Per poter effettuare un test al momento è necessario recuperare un token di autenticazione valido, per ottenerlo dalla console del browser come riportato nella sezione di sopra 'Aggiungere una nuova Request GraphQL' -> 'Recupero informazioni della chiamata GraphQL'. Il valore va riportato all'interno delle User Defined Variables (Desk Variables), sostituire il valore del token corrispondente (tokenRelatoreEstensore o tokenPresidente in base al tipo di utente)
	- Per avviare il test cliccare sul relativo pulsante Start situato nel menu in alto (icona play verde), dal menu Run -> Start, oppure tramite comando CTRL+R 
	- Si possono Abilitare/Disabilitare le request per includerle/escludere dal test, per farlo selezionare la Request (può essere fatto anche a livello di Thread Group e di Controller) click col tasto destro e selezionare Enable/Disable. Quando è disabilitato compare nel menu laterale di sinistra con colore grigio

Aggiungere un nuovo Thread Group
	- Dal menu laterale di sinistra posizionarsi su Test Plan, quindi click col tasto destro e andare su Add -> Threads (user) -> Thread Group
	- Thread Group attualmente creato:
		- Graphql
		- REST API
	
Aggiungere nuovi Logic Controller
	- Dal menu laterale di sinistra posizionarsi sul Thread Group, quindi click col tasto destro e andare su Add -> Logic Controller -> Seleziona il controller che si desidera utilizzare
	- Logic controller attualmente creati in graphql:
		- RelatoreEstensore (contiene query graphql eseguite con utente di tipo relatore/estensore)
		- Presidente (contiene query graphql eseguite con utente di tipo presidente)
		- Mutation (contiene l'insieme delle mutation graphql disponibili nell'applicativo)
	- Logic controller attualmente creati in REST API:
		- configuration
		- auth
		- datiScrivania
		- provvedimenti-estensore
		- provvedimenti-presidente
		- template
		
Aggiungere nuove variabili Utente
	- Nel menu laterale di sinistra, sotto Test Plan è presente 'Desk Variables', che contiene un insieme di variabili comuni ai vari Thread Group. All'interno dei Simple Controller invece sono presenti ulteriori variabili utilizzate nelle Request di quello specifico Simple Controller, quindi se la variabile deve essere utilizzata all'interno di una o più chiamate dello stesso Simple Controller si possono aggiungere direttamente al suo interno.
	
Aumentare il numero di utenti per eseguire più volte la stessa Request
	- Andare sul Thread group (es. GraphQL), nella sezione 'Thread Properties' inserire nel campo 'Number of Threads (user)' il valore corrispondente al numero di utenti che si desidera utilizzare, se ad esempio il valore è impostato su 10 le Request contenute al suo interno verranno eseguite 10 volte
	- Sempre nella stessa sezione 'Thread Properties' il campo 'Ramp-up period (seconds)' viene utilizzato per stabilire un intervallo di tempo entro il quale devono essere effettuate le chiamate della singola Request:
		-es: se 'Number of Threads (user)' è pari a 10 e 'Ramp-up period (seconds)' è pari a 1, ogni chiamata verrà eseguita a distanza di 10/1 = 0.1 secondi

Link per Playground graphql (strumento utile per effettuare dei test)
	- Per dockerpa3 -> https://dockerpa3.netserv.it/graphql
	- Per recuperare le informazioni necessarie basta seguire la stessa procedura presente nella sezione di sopra 'Aggiungere una nuova Request GraphQL' -> 'Recupero informazioni della chiamata GraphQL'
	- Copiare la query nell'area di sinistra della pagina (dove è riportato "# Write your query or mutation here")
	- In basso sono presenti due tab:
		- Query variables, dove inserire le variabili della query
		- HTTP Headers, dove al suo interno andrà riportato il bearer token, nel seguente formato (sostituire token_generato con il token prelevato dalla chiamata effettuata):
			{
				"authorization": "Bearer token_generato"
			}