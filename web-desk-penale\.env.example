NEXT_PUBLIC_BASE_PATH=
# inserire hostname del Frontend che viene risolto dal proxy quando ambiente diverso da localhost e in presenza del proxy
SERVIZI_PORTALE_URL=http://localhost:3001
#SERVIZI_PORTALE_URL=https://dockerpa2.netserv.it
GLOBAL_PATH=api/v1

NODE_ENV=production
#ottimizzazione web in produzione
#NEXT_SHARP_PATH=/opt/sharp/node_modules/sharp

# per attivare IAM Keycloak
IS_DEBUG=false

#Utility
ENABLE_FIRMA_OTP=false
ENABLE_MODHEADER=false

#Impostare il salvataggio automatico in minuti accetta un double
AUTOSAVE_TIMER=1

#CONFIGURAZIONE NEXT AUTH
#per il redirect URL da IAM
NEXTAUTH_URL=http://localhost:3000
#NEXTAUTH_URL=https://dockerpa2.netserv.it
#NEXTAUTH_URL=https://web-deskcp-magistrato-pre
NEXTAUTH_SECRET=secret
#per disattivare il controllo dei certificati Nodejs
NODE_TLS_REJECT_UNAUTHORIZED=0

#web proxy per rete giustizia
#http_proxy=http://sdm:giustizia@**************:3128


#APP DEV
AZURE_AD_B2C_CLIENT_ID="1b653aa9-30e8-4a29-a4ed-c53c725f0a09"
AZURE_AD_B2C_CLIENT_SECRET="****************************************"
AZURE_AD_B2C_TENANT_NAME="b2cmingiustiziaspidcoll"
AZURE_AD_B2C_PRIMARY_USER_FLOW="B2C_1A_SIGNIN_AAD"
AZURE_AD_B2C_HOST_NAME=auth03coll.giustizia.it
AZURE_REDIRECT_URI=https://sdm-web-deskcp-01/
MISCROSOFT_TENANT_ID=792bc8b1-9088-4858-b830-2aad443e9f3f
AZURE_SCOPE_PORTALE_DESKCASSP=https://b2cmingiustiziaspidcoll.onmicrosoft.com/GiustiziaAPI/deskcassp
AZURE_SCOPE_FIRMA_REMOTA=https://b2cmingiustiziaspidcoll.onmicrosoft.com/GiustiziaAPI/firmaremota

#APP PREESERCIZIO
#AZURE_AD_B2C_CLIENT_ID="49a93774-5d55-46f7-be7f-95de295f921a"
#AZURE_AD_B2C_CLIENT_SECRET="****************************************"
#AZURE_REDIRECT_URI=https://web-deskcp-magistrato-pre/



#proxy forward giustizia
#http_proxy=http://servizitelematici:<EMAIL>
