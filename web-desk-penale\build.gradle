String packageJsonContent = new File(project.projectDir, 'package.json' ).getText( 'UTF-8' )
packageJsonContent = packageJsonContent.replaceAll('("version": ")((.)+)(",)', '"version": "' + project.version + (rcVersion ? ("-RC" + rcVersion) : "") + '",' )
new File(project.projectDir, 'package.json').write( packageJsonContent, 'UTF-8' )

task build {
    doLast {
		if (System.getProperty('os.name').toLowerCase(Locale.ROOT).contains('windows')) {
			exec {
				workingDir "$projectDir"
				commandLine 'cmd', '/c', 'yarn', 'install'
			}
			exec {
				workingDir "$projectDir"
				commandLine 'cmd', '/c', 'yarn', 'patch'
			}		
		} else {
			exec {
				workingDir "$projectDir"
				commandLine 'yarn', 'install'
			}
			exec {
				workingDir "$projectDir"
				commandLine 'yarn', 'patch'
			}
		}
		copy {
			from ".next/standalone"
			into buildDir
		}
		copy {
			from "public"
			into new File(buildDir, "public")
		}
		copy {
			from ".next/static"
			into new File(buildDir, ".next/static")
		}
    }
}

 task clean {
	delete "build", ".next", "__generated__"//, "node_modules"
}
 