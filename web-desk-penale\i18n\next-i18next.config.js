/**
 * Configurazione localizzazione
 */

const moment = require('moment');
const i18n = require('i18next');

/**
 * Import localizzazioni data/ora
 */
require('moment/locale/it');
// Importare altri locale, se servono
// import 'moment/locale/de';

/**
 * Configurazione loader backend per i file di localizzazione
 */
const ChainedBackend = require('i18next-chained-backend').default;
const HttpBackend = require('i18next-http-backend/cjs');
// Opzionalmente, cache su localstorage
const LocalStorageBackend = require('i18next-localstorage-backend').default;

/**
 * Localizzazioni supportate
 */
const defaultLocale = 'it';
const locales = ['it' /*, 'de'*/];

/**
 * Setta il locale di default per le date
 */
moment.locale(defaultLocale);

/**
 * Se cambia lingua, cambia locale di momentjs
 */
i18n.on('languageChanged', (lng) => {
  moment.locale(lng);
});

/**
 * Calcola il path dei file di traaduzione in caso di contextPath
 */
const translationPath =
  typeof window === 'undefined'
    ? require('path').resolve('./public/locales')
    : (process.env.NEXT_PUBLIC_BASE_PATH ?? '') + '/locales';

/**
 * Configurazione i18next
 */
module.exports = {
  backend: {
    backendOptions: [
      { expirationTime: 60 * 60 * 1000 },
      {
        loadPath: translationPath + '/{{lng}}/{{ns}}.json',
      },
    ], // 1 hour
    backends:
      typeof window !== 'undefined' ? [LocalStorageBackend, HttpBackend] : [],
    ns: ['translation'],
  },
  debug: process.env.NODE_ENV === 'development',
  i18n: {
    defaultLocale,
    locales,
  },
  localePath: translationPath,
  interpolation: {
    escapeValue: false, // Non fare l'escape delle stringhe, perchè lo gestisce già React
    // Formatta le date con momentjs
    format: (value, format, lng) => {
      moment.locale(lng);
      const parsed = moment(value);
      if (parsed.isValid()) {
        return parsed.format(format);
      }
      return value;
    },
  },
  defaultNS: 'translation',
  serializeConfig: false,
  use: typeof window !== 'undefined' ? [ChainedBackend] : [],
};
