const { i18n } = require('./i18n/next-i18next.config');
const packageJson = require('./package.json');

/** @type {import("next").NextConfig} */
module.exports = {
  // your custom config goes here

  /*   rewrites: () => {
    return [
      {
        source: '/graphql',
        destination: `${process.env.SERVIZI_PORTALE_URL}/graphql`,
      },
    ];
  }, */
  staticPageGenerationTimeout: 1000,
  reactStrictMode: false,
  output: 'standalone',
  assetPrefix: process.env.NEXT_PUBLIC_BASE_PATH || '',
  basePath: process.env.NEXT_PUBLIC_BASE_PATH || '',
  webpack: (config) => {
    // Configurazione webpack per permettere agli alias import sugli import di funzionare
    // Vedi: https://github.com/facebook/relay/issues/3272
    config.resolve.preferRelative = true;
    return config;
  },
  compiler: {
    relay: {
      src: '.',
      language: 'typescript', // or 'javascript`
      artifactDirectory: './__generated__/', // you can leave this undefined if you did not specify one in the `relay.json`
    },
  },
  typescript: {
    // !! DANGER !!
    // Impostando questo a true, si 'rilassano' gli errori bloccanti in fase di build
    // !! DANGER !!
    ignoreBuildErrors: false,
  },
  i18n,
  env: {
    PACKAGE_VERSION: packageJson.version,
  },
  experimental: {
    optimizePackageImports: [
        "@mui/icons-material",
        "@netservice/astrea-react-ds",
    ],
},
};
