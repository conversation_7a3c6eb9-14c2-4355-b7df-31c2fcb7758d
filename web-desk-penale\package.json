{"name": "desk-cass-penale", "version": "1.03.00", "description": "Desk Cassazione Penale", "contributors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "private": true, "scripts": {"postinstall": "patch-package", "dev": "relay-compiler && next dev", "build": "mkdirp __generated__ && relay-compiler && next build", "patch": "mkdirp __generated__ && relay-compiler && next build && patch-package && node update-version.js", "start": "set NODE_OPTIONS='--max-http-header-size=40960' && next start", "starttest": "node  ./src/configuration/server.js", "lint": "next lint", "lint:fix": "next lint --fix", "relay": "mkdirp __generated__ && relay-compiler", "update-version": "node update-version.js", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@azure/msal-browser": "^3.0.2", "@date-io/moment": "^2.16.1", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@fontsource/titillium-web": "^4.5.9", "@fullcalendar/core": "^6.1.5", "@fullcalendar/daygrid": "^6.1.5", "@fullcalendar/react": "^6.1.5", "@mui/icons-material": "^5.11.11", "@mui/material": "^5.11.12", "@mui/styles": "^5.11.12", "@mui/system": "^5.11.12", "@mui/x-date-pickers": "^6.0.0", "@netservice/astrea-react-ds": "2.9.0-b3", "@tanstack/react-query": "^5.67.1", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.13.5", "@tinymce/tinymce-react": "^4.3.0", "@types/node-fetch": "^2.6.9", "@types/react-big-calendar": "^1.6.4", "axios": "^1.3.4", "cookies": "^0.8.0", "dotenv": "^16.0.3", "eslint": "8.55.0", "eslint-config-next": "14.0.4", "graphql": "^16.6.0", "graphql-relay": "^0.10.0", "https-proxy-agent": "^7.0.1", "i18next": "^22.4.11", "i18next-browser-languagedetector": "^7.0.1", "i18next-chained-backend": "^4.2.0", "i18next-http-backend": "^2.1.1", "i18next-localstorage-backend": "^4.1.0", "moment": "^2.29.4", "next": "14.2.16", "next-auth": "^4.24.7", "next-i18next": "^13.2.1", "notistack": "^3.0.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "properties-reader": "^2.3.0", "raw-loader": "^4.0.2", "react": "18.2.0", "react-big-calendar": "^1.8.1", "react-dom": "18.2.0", "react-i18next": "^12.2.0", "react-dropzone": "^14.2.3", "relay-forms": "^2.0.0", "relay-hooks": "^8.0.0", "relay-runtime": "^14.1.0", "tinymce": "^6.4.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.1.4", "@types/node": "18.14.6", "@types/react": "^18.2.31", "@types/react-dom": "18.0.11", "@types/react-relay": "^14.1.3", "@types/relay-runtime": "^14.1.8", "cross-env": "^7.0.3", "csstype": "^3.1.1", "mkdirp": "^2.1.5", "relay-compiler": "^14.1.0", "relay-compiler-language-typescript": "^15.0.1", "relay-config": "^12.0.1", "tslib": "^2.5.0", "typescript": "4.9.5"}, "resolutions": {"@types/react": "^17.0.38"}}