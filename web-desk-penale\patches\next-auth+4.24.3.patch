diff --git a/node_modules/next-auth/core/lib/cookie.js b/node_modules/next-auth/core/lib/cookie.js
index 11a1648..2b868dc 100644
--- a/node_modules/next-auth/core/lib/cookie.js
+++ b/node_modules/next-auth/core/lib/cookie.js
@@ -21,7 +21,7 @@ function _checkPrivateRedeclaration(obj, privateCollection) { if (privateCollect
 function _classPrivateMethodGet(receiver, privateSet, fn) { if (!privateSet.has(receiver)) { throw new TypeError("attempted to get private field on non-instance"); } return fn; }
 
 const ALLOWED_COOKIE_SIZE = 4096;
-const ESTIMATED_EMPTY_COOKIE_SIZE = 163;
+const ESTIMATED_EMPTY_COOKIE_SIZE = 300;
 const CHUNK_SIZE = ALLOWED_COOKIE_SIZE - ESTIMATED_EMPTY_COOKIE_SIZE;
 
 function defaultCookies(useSecureCookies) {
