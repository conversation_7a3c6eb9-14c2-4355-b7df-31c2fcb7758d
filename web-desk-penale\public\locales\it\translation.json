{"server": {"errorCode": {"RICHIESTA_MODIFICA": "Errore nella richiesta. Modificare i parametri di ingresso", "CODE_TOKEN_EXPIRED": "Autenticazione scaduta, rieseguire il login", "CODE_UNAUTHORIZED": "Non si è autorizzati ad accedere alle funzionalità del ruolo - Contattare il CED", "genericError": "Errore generico - Contattare il CED", "traduzioneAssente": "Traduzione assente del codice: {{code}}", "CODE_UTENTE_SIC": "Utente non presente in anagrafica SIC Penale - Contattare il CED", "MAGI_SIC_NOT_FOUND": "Magistrato non presente in anagrafica SIC Penale - Contattare il CED", "CODE_MAGIS_SIC": "Verificare il profilo del Magistrato - Contattare il CED", "PROVV_NOT_CREATED": "Provvedimento non creato", "FASCICOLO_NOT_FOUND": "Fascicolo non trovato", "FIRMA_DEP_ERROR": "Errore durante la firma e il deposito", "FIRMA_REMOTA_NOT_ENABLED": "Utente non abilitato alla firma remota - Contattare il CED", "ERR_NETWORK": "Server non raggiungibile - Contattare il CED.", "CREDENZIALI_ERRATE": "Errore durante la firma: Verificare le credenziali", "PROVV_NOT_DUPLICATED": "Provvedimento non duplicabile o esiste già una bozza. Tornare al dettaglio del fascicolo", "ROLE_NOT_MANAGED": "Ruolo non ancora gestito", "TIPO_SENT_NOT_MANAGER": "Tipologia di provvedimento non gestito - Contattare il CED", "PROVV_NOT_FOUND": "Provvedimento non trovato", "DUPLICA_PROVV": "Provvedimento già duplicato", "SERVIZI_DEPOSITO_ERROR": "Servizi deposito non raggiungibile - Contattare il CED", "DEPOSITO_INTERNAL_ERROR": "Errore durante la firma. Verificare l'url del server e/o le credenziali di firma remota - Contattare il CED", "PROVV_GIA_FIRMATO_DEPOSITATO": "Provvedimento già firmato e depositato", "PROVV_LOCKED": "Già in uso", "LAVORATO": "Lavorato", "PDF_CORRUPTED_SIGN_ERROR": "NON LAVORATO", "FILE_NOT_FOUND": "File non trovato", "PROVV_LAVORATO": "Provvedimento già lavorato in un'altra pagina web, controlla il dettaglio fascicolo", "DEPOSITO_ERROR_CONNECTION": "Errore nel salvataggio della bozza, servizi deposito non raggiungibile - Contattare il CED", "CODA_DEPOSITO_NOT_FOUND": "Provvedimento già lavorato", "CODA_DEPOSITO_ALREADY_EXIST": "Il provvedimento è già presente nella coda di firma", "ID_DEPOSITO_NULL": "Errore nel deposito del provvedimento su servizi deposito - Contattare il CED", "OSCURAMENTO_MANDATORY": "<PERSON><PERSON>re, non si può procedere perché il file non è oscurato.", "RUOLO_NOT_DEFINED": "Ruolo dell'utente non determinato - Contattare il CED", "USER_NOT_PRESENT_SIGN_ERROR": "Utente non presente - Contattare il CED", "INCORRECT_PASSWORD_SIGN_ERROR": "Password errata - Contattare il CED", "INCORRECT_PIN_SIGN_ERROR": "Pin errato - Contattare il CED", "USER_BLOCKED_SIGN_ERROR": "Utente bloccato - Contattare il CED", "INVALID_JWT_TOKEN_SIGN_ERROR": "Autenticazione ADN fallita. Riprovare dopo aver eseguito il logout e in caso di nuovo errore contattare il CED", "UDIENZA_ERROR": "Udienza non trovata - Contattare il CED"}}, "modal": {"close": "<PERSON><PERSON>", "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "proceed": "<PERSON><PERSON><PERSON>"}}, "common": {"filtra": "Filtra", "filtraPer": "FILTRA PER", "applicaFiltri": "Applica", "dateFormat": "{{date, DD MMMM YYYY}}", "dateTemplate": "{{date, DD/MM/YYYY}}", "campiObbligatori": "I campi con * sono obbligatori", "datetimeFormat": "{{date, DD MMMM YYYY HH:mm}}", "datetimeFormatShort": "{{date, DD/MM/YYYY HH:mm}}", "yes": "Sì", "no": "No", "submit": "Invia", "note": "Note", "reset": "Reset", "oscuramentoSic": "O<PERSON><PERSON><PERSON> dal sic", "error": {"title": "Si è verificato un errore imprevisto", "content": "È possibile scaricare un report dell'errore, o tornare alla pagina precedente", "downloadReport": "Scarica report", "goBack": "Torna indietro", "name": "Codice Errore: ", "retry": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "data": "Data", "info": "Info", "collegio": "Collegio", "reato": "<PERSON><PERSON>", "stato": "Stato", "valorePonderale": "<PERSON><PERSON>", "parti": "Parti", "creaNuovo": "<PERSON><PERSON>", "tipologia": "Tipologia", "reati": "Reati", "autore": "Autore", "estensore": "Estensore", "statoProvvedimento": "Stato Provvedimento", "statoBusta": "Stato", "ultimaModifica": "Ultima Modifica", "oscuramento": "Oscuramento", "azioni": "Azioni", "homepage": "Homepage", "chiudi": "<PERSON><PERSON>", "forward": "<PERSON><PERSON>", "back": "Indietro", "nomeCognome": "Nome e Cognome", "cognome": "Cognome", "ruolo": "<PERSON><PERSON><PERSON>", "vedi": "<PERSON><PERSON><PERSON>", "principale": "<PERSON><PERSON>", "vediTutte": "<PERSON>edi tutte", "vediTutti": "<PERSON><PERSON>i tutti", "scrivania": "Scrivania", "selezionaOpzione": "Seleziona un'opzione", "indietro": "Indietro", "warning": "Attenzione!", "annulla": "<PERSON><PERSON><PERSON>"}, "notifiche": {"segnalazioni": "Segnalazioni", "nessunaNotifica": "Nessuna notifica", "cerca": "Cerca", "segnaComeLette": "SEGNA COME LETTE", "deselezionaTutte": "DESELEZIONA TUTTE", "selezionaTutte": "SELEZIONA TUTTE", "mostraAltro": "Mostra altro", "notificheCard": {"udienza": "Udienza", "visualizzaFascicolo": "Visualizza fascicolo", "ore": "ore"}}, "login": {"desk": "DESK", "cassazionePenale": "CASSAZIONE PENALE", "login": "LOGIN", "accedi": "Accedi", "reindirizzamento": "<PERSON>lic<PERSON>do <PERSON> reindirizzato al servizio di autenticazione"}, "meta": {"title": "Desk Penale Corte Suprema di Cassazione", "description": "Template single page application"}, "header": {"labels": {"title": "Desk Penale Corte Suprema di Cassazione"}, "userMenu": "Menu utente", "menu": "<PERSON><PERSON>", "notifications": "Notifiche", "openNotifications": "Apri notifiche", "closeNotifications": "<PERSON><PERSON> not<PERSON>", "account": "Account", "openAccount": "Apri account", "accountMenu": {"impostazioni": "Impostazioni", "info": "Info", "logout": "Logout"}, "links": {"dashboard": "Dashboard", "ricerca": "Ricerca", "anagrafica": "Anagrafica", "scrivania": "Minute Da Verificare"}}, "scrivania": {"oscuratoSic": "Oscuramento Sic:", "oscuratoDeskCSP": "Oscuramento Desk/CSP:", "visualizzaLista": "Visualizza Lista", "scrivania": "Scrivania", "filtriImpostati": "Fi<PERSON>ri impostati", "udienzeConMinuteTelematicheNuove": "Solo udienze con minute telematiche nuove", "soloUdienzeConAlmenoUnProvvNonPubblicato": "Solo udienze con almeno un provvedimento non pubblicato", "dataUdienza": "Data udienza", "sezione": "Sezione", "tipoUdienza": "Tipo <PERSON>", "collegio": "Collegio", "chiusa": "CHIUSA", "del": "del", "tutteLeSezioni": "<PERSON>tte le sezioni", "tutte": "<PERSON><PERSON>", "mostraTutti": "<PERSON><PERSON> tutti", "minuteDaVerificare": "Minute da verificare", "nuoveMinute": "Nuove minute da visualizzare!", "tooltip": {"minutaNuova": "<PERSON><PERSON>", "minutaRevisionata": "Minuta Revisionata"}, "udienza": "Udienza", "ricorsiTotali": "Ricorsi Totali", "riuniti": "(di cui) Riuniti", "totaleProvvPubb": "Totale provvedimenti pubblicati", "minuteCartacee": "Minute cartacee", "datiTelematici": "<PERSON><PERSON>ema<PERSON>", "minuteTelNuove": "Minute Telematiche Nuove", "richiestaModifica": "Richiesta Modifica", "bozzaMinutamodificata": "Bozza Minuta Modificata", "minutaModificataInoltrataEstensore": " Minuta modificata e inoltrata all'Estensore", "busteRifiutate": "Buste rifiutate", "provvInviatiCancelleria": "Provv. Inviati in cancelleria", "provvCodaDeposito": "Provv. in Coda di deposito", "provvPubblicati": "Provv. <PERSON><PERSON><PERSON>", "codaDeposito": "Coda di deposito", "procediFirma": "Procedi alla firma e al deposito", "firmaDeposita": "Firma e deposita", "firmaDepositoMassivo": "Firma e deposito massivo della coda di deposito", "notaFirmaDepositoMassivo": "Verifica tutte le minute in coda di deposito per poter effetuare la firma ed il deposito massivo", "nessunDeposito": "<PERSON><PERSON><PERSON>o", "esciDallaCoda": "Esci dalla coda di deposito", "scaricaTutti": "Scarica tutti i provvedimenti", "scarica": "Scarica provvedimento", "minuteUdienza": {"minute": "Minute"}, "richiediModificaModal": {"minutePerUdienza": "Minute per udienza.", "categoriaDellaModifica": "Categoria della modifica", "specificheDellaRichiestaDiModifica": "Specifiche della richiesta di modifica", "annulla": "<PERSON><PERSON><PERSON>", "inviaRichiesta": "<PERSON><PERSON>", "richiestaDiModificaInviataConSuccesso": "Richiesta di modifica inviata con successo"}, "inviaMinutaModificataModal": {"inviaMinutaModificata": "Invia minuta modificata", "annulla": "<PERSON><PERSON><PERSON>", "invia": "Invia", "minutaInviataConSuccesso": "Minuta inviata con successo", "specificheDelleModificheApportate": "Specifiche delle modifiche apportate"}, "udienzaDatiHeader": {"datiUdienza": "<PERSON><PERSON>", "collegio": "Collegio", "termineDeposito": "<PERSON><PERSON><PERSON>", "giorniDaOggi": "giorni da oggi", "giorni": "<PERSON>ior<PERSON>", "valorePonderale": "<PERSON><PERSON>:", "valorePonderaleComplessivo": "Valore Ponderale Complessivo", "valPondComplessivoTooltipTitle": "Il valore complessivo indica la somma dei valori ponderali di tutti i fascicoli riuniti"}, "udienzaDatiCalendarHeader": {"datiUdienza": "<PERSON><PERSON>", "collegio": "Collegio", "termineDeposito": "<PERSON><PERSON><PERSON>", "giorniDaOggi": "giorni da oggi", "giorni": "<PERSON>ior<PERSON>", "valorePonderale": "<PERSON><PERSON>:", "valorePonderaleComplessivo": "Valore Ponderale Complessivo"}, "filesUdienza": {"apri": "APRI"}, "pdfPreview": {"verificato": "Verificato", "richiediModifica": "Richiedi Modifica", "previous": "Precedente", "next": "Successivo", "verificatiTutti": "Verificati tutti", "MinutaSuccessiva": "Minuta successiva", "MinutaPrecedente": "Minuta precedente", "modifica": "Modifica"}, "verificaEditor": {"verificaOrichiedi": "Verifica o richiedi modifica"}, "modifyUdienza": {"minuteRichiestaModifica": "Minute richiesta modifica"}, "verificaProvvedimento": {"inBozza": "<PERSON><PERSON>", "inviatoInCancelleria": "Inviato in cancelleria", "minutaPervenuta": "Minuta pervenuta", "bustaRifiutata": "<PERSON>ta rifiutata", "bustaRifiutataAlPresidente": "Busta rifiutata al Presidente", "miinutaAccettata": "Minuta accettata", "minutaDaModificare": "<PERSON><PERSON> modifica", "minutaModificata": "Minuta modificata", "inviatoInCancelDalPresidente": "Inviata in Cancelleria dal Presidente", "provvedimentoDepositato": "Provvedimento depositato", "bozzaMinutamodificata": "Bozza Minuta Modificata", "minutaModificataInoltrataEstensore": "Minuta modificata e inoltrata all'Estensore", "pubblicato": "Pubblicato", "inCodaDiFirma": "In coda di firma", "tutti": "<PERSON><PERSON>", "minutaDiSentenza": "Minuta di Sentenza", "minutaDiOrdinanza": "Minuta di ordinanza", "sentenza": "Sentenza", "ordinanza": "Ordinanza", "verificaSelezionati": "Verifica selezionati"}}, "fascicolo": {"calendarioUdienza": "Calendario Udienze e Scadenzario", "fascicolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datiUdienza": "<PERSON><PERSON>", "fascicoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fascicoliRiuniti": "Fascicoli riuniti", "termineDeposito": "<PERSON><PERSON><PERSON>", "datiPrincipali": "<PERSON><PERSON>", "provvedimenti": "Provvedimenti", "importa": "IMPORTA", "salvaInBozza": "Salva in bozza", "uploadAvviso": "Attenzione, non sarà possibile procedere online alla sottolineatura-oscuramento per i documenti importati", "firmaDeposita": "Procedi alla Firma e al Deposito", "annulla": "<PERSON><PERSON><PERSON>", "tipologiaProvvedimento": "Tipologia Provvedimento", "relazionatoDa": "Relazionato da", "tipoRicorso": "Tipo Ricorso", "dataIscrizione": "Data iscrizione", "provvedimentoImpugnato": "Provvedimento impugnato", "redazioneOnline": {"tipologiaProvvedimento": "Tipologia Provvedimento", "motivazioneSemplificata": "Motivazione semplificata", "chiudi": "CHIUDI", "conferma": "CONFERMA"}, "datiFascicolo": {"titoloMascheraDatiFascicolo": "<PERSON><PERSON>", "datiRiuniti": "<PERSON><PERSON>", "riunitoAl": "Riunito al ", "downloadprovvedimento": "Download provvedimento", "downloadprovvedimentoOscurato": "Download provvedimento oscurato", "visualizzaProvvedimento": "Visualizza provvedimento", "visualizzaProvvedimentoOscurato": "Visualizza provvedimento oscurato"}, "detailFascicolo": {"esitoParzialeSenzaProvvedimento": "Esito parziale senza provvedimento", "esitoSenzaProvvedimento": "Esito senza provvedimento", "parziale": "Parziale", "sospesoInUdienza": "Sospeso in udienza", "redazioneNuovoProvvedimento": "Redazione nuovo provvedimento", "importaDocumento": "Importa Documento", "collegioUdienza": "Collegio Udienza", "calendarioUdienze": "Calendario udienze", "udienza": "Udienza:", "fascicolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubblicato": "Pubblicato", "pubblicatoSic": "Pubblicato da SIC", "provvDepositatoSic": "Provvedimento depositato da SIC", "depositatoSic": "Minuta depositata da SIC", "provvedimenti": "Provvedimenti", "provvedimentoRedattoEstensore": "Il provvedimento verrà redatto dall'estensore.", "provvedimentoParziale": "Non è possibile redigere il provvedimento per il fascicolo n: ", "provvedimentoSubordinato": "Non è possibile redigere un provvedimento autonomo per il ricorso riunito.", "motivo": "Motivo:", "descrizione": "Descrizione:"}, "datiProvvedimento": {"mainTitle": "<PERSON><PERSON>", "oscuratoSic": "Oscuramento Sic:", "oscuratoDeskCSP": "Oscuramento Desk/CSP:"}, "provvedimentoDepositato": {"provvedimentoDepositato": "Provvedimento Depositato", "tornaAlCalendarioUdienze": "Torna al Calendario Udienze", "visualizzaDettaglioFascicolo": "Visualizza Dettaglio F<PERSON>lo"}, "detailFascicoloPresidente": {"scrivania": "Scrivania", "udienza": "Udienza", "collegio": "Collegio", "del": "del", "fascicolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "provvedimenti": "Provvedimenti", "redazioneNuovoProvvedimento": "Redazione nuovo provvedimento", "importaDocumento": "Importa documento"}, "provvedimentiTablePresidente": {"modificaProvvedimento": "Modifica", "depositaMinuta": "Deposita minuta", "scaricaProvvedimento": "Scarica", "scarica": "Scarica", "scaricaProvvedimentoOscurato": "Scarica oscurato", "eliminaProvvedimento": "Elimina", "caricaProvvedimento": "Carica", "visualizzaProvvedimento": "Visualizza", "visualizza": "Visualizza", "statoProvvedimento": "Stato", "noteProvvedimento": "Note", "statoProvvedimentoFascicolo": "Stato provvedimento fascicolo", "provvedimentoEliminato": "Provvedimento eliminato con successo", "downloadOK": "Download del pdf avvenuto con successo"}, "provvedimentiTable": {"modificaProvvedimento": "Modifica", "depositaMinuta": "Deposita minuta", "scaricaProvvedimento": "Scarica", "scarica": "Scarica", "scaricaProvvedimentoOscurato": "Scarica oscurato", "eliminaProvvedimento": "Elimina", "caricaProvvedimento": "Carica", "statoBusta": "Stato", "noteProvvedimento": "Note", "statoProvvedimentoFascicolo": "Stato provvedimento fascicolo", "importaProvvedimento": "Importa", "provvedimentoEliminato": "Provvedimento eliminato con successo", "downloadOK": "Download del pdf avvenuto con successo", "inserisciInCodaDiFirma": "Inserisci in coda di firma", "aggiuntoAllaCodaDiDeposito": "Provvedimento aggiunto correttamente alla coda di deposito"}, "firmaDeposito": {"firmaEDepositoMinuta": "Firma e deposito", "fascicolo": "fascicolo", "annulla": "<PERSON><PERSON><PERSON>", "firmaEDeposita": "Firma e deposita", "firmaEDepositaIlProvvedimento": "Firma e deposita il provvedimento", "conformitaVerificata": "Conformità verificata tra il provvedimento (pdf) e i dati strutturati (xml)", "inserisciInCodaDiFirma": "<PERSON><PERSON> in coda di firma"}, "_firmaDeposita": {"firmaEDepositoMinuta": "Firma e deposito", "fascicolo": "fascicolo", "annulla": "<PERSON><PERSON><PERSON>", "firmaEDeposita": "Firma e deposita", "otp": "OTP", "password": "Password", "username": "Username", "otpNonInserito": "OTP non inserito!", "firmaEDepositoOK": "Firma e deposito effettuati con successo!", "firmaEDepositoKO": "Errore durante la firma.", "datiObbligatoriNonInseriti": "Dati obbligatori non inseriti"}, "fileBusta": {"oscurato": "OSCURATO", "nonOscurato": "NON OSCURATO", "downloadDelFile": "Download del file", "avvenutoConSuccesso": "avvenuto con successo"}}, "provvedimenti": {"verificaProvvedimenti": "Verifica Provvedimenti", "motivoRichiestaModifica": "RICHIESTA DI MODIFICA DAL PRESIDENTE", "motivoRifiuto": "MOTIVO DEL RIFIUTO", "bustaAccettate": "BUSTA ACCETTATA", "createProvvCoda": "Provvedimento creato e inserito in coda di deposito"}, "provvedimento": {"importaProvvedimenti": {"firmaEDeposita": "Firma e deposita", "nessunProvvedimentoImportato": "Nessun provvedimento importato"}}, "impostazioni": {"impostazioniEliminate": "Eliminazione delle impostazioni completata", "impostazioniInserite": "Inserimento delle impostazioni completato", "modificaImpostazioni": "Modifica delle impostazioni completato", "credenzialiFirmaRemota": "Credenziali di Firma Remota", "email": "Email:", "ruolo": "Ruolo:", "elimina": "Elimina", "conferma": "Conferma", "password": "Password", "nomeUtente": "Nome utente:", "impostazioniUtenteCollegato": "Impostazioni utente collegato"}, "calendario": {"esitoUdienza": "Esito Udienza", "ordine": "<PERSON><PERSON>", "nrg": "NRG", "parti": "Parti", "valore": "<PERSON><PERSON>", "stato": "Stato", "oscuramentoSic": "Oscuramento Sic", "oscuramentoDeskCSP": "Oscuramento Desk/CSP", "provvedimenti": "Provvedimenti", "azioni": "Azioni", "reato": "Reati", "semplificata": "Semplif.", "valorePonderale": "Val. Pon.", "tipologia": "Tipologia Provv.", "dataUdienza": "Data Udienza", "dataDeposito": "Data", "estensore": "Estensore", "verificato": "Verificato", "calendarioTable": {"riunitoAl": "(R) - Riuni<PERSON> al ", "visualizzaFascicolo": "Visualizza fascicolo", "downloadIntestazione": "Download Intestazione", "intestazioneScaricata": "Intestazione scaricata con successo", "erroreIntestazioneScaricata": "Errore nel download dell'intestazione. Verificare la presenza del Presidente nel collegio"}, "datiGeneraliUdienza": {"depositatoDaSic": "Provvedimento depositato da Sic", "inviatoInCancelDalPresidente": "Inviata in cancelleria dal Presidente", "minutaDaModificare": "<PERSON><PERSON> modifica", "minutaAccettataInviataAlPresidente": "Minuta accettata e inviata al Presidente", "inCodaFirmaRelatore": "In coda di firma", "bustaRifiutata": "<PERSON>ta rifiutata", "bustaRifiutataAlPresidente": "Busta rifiutata al Presidente", "inviatoInCancelleria": "Inviato in cancelleria", "bozzaMinutamodificata": "Bozza Minuta Modificata", "minutaRevisione": "Minuta in revisione", "minutaModificataInoltrataEstensore": "Minuta modificata e inoltrata all'Estensore", "minutaModificataDalPresidente": "Minuta modificata dal <PERSON>e", "minutaDepositataDaSic": "Minuta depositata da Sic", "inBozza": "In bozza", "daRedigere": "Da redigere", "pubblicato": "Pubblicato", "pubblicatoDaSic": "Pubblicato da Sic", "inRedazioneEstensore": "In redazione all'estensore", "tutti": "<PERSON><PERSON>", "udienza": "Udienza", "del": "del", "listaFascicoli": "Lista Fascicoli", "numeroFascicoli": "Numero Fascicoli", "valorePonderaleTotale": "Valore Ponderale Totale", "statoProvvedimento": "Stato Provvedimento", "scadenze": "SCADENZE"}, "detailReati": {"visualizzaReati": "Visualizzazione reati", "reato": "<PERSON><PERSON>"}, "detailStato": {"provvedimentoDaRedigere": "Provved<PERSON><PERSON> da redigere"}, "mainCalendar": {"mese": "Mese", "lista": "Lista", "giorno": "<PERSON><PERSON><PERSON>"}, "trackingStato": {"minutaDaRedigere": "<PERSON><PERSON> da red<PERSON>e"}, "uploadProvvedimento": {"inserisciInCodaDiFirma": "<PERSON><PERSON> in coda di firma", "motivazioneSemplificata": "Motivazione semplificata", "fileCaricatoConSuccesso": "File caricato con successo", "conversionFailed": "Errore durante la conversione del file", "uploadFailed": "Errore durante il caricamento del file", "fileNonValido": "Il formato del file selezionato non è supportato. Si prega di utilizzare file PDF o DOCX"}, "uploadProvvedimentoDuplicate": {"provvedimentoDuplicatoConSuccesso": "Provvedimento duplicato con successo", "motivazioneSemplificata": "Motivazione semplificata", "fileCaricatoConSuccesso": "File caricato con successo"}, "intestazioni": {"calendarioUdienze": "CALENDARIO UDIENZE"}, "calendarTabs": {"chiusa": "CHIUSA"}}, "intestazioni": {"nrg": "NRG", "parti": "Parti", "valore": "Valore", "stato": "Stato Provvedimento", "reato": "<PERSON><PERSON>", "oscuramento": "Oscuramento", "valorePonderale": "<PERSON><PERSON>:", "tipologia": "Tipologia", "scaricaintestazioni": "Scarica Intestazioni"}, "deposito": {"depositoMassivo": {"calendario": "Calendario", "scrivania": "Scrivania", "firmaDepositoMassivo": "Firma e deposito massivo della coda di deposito", "firmaEDepositoMassivo": "Firma e deposito massivo", "notaFirmaDepositoMassivo": "Verifica tutti i provvedimenti in coda di deposito per poter effetuare la firma ed il deposito massivo", "conformitaVerificataMassivo": "Conformità verificata tra tutti i dati atto (xml) e i provvedimenti (pdf) in coda", "esciDallaCoda": "Esci dalla coda di deposito", "scaricaTutti": "Scarica tutti i provvedimenti", "nessunDeposito": "<PERSON><PERSON><PERSON>o", "firmaDeposita": "Firma e deposita", "eliminaDepositoCompletato": "Eliminazione deposito completata", "downloadOK": "Download avvenuto con successo!", "codaDiFirmaScaduta": "Lo stato della coda di firma è scaduto", "firmaEDepositoOK": "Firma e deposito effettuati con successo!", "firmaEDepositoCompletato": "Firma e deposito effettuati con successo", "downloadCompletato": "Download dei file pdf e xml avvenuto con successo"}, "codaDeposito": {"codaDiDeposito": "Coda di deposito", "ordine": "<PERSON><PERSON>", "nrg": "NRG", "udienza": "Udienza", "azioni": "Azioni", "procediFirma": "Procedi alla firma e al deposito"}, "fileBustaDeposito": {"downloadDelFile": "Download del file", "avvenutoConSuccesso": "avvenuto con succcesso", "erroreNelDownload": "Errore nel download del file", "oscurato": "OSCURATO", "nonOscurato": "NON OSCURATO", "anteprima": "Anteprima", "downloadPDF": "Download PDF"}, "minutaDiOrdinanza": {"erroreNelRecuperoFile": "Errore nel recupero dei file associati", "downloadDelFile": "Download del file", "avvenutoConSuccesso": "avvenuto con succcesso", "erroreNelDownload": "Errore nel download del file", "udienza": "Udienza", "fascicolo": "fascicolo", "scarica": "Scarica provvedimento", "conformitaVerificata": "Conformità verificata tra il provvedimento (pdf) e i dati\nstrutturati (xml)", "downloadCompletato": "Download dei file pdf e xml avvenuto con successo", "tooltipSuccess": "Provvedimento firmato e depositato correttamente", "tooltipWarning": "Provvedimento saltato durante il deposito massivo. Verificare e riprovare singolarmente se necessario", "tooltipError": "Verificare che il provvedimento non sia in lavorazione in altra tab del browser e riprovare fra poco", "tooltipUndefined": "Stato non definito", "tooltipPdfCorrupted": "Verificare integrità dei file allegati del deposito - Contattare il CED"}}, "buttonsLabel": {"conferma": "CONFERMA", "annulla": "ANNULLA", "caricaDaFile": "CARICA DA LOCALE"}, "editor": {"strutturaEditor": {"aggiuntoAllaCodaDiDeposito": "Provvedimento aggiunto correttamente alla coda di deposito", "introduzione": "INTRODUZIONE", "ritenutoInFatto": "RITENUTO IN FATTO", "consideratoInDiritto": "CONSIDERATO IN DIRITTO", "PQM": "P.Q.M."}, "editorLibero": {"attenzionePerDepositareIlFileWord": "Attenzione! Per depositare il file word che si sta scaricando in locale sarà necessario ricaricarlo", "chiudi": "CHIUDI", "download": "Procedi con il download", "provvedimentoCreatoConSuccesso": "Provvedimento creato con successo", "cosiEDeciso": "Così deciso il", "indietro": "Torna indietro", "downloadWord": "Download Word", "firmaEDeposito": "Procedi alla firma e al deposito", "sottolineatura": "Procedi con sottolineatura", "mettiInCodaDiFirma": "<PERSON><PERSON> in coda di firma", "inviaMinutaModificata": "Invia minuta modificata"}}, "form": {"buttons": {"submit": "Invia", "reset": "Can<PERSON><PERSON>", "annulla": "<PERSON><PERSON><PERSON>", "conferma": "Conferma", "mostra": "Mostra", "nascondi": "Nascondi"}}, "dragDrop": {"noFile": "Non è stato caricato alcun file", "uploadedFile": "", "labels": {"textDrag": "Trascina i documenti all'interno dell'area tratteggiata o carica i file da locale tramite l'apposito bottone", "rilasciaFile": "Rilascia il file", "loadFromFile": "Carica da locale"}}, "errors": {"tipoAllegatoRequired": "Specificare la tipologia dell'allegato", "nessunaUdienza": "Nessuna udienza presente come Presidente di Collegio"}, "parti": {"parte": "Parte", "avvocati": "Avvocati"}, "shared": {"codaButton": {"codaDiDeposito": "Coda di deposito"}, "conferma": {"conferma": "Conferma", "prosegui": "<PERSON><PERSON><PERSON>", "annulla": "<PERSON><PERSON><PERSON>"}, "datiPrincipali": {"datiPrincipali": "<PERSON><PERSON>", "nrg": "NRG", "calendarioUdienza": "Calendario Udienze e Scadenzario"}, "datiUdienza": {"datiUdienza": "<PERSON><PERSON>", "termineDeposito": "<PERSON><PERSON><PERSON>", "visualizzaUdienza": "Visualizza Udienza"}, "editorModal": {"inviaMinutaModificata": "Invia minuta modificata", "mettiInCodaDiFirma": "<PERSON><PERSON> in coda di firma", "procediConSottolineatura": "Procedi con sottolineatura", "procediAllaFirmaDelDeposito": "Procedi alla firma e al deposito", "downloadWord": "Download Word", "tornaIndietro": "Torna indietro", "download": "Procedi con il download", "chiudi": "CHIUDI", "depositoFileWord": "Attenzione! Per depositare il file word che si sta scaricando in locale sarà necessario ricaricarlo", "confermaPresaInCaricoFascicolo": "Conferma presa in carico fascicolo"}}, "spinner": {"loading": "Caricamento in corso..."}, "pages": {"custom401": {"401": "401", "messageOf401": "Non autorizzato ", "deskCassazionePenale1": "L'utente non è autorizzato ad accedere alle funzionalità dell'applicativo. ", "deskCassazionePenale2": "Per procedere è necessario verificare di avere le autorizzazioni correttamente configurate nell'ADN di Giustizia. ", "deskCassazionePenale3": "Per maggiori informazioni contattare il CED. ", "goToLoginPage": "Vai alla pagina di login"}, "custom403": {"403": "403", "messageOf403": "Vietato ", "deskCassazionePenale1": "L'utente autenticato sull'ADN di Giustizia non è autorizzato ad accedere alle funzionalità dell'applicativo. ", "deskCassazionePenale2": "Per procedere è necessario aver prima completato le procedure di autorizzazione al Desk Cassazione Penale. ", "deskCassazionePenale3": "Per maggiori informazioni contattare il CED. ", "goToLoginPage": "Vai alla pagina di login"}, "custom404": {"404": "404", "paginaORisorsaNonTrovata": "Pagina o risorsa non trovata", "indirizzoWebCorretto": "Quando si digita l'indirizzo web, verificare che sia corretto.", "seIndirizzoIncollatoControllalo": "Se avete incollato l'indirizzo web, controllate che l'intero\nindirizzo sia corretto.", "erroreAncoraPresenteContattaHelpDesk": "Se l'indirizzo web è corretto ma l'errore è ancora visibile, \n contattare l'help desk.", "tornaAllaPaginaPrecedente": "Torna alla pagina precedente"}, "custom500": {"erroreInternoAlServer": "Errore interno al server", "indirizzoWebCorretto": "Quando si digita l'indirizzo web, verificare che sia corretto.", "seIndirizzoIncollatoControllalo": "Se avete incollato l'indirizzo web, controllate che l'intero\nindirizzo sia corretto.", "erroreAncoraPresenteContattaHelpDesk": "Se l'indirizzo web è corretto ma l'errore è ancora visibile, \n contattare l'help desk.", "tornaAllaPaginaPrecedente": "Torna alla pagina precedente"}, "custom503": {"servizioNonDisponibile": "Servizio non disponibile", "messageOf503": "Il server non è temporaneamente in grado di soddisfare la richiesta a causa di un'interruzione della manutenzione o di problemi di capacità. Si prega di riprovare più tardi.", "contattareCED": "Contattare il CED"}, "scrivania": {"index": {"nessunaUdienzaPresenteComePresidenteDiCollegio": "Nessuna udienza presente come Presidente di Collegio"}}, "libero": {"bozzaSalvata": "Bozza salvata con successo", "saltaSottolineaturaDati": "Salta la sottolineatura dei dati", "provvedimentoNonOscurato": "Provvedimento non oscurato", "confermaProseguiSenzaSott": "Attenzione non sarà possibile procedere online alla sottolineatura-oscuramento per i documenti importati", "confermaProseguiSenzaSottEditor": "Confermi di voler proseguire senza predisporre il provvedimento oscurato?", "redazioneFascicolo": "Redazione fascicolo", "udienzaChiusa": "UDIENZA CHIUSA", "strutturata": "Stru<PERSON><PERSON><PERSON>", "testoLibero": "Testo Li<PERSON>o", "oscuramentoDati": "Oscuramenti dati", "si": "Si", "no": "No", "avvisoOmissioneGeneralita": "L'avviso per l'omissione di generalità verrà inserito in automatico dal sistema", "avvisoOscuramentoDati": "Avviso oscuramento dati", "cosiEDeciso": "Così deciso il", "epigrafe": "Epigrafe", "confermaProseguiConEvidenziaPerPresidente": "Sei sicuro di voler procedere senza modificare il colore del testo?", "confermaProseguiSenzaEvidenziaForEstensore": "Sei sicuro di voler procedere senza unificare il colore del testo?", "confermaAnnullaEElimina": "Sei sicuro di voler annullare la bozza di modifica?", "provvedimentoEliminatoBozza": "Bozza eliminata con successo"}}, "info": {"versioni": "Versioni", "noErrors": "<PERSON><PERSON><PERSON> errore trovato", "downloadErrors": "Scarica log console web", "errorsDownloaded": "Log console web scaricata correttamente", "errorDownloadingErrors": "Errore nello scaricamento del log console web"}}