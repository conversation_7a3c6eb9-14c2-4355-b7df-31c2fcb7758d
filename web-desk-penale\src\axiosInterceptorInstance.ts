import { useNotifier } from './utils/NsNotifier';
import axios from "axios";
import { useSession } from "next-auth/react";
import { useTranslation } from "react-i18next";
import useDebouncedCallback from '@restart/hooks/useDebouncedCallback';
import { errorLogger } from './utils/errorLogger';

export const AxiosInteceptor = ()=>{
  const { data: session, update } = useSession();
  const { t, i18n} = useTranslation();
  const { notify } = useNotifier();
  const notificationUseDebounce = useDebouncedCallback(
    (value: { message: string; type: string }) => {
      // Hit the API
      notify({
        message: value.message,
        type: value.type as "default" | "error" | "success" | "warning" | "info"
      });
    },
    1000
  );

  const Traduzione = (code: string, typeError: string) => {
    let key = 'server.errorCode.' + code;
    if (!i18n.exists(key)) {
      key = code;
    } else {
      key = t(key);
    }
    console.log(`code.${code}`);
    notificationUseDebounce({
      message: t(key),
      type: typeError || 'error',
    });
  };

  const storeErrors = (errorMessage: string, query?: string) => {
    const storedErrors = localStorage.getItem('errors');
    const errors = storedErrors ? JSON.parse(storedErrors) : [];
    errors.push({
      message: errorMessage || 'Unknown error',
      graphql: query,
      timestamp: new Date().toISOString(),
    });
    localStorage.setItem('errors', JSON.stringify(errors));
  };

  axios.interceptors.request.use(
    async (config) => {
      // Refresh token solo se mancano meno di 5 minuti alla scadenza
      const REFRESH_THRESHOLD = 5 * 60 * 1000;
      
      if (typeof session?.accessTokenExpires === 'number') {
        const timeUntilExpiry = session.accessTokenExpires - Date.now();
        
        // Refresh solo se il token sta per scadere e non è già in corso un refresh
        if (timeUntilExpiry < REFRESH_THRESHOLD && !config.headers['X-Token-Refresh-In-Progress']) {
          config.headers['X-Token-Refresh-In-Progress'] = 'true';
          const sessionUpdated = await update({ ...session });
          if (sessionUpdated) {
            session.accessToken = sessionUpdated.accessToken;
          }
          delete config.headers['X-Token-Refresh-In-Progress'];
        }
      }

      if (session?.accessToken) {
        config.headers['Authorization'] = 'Bearer ' + session.accessToken;
      }

      return config;
    },
    (error) => {
      Promise.reject(error);
    }
  );

  axios.interceptors.response.use(
    (response) => {
      // Do something with response data
      if (response?.data.errors?.length > 0) {
        const error = response?.data.errors[0];
        if (error?.extensions?.statusError) {
          if (error?.extensions?.statusError === 433) {
            Traduzione(
              error?.extensions?.errorCode,
              error?.extensions?.typeError || 'error'
            );
          }
        } else if (error && error.statusError === 433) {
          Traduzione(error?.errorCode, error?.typeError || 'error');
        }
      }
      return response;
    },
    (error) => {
      errorLogger.logNetworkError(error);
        storeErrors(error?.response?.data?.message, error?.config?.data);
        
      if (error?.response?.status === 401) {
          if (error?.response?.data?.message?.startsWith('CODE_')) {
          Traduzione(
            error?.response?.data?.message,
            error?.response?.data?.typeError
          );
          console.error(
            `Errore lato server: ${error.response.status.message}, codeError:${error.response.status.errorCode}`
          );
        } else {
          notificationUseDebounce({
            message:
              error?.response?.data?.message ||
              t('server.errorCode.genericError'),
            type: error?.response?.data?.typeError || 'error',
          });
        }
      } else if (error?.response?.status === 433) {
        if(error?.response?.data?.errorCode === 'FIRMA_ERROR'){
          console.log(error?.response?.data)
        } else {
          Traduzione(
            error?.response?.data?.errorCode,
            error?.response?.data?.typeError
          );
        }
        console.error(
          `Errore lato server: ${error.response.status.message}, codeError:${error.response.status.errorCode}`
        );
      } else {
        if (error?.response) {
          if( error?.response?.data?.message === 'DEPOSITO_ERROR_CONNECTION' ||
              error?.response?.data?.message === 'DEPOSITO_INTERNAL_ERROR' ||
              error?.response?.data?.message === 'ID_DEPOSITO_NULL' ) {
            Traduzione(
              error?.response?.data?.message,
              error?.response?.data?.typeError
            );
          } if(error?.response?.data?.errorCode === 'FIRMA_ERROR'){
            console.log(error?.response?.data)
          } else {
            notificationUseDebounce({
              message:
                error?.response?.data?.message ||
                t('server.errorCode.genericError'),
              type: error?.response?.data?.typeError || 'error',
            });
          }
        } else if (error?.code === 'ERR_NETWORK') {
          notificationUseDebounce({
            message: t('server.errorCode.ERR_NETWORK'),
            type: 'error',
          });
        }
      }
      return Promise.reject(error);
    }
  );

}
