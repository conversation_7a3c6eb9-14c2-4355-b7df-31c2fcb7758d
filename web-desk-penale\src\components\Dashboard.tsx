import Grid from '@mui/material/Grid';
import { Box } from '@mui/system';
import { Link } from './shared/Link';
import { useTranslation } from 'react-i18next';
import { MenuItem } from './shared/MenuCard';
import React from 'react';
import { DashboardProps, MenuItemData } from 'src/interfaces';


export const Dashboard: React.FC<DashboardProps> = ({ cardGroups }) => {
  const { t } = useTranslation();

  const titleStyle = {
    fontSize: '20px',
    color: '#fefefe',
    fontWeight: '600',
    paddingLeft: '32px',
    paddingTop: '16px',
  };
  const backgroundTitle = {
    backgroundColor: '#3E4E87',
    height: '140px',
    width: '105%',
    marginLeft: '-24px',
  };

  return (
    <Box>
      {Object.keys(cardGroups).map((k: string) => (
        <React.Fragment key={k}>
          <Grid marginTop={4} item={true} sx={backgroundTitle} xs={12}>
            <Box sx={titleStyle}>{t(k)}</Box>
          </Grid>
          <Grid
            marginTop={4}
            item={true}
            sx={{ marginTop: '-100px' }}
            container
            spacing={2}
            xs={11}
          >
            {cardGroups[k].map((menuItem: MenuItemData) => (
              <Grid item={true} xs={12} lg={4} key={menuItem.link}>
                <Link href={menuItem.link}>
                  <MenuItem item={menuItem} />
                </Link>
              </Grid>
            ))}
          </Grid>
        </React.Fragment>
      ))}
    </Box>
  );
};
