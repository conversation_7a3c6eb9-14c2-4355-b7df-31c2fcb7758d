import { NsLogin } from '@netservice/astrea-react-ds';
import { signIn } from 'next-auth/react';
import { useTranslation } from 'next-i18next';
import { useConfig } from './shared/configuration.context';
import { version } from '../../package.json';


export default function Login() {
  const { t } = useTranslation();
  const { isDebug } = useConfig();
  const tipoDiAccesso = isDebug ? 'keycloak' : 'azure-ad-b2c';

  return (
    <div className={'NsCustomLogin'}>
      <NsLogin
          cardBorderRadius="0px"
          gradient="linear-gradient(-240.64224645720873deg, rgba(48, 138, 125, 0.99)  1.9191447712979693e-14%, rgba(48, 138, 125, 0.7), #0c4b50 100% )"
          handleFormSubmit={function noRefCheck() {}}
          logoSrc="/images/loginMinisteroLogo.png"
          onButtonClick={() => signIn(tipoDiAccesso)}
          title1="DESK CASSAZIONE PENALE"
          title2={version}
          type="link"
          description={t('login.reindirizzamento') as string}
          loginButtonText={t('login.accedi') as string}
          cardWidth="600px"
      />
    </div>
  );
}
