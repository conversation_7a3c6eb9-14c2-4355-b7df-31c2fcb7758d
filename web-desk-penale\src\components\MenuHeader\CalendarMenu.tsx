import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NsHeaderUserMenu,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { signOut } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { NotificheSchemaPopover } from '../relay/relayQueries';
import { GetAuthUser, getMsalLogoutUrl, MsalConfig } from '../shared/Utils';
import { useConfig } from '../shared/configuration.context';
import { relayQueries_NotifichePopoverQuery } from '@/generated/relayQueries_NotifichePopoverQuery.graphql';
import { NotifichationsProps } from 'src/interfaces';
import InfoIcon from '@mui/icons-material/Info';
import LogoutIcon from '@mui/icons-material/Logout';
import SettingsIcon from '@mui/icons-material/Settings';
import { Badge, Box, Button, Typography } from '@mui/material';
import packageJson from '../../../package.json';
import { NotificationsNoneOutlined } from '@mui/icons-material';
import styled from '@emotion/styled';
const ROOT_QUERY = NotificheSchemaPopover;

interface MenuOption {
  name: string;
  path: string;
  action?: () => void;
  icon: React.ReactElement;
}

const MenuIconStyle = {
  marginRight: '.2em',
};

const MenuLogoutIcon = styled(LogoutIcon)(MenuIconStyle);
const MenuInfoIcon = styled(InfoIcon)(MenuIconStyle);
const MenuSettingsIcon = styled(SettingsIcon)(MenuIconStyle);

const MenuButton = styled(Button)({
  color: 'rgba(0, 0, 0, 0.54)',
  padding: 0,
});

const logoImage = () => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <img
        src="/images/logoMinistero.png"
        alt="logo"
        style={{
          height: '55px',
          objectFit: 'cover',
          width: '48px',
          cursor: 'pointer',
        }}
      />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          color: 'white',
          marginLeft: '15px',
        }}
      >
        <Typography variant="h1" className="header-title">
          <strong>Desk Penale</strong> Corte Suprema di Cassazione
        </Typography>
        <Typography variant="caption" sx={{ alignSelf: 'flex-start' }}>
          {packageJson.version}
        </Typography>
      </Box>
    </Box>
  );
};

export default function CalendarMenu() {
  const { t } = useTranslation();
  const router = useRouter();
  const msalConfig: MsalConfig = useConfig();
  const { notifichationsCount } = useConfig();
  const { data: _, token } = GetAuthUser();
  const [counter, setCounter] = useState<{ read: number; unread: number }>({
    read: 0,
    unread: 0,
  });
  const [firstLoad, setFirstLoad] = useState<boolean>(true);
  const [notifichations, setNotifichations] = useState<NotifichationsProps>({
    unread: [],
    read: [],
  });

  const { notify } = useNotifier();

  const { data } = useQuery<relayQueries_NotifichePopoverQuery>(
    ROOT_QUERY,
    {
      first: 10,
      after: '0',
    },
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  useEffect(() => {
    if (data && firstLoad) {
      setFirstLoad(false);
      const mappedNotofiche = data?.notificheByCurrentUserPopover.edges.reduce(
        (result: any, notify: any) => {
          const notificationItem = {
            id: notify.cursor,
            status: notify.node.read ? 'valid' : 'invalid',
            text: notify.node.descrizione,
          };

          if (notify.node.read) {
            result.read.push(notificationItem);
          } else {
            result.unread.push(notificationItem);
          }

          return result;
        },
        { unread: [] as any[], read: [] as any[] }
      );
      setNotifichations({ ...mappedNotofiche });
      const totalNotifications =
        data?.notificheByCurrentUserPopover?.aggregate?.totalElement || 0;
      const unreadNotifications =
        data?.notificheByCurrentUserPopover?.aggregate?.unread || 0;

      setCounter({
        unread: unreadNotifications,
        read: totalNotifications - unreadNotifications,
      });
    }
  }, [data?.notificheByCurrentUserPopover]);

  useEffect(() => {
    setCounter({ ...notifichationsCount });
  }, [notifichationsCount]);

  const customSignOut = async () => {
    try {
      localStorage.clear();
      signOut().then(() => {
        router.push(getMsalLogoutUrl(msalConfig, token));
      });
    } catch (e) {
      notify({
        message: 'Errore durante il logout',
        type: 'error',
      });
      return false;
    }
  };

  const userPanelOptions: MenuOption[] = [
    {
      name: '',
      path: '/impostazioni',
      icon: (
        <MenuButton>
          <MenuSettingsIcon />
          {t('header.accountMenu.impostazioni')}
        </MenuButton>
      ),
    },
    {
      name: '',
      path: '/info',
      icon: (
        <MenuButton>
          <MenuInfoIcon />
          {t('header.accountMenu.info')}
        </MenuButton>
      ),
    },
    {
      name: '',
      icon: (
        <MenuButton onClick={customSignOut}>
          <MenuLogoutIcon />
          {t('header.accountMenu.logout')}
        </MenuButton>
      ),
      path: '#',
    },
  ];

  return (
    <NsHeader
      menuItems={[
        {
          name: t('fascicolo.calendarioUdienza'),
          path: '/calendario',
        },
        {
          name: t('header.links.scrivania'),
          path: '/scrivania',
        },
      ]}

      onLogout={customSignOut}
      router={router}
      logo={logoImage()}
      userPanelMenuItems={
        <>
          <Button onClick={() => router.push('/notifiche')}>
            <Badge badgeContent={counter.unread} color="error">
              <NotificationsNoneOutlined />
            </Badge>
          </Button>
          <NsHeaderUserMenu
            menuItems={userPanelOptions}
            sx={{
              marginRight: '.5em',
            }}
          />
        </>
      }
      type={'horizontal'}
      configuration={{ centralLogo: false }}
    />
  );
}
