import { UtentiComponentFragment$key } from '@/generated/UtentiComponentFragment.graphql';
import { useTranslation } from 'react-i18next';
import { graphql, useFragment } from 'relay-hooks';
import { UtentiComponentProps } from 'src/interfaces';

const fragment = graphql`
  fragment UtentiComponentFragment on Query {
    getMockPeople {
      id
      firstName
      lastName
      birthDate
    }
  }
`;



export const UtentiComponent: React.FC<UtentiComponentProps> = ({ data }) => {
  const { t } = useTranslation();
  const { getMockPeople } = useFragment(fragment, data);

  return (
    <ul>
      {getMockPeople!.map((u) => (
        <li key={u.firstName}>{`${u.firstName} ${u.lastName} nato il ${t(
          'common.dateFormat',
          { date: u.birthDate }
        )}`}</li>
      ))}
    </ul>
  );
};
