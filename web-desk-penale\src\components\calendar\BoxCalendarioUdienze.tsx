import { Box, Typography } from '@mui/material';
import React, { useState } from 'react';
import { BoxCalendarioUdienzeProps } from 'src/interfaces';
import DatiGeneraliUdienza from './DatiGeneraliUdienza';
import Image from 'next/image';

const BoxCalendarioUdienze = ({ selectedEvent }: BoxCalendarioUdienzeProps) => {
  const [text, setText] = useState<string>();

  React.useEffect(() => {
    if (selectedEvent && selectedEvent.title) {
      setText(selectedEvent.title);
    } else {
      setText(
        "Seleziona un'udienza dal calendario\n" +
          'per visualizzare la relativa lista di fascicoli'
      );
    }
  }, [selectedEvent]);

  return (
    <>
      <Box
        mt={5}
        sx={{
          width: '100%',
          height: 500,
          bgcolor: '#ebefef',
          boxSizing: 'border-box',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            maxWidth: 523,
          }}
        >
          {selectedEvent ? (
            <DatiGeneraliUdienza selectedEvent={selectedEvent} dayEvents={[]} />
          ) : (
            <div style={{ textAlign: 'center' }}>
              <Image
                width={32}
                height={36}
                src="/images/calendario.png"
                alt="calendario"
              />
              <Typography variant="h6" style={{ color: 'black' }}>
                {text}
              </Typography>
            </div>
          )}
        </Box>
      </Box>
    </>
  );
};

export default BoxCalendarioUdienze;
