import { Box, Tab, Tabs, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { Sezione, TipoUdienza, circleStyle } from 'src/components/shared/Utils';
import { colors } from './LegendaSezione';
import { useTranslation } from 'next-i18next';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  const { t } = useTranslation();
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      style={{ height: '0px' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function CalendarTabs({
  dayEvents,
  getEvent,
  currentEvent,
}: any) {
  const [value, setValue] = useState(0);
  const { t } = useTranslation();

  const handleChange = (_: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    getEvent(dayEvents[newValue]);
  };

  useEffect(() => {
    const index = dayEvents.findIndex(
      (event: any) => event.id === currentEvent.id
    );
    setValue(index);
  }, [currentEvent]);

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="basic tabs example"
        >
          {dayEvents.map((event: any) => {
            return (
              <Tab
                key={event.id}
                label={
                  <Box alignItems="center" display="flex">
                    <Box
                      mr={1}
                      ml={1}
                      sx={{
                        ...circleStyle,
                        ...(event.sezione === 'SU' && {
                          background: colors.SU,
                        }),
                        ...(event.sezione === 'S7' && {
                          background: colors.VII,
                        }),
                        ...(event.sezione &&
                          ['S1', 'S2', 'S3', 'S4', 'S5', 'S6'].includes(
                            event.sezione
                          ) && { background: colors.V }),
                      }}
                    ></Box>
                    {event?.type !== 'SCADENZA' && (
                      <Box
                        mr={1}
                        p={0.5}
                        sx={{
                          backgroundColor: '#BDC5C7',
                          fontWeight: 700,
                          borderRadius: '5px',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        {t('calendario.calendarTabs.chiusa')}
                      </Box>
                    )}
                    <Box component="span">
                      {`${Sezione[event.sezione]} | ${
                        TipoUdienza[event.descrizione]
                      } |
                      COLL ${event.aula}`}
                    </Box>
                  </Box>
                }
                {...a11yProps(event.id)}
              />
            );
          })}
        </Tabs>
      </Box>
      {dayEvents.map((event: any, i: number) => (
        <CustomTabPanel key={event.id} value={value} index={i}></CustomTabPanel>
      ))}
      <style jsx global>{`
        .css-1k7kg73-MuiButtonBase-root-MuiTab-root.Mui-selected {
          background: white !important;
        }
      `}</style>
    </Box>
  );
}
