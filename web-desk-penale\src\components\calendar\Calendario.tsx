import {
  Box,
  Fade,
  FormControlLabel,
  Grid,
  Switch,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CalendarEvent } from 'src/interfaces';
import BoxCalendarioUdienze from './BoxCalendarioUdienze';
import DatiGeneraliUdienza from './DatiGeneraliUdienza';
import MainCalendar from './MainCalendar';
import CodaButton from '../shared/CodaButton';
export default function Calendario() {
  const { t } = useTranslation();
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );
  const [dayEvents, setDayEvents] = useState<CalendarEvent[]>([]);
  const [showCalendar, setShowCalendar] = useState(true);

  const handleSelectEvent = (
    event: CalendarEvent,
    currentDayEvents: CalendarEvent[]
  ) => {
    setSelectedEvent(event);
    setDayEvents([...currentDayEvents]);
    localStorage.setItem('event', JSON.stringify(event));
    localStorage.setItem('dayEvents', JSON.stringify([...currentDayEvents]));
  };

  const router = useRouter();
  const { params } = router.query;

  const getEvent = useCallback(async () => {
    if (params) {
      const event = localStorage.getItem('event');
      const dayEvents = localStorage.getItem('dayEvents');
      setSelectedEvent(JSON.parse(event as string));
      setDayEvents(JSON.parse(dayEvents as string));
    }
  }, []);

  useEffect(() => {
    getEvent();
  }, []);

  return (
    <Grid p={4} container>
      <Typography variant="h1" m={2} style={{ fontSize: '3rem' }}>
        {t('fascicolo.calendarioUdienza')}
      </Typography>
      <Grid item container>
        <Grid item xs={12} container alignItems={'center'}>
          <FormControlLabel
            control={
              <Switch
                checked={showCalendar}
                onChange={() => setShowCalendar((prev) => !prev)}
              />
            }
            label="Calendario"
          />
        </Grid>
        <Fade in={showCalendar}>
          <Grid
            display={showCalendar ? 'block' : 'none'}
            item
            xs={12}
            md={12}
            lg={4.5}
          >
            <MainCalendar
              onSelectEvent={(
                event: CalendarEvent,
                currentDayEvents: CalendarEvent[]
              ) => handleSelectEvent(event, currentDayEvents)}
            />
          </Grid>
        </Fade>
        <Grid item xs={12} md={12} lg={showCalendar ? 7.5 : 12}>
          {!selectedEvent ? (
            <BoxCalendarioUdienze selectedEvent={selectedEvent} />
          ) : (
            <DatiGeneraliUdienza
              selectedEvent={selectedEvent}
              dayEvents={dayEvents}
              closeDatiUdienza={() => setSelectedEvent(null)}
            />
          )}
        </Grid>
      </Grid>
      <CodaButton
        refreshPage={() =>
          setSelectedEvent((prev: any) => ({
            ...prev,
            refreshed: !prev?.refreshed,
          }))
        }
        ruolo={'ESTENSORE'}
      />
    </Grid>
  );
}
