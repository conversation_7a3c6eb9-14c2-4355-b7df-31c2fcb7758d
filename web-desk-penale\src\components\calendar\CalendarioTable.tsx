import FileDownloadIcon from '@mui/icons-material/FileDownload';
import FolderIcon from '@mui/icons-material/Folder';
import {
  Box,
  Checkbox,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { NsDataGridVirtualizedInfiniteScrolling, RelayLoadMoreOptions } from 'src/components/shared/NsDataGridVirtualizedInfiniteScrolling';
import { useRouter } from 'next/router';
import axios from 'axios';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CalendarioTableProps2 } from 'src/interfaces';
import {
  formatDate,
  getStateNames,
} from '../shared/Utils';
import { StatoProvvedimentiEnum } from '../../types/types';

import { useConfig } from '../shared/configuration.context';
import DetailParti from './DetailParti';
import DetailReati from './DetailReati';
import TrackingStato from './TrackingStato';
import { useDownloadUtils } from '../shared/Utils';
import DetailRiuniti from './components/DetailRiuniti';
import DialogModal from '../shared/DialogModal';
import { usePagination } from 'relay-hooks';
import ButtonMenu from '../shared/ButtonMenu';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import { relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery } from '@/generated/relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery.graphql';
import { relayQueriesUdienzeFragment_RicorsiUdienzaPaginata$key } from '@/generated/relayQueriesUdienzeFragment_RicorsiUdienzaPaginata.graphql';
import { CalendarioTableFragment } from '../relay/relayQueriesUdienzeFragment';
import { StatiSic } from 'src/types';
const checkboxStyle = {
  '& .MuiSvgIcon-root': {
    width: '24px',
    height: '24px',
    color: 'black !important',
  },
  padding: 0,
  marginLeft: '-3px',
};

const iconBoxStyles = {
  background: '#e0eeec',
  width: '30px',
  height: '30px',
  marginRight: '10px',
  cursor: 'pointer',
  alignItems: 'center',
  justifyContent: 'center',
  display: 'flex',
};

const buttonProps = {
  name: 'Azioni massive',
  menuNames: [
    {
      title: 'Scarica Intestazioni',
      value: 'intesazioni',
      sx: { background: 'red' },
    },
    { title: 'Importa Provvedimenti', value: 'provvedimenti' },
  ],
  endIcon: <ExpandMoreIcon />,
};

export default function CalendarioTable({
  idUdienza,
  query,
}: CalendarioTableProps2) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [selected, setSelected] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [nrgList, setNrgList] = useState<any>([]);

  const router = useRouter();
  const { servizi } = useConfig();

  const serviceUrl = `${servizi}`;
  const [isDownloadingIntestazioni, setIsDownloadingIntestazioni] =
    useState(false);

  const [buttonDisabled, setButtonDisabled] = useState<boolean>(true);

  const { downloadFile } = useDownloadUtils(serviceUrl);
  const [clickedRows, setClickedRows] = useState<any>([]);
  const [selectChecked, setSelectChecked] = useState<boolean>(false);
  const queryVaribile = {
    idUdienza: idUdienza,
    first: 20,
    after: null,
    before: null,
    last: null,
    status: null,
  };
  const [selectedOption, setSelectedOption] = useState<string | null>('TUTTI');
  const { notify } = useNotifier();
  const [selectedFascicoliForImport, setSelectedFascicoliForImport] =
    useState<any>([]);

  const handleRowClick = (row: any) => {
    if (
      row.esitoParziale.motivo == 'PARZIALE' ||
      row.esitoParziale.motivo == 'DEFINITIVO' ||
      selected.length > 0
    ) {
      setButtonDisabled(false);
    } else {
      setButtonDisabled(true);
    }
    // La sincronizzazione di nrgList e selectedFascicoliForImport è gestita da useEffect
  };

  const serviceUrlGetZip = `${servizi}/provvedimento/getTemplateZip/`;
  const downloadIntestazioniMassivo = async (
    idUdienza: number,
    nrgList: number[]
  ) => {
    try {
      setIsDownloadingIntestazioni(true);
      const response = await axios.post(
        `${serviceUrlGetZip}${idUdienza}`,
        {
          nrgList: nrgList,
        },
        {
          responseType: 'arraybuffer',
        }
      );
      const url = window.URL.createObjectURL(
        new Blob([response.data], { type: 'application/zip' })
      );
      const link = document.createElement('a');
      const fileName = 'intestazioni.zip';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      notify({
        message: 'Intestazioni scaricate con successo',
        type: 'success',
      });
    } catch (error) {
      notify({
        message: 'Errore durante il download delle intestazioni',
        type: 'error',
      });
    } finally {
      setIsDownloadingIntestazioni(false);
    }
  };

  const options = [
      { keyName: t('calendario.datiGeneraliUdienza.tutti'), value: 'TUTTI' },
    {
      keyName: t('calendario.datiGeneraliUdienza.inRedazioneEstensore'),
      value: StatoProvvedimentiEnum.IN_RELAZIONE_ESTENSORE,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.daRedigere'),
      value: StatoProvvedimentiEnum.DA_REDIGERE,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.minutaDepositataDaSic'),
      value: StatoProvvedimentiEnum.MINUTA_DEPOSITATA_SIC,
    },
    { keyName: t('calendario.datiGeneraliUdienza.inBozza'), value: StatoProvvedimentiEnum.IN_BOZZA },
    {
      keyName: t('calendario.datiGeneraliUdienza.inCodaFirmaRelatore'),
      value: StatoProvvedimentiEnum.IN_CODE_FIRMA_REL,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.inviatoInCancelleria'),
      value: StatoProvvedimentiEnum.INVIATO_IN_CANCELLERIA_RELATORE,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.bustaRifiutata'),
      value: StatoProvvedimentiEnum.BUSTA_RIFIUTATA,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.bustaRifiutataAlPresidente'),
      value: StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE,
    },
    {
      keyName: t(
        'calendario.datiGeneraliUdienza.minutaAccettataInviataAlPresidente'
      ),
      value: StatoProvvedimentiEnum.MINUTA_ACCETTATA,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.minutaDaModificare'),
      value: StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.minutaRevisione'),
      value: StatoProvvedimentiEnum.MINUTA_IN_REVISIONE,
    },
    {
      keyName: t(
        'calendario.datiGeneraliUdienza.minutaModificataDalPresidente'
      ),
      value: StatoProvvedimentiEnum.MINUTA_MODIFICATA_PRESIDENTE,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.inviatoInCancelDalPresidente'),
      value: StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.depositatoDaSic'),
      value: StatoProvvedimentiEnum.PROVV_DEPOSITATO_SIC,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.pubblicato'),
      value: StatoProvvedimentiEnum.PUBBLICATA,
    },
    {
      keyName: t('calendario.datiGeneraliUdienza.pubblicatoDaSic'),
      value: StatoProvvedimentiEnum.PUBBLICATO_SIC,
    },
  ];

  const closeModal: any = (value?: string) => {
    if (value === 'backdropClick') return;
    setModalProps({ ...modalProps, isOpen: false });
  };

  const {
    data: fascicoli,
    error,
    loadNext,
    hasNext,
    isLoadingNext,
    refetch,
    isLoading: isLoadingFascicoli,
  } = usePagination<
    relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery,
    relayQueriesUdienzeFragment_RicorsiUdienzaPaginata$key
  >(CalendarioTableFragment, query);
  const edges = useMemo(
    () =>
      fascicoli?.ricorsiUdienza?.edges
        ? [...fascicoli.ricorsiUdienza.edges]
        : [],
    [fascicoli]
  );
  const totalRowCount = query.getFascitoloDetExtraInfo?.totalCount || 0;
  const fascitoloDetExtraInfo = query.getFascitoloDetExtraInfo;

  // Callback per caricare più dati quando l'utente scorre
  const handleLoadMore = useCallback(
    (options: RelayLoadMoreOptions) => {
      // Chiama loadNext di Relay
      loadNext(options.fetchSize);
    },
    [loadNext]
  );

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    title: '',
    content: <></>,
    onClose: closeModal,
  });

  const handleSelectedOption = (e: any) => {
    setSelectedOption(e.target.value);
    queryVaribile.status =
      !e.target.value || e.target.value === 'TUTTI' ? null : e.target.value;
    refetch(queryVaribile);
  };

  const handleModal = (param: string, data?: any) => {
    let content, title;
    const id = data.provvedimento?.idProvvedimento;
    const isEstensorePresidente = Boolean(
      data.isEstensore && data.isPresidente
    );
    if (param == 'stato') {
      if (
        (id !== undefined && id !== null) ||
        data.checkStatoOnSIC.statoProvvedimento == StatiSic.provvedimentoDepositatoSic ||
        data.checkStatoOnSIC.statoProvvedimento == StatiSic.minutaDepositataSic ||
        data.checkStatoOnSIC.statoProvvedimento == StatiSic.pubblicatoSic
      ) {
        content = (
          <TrackingStato
            id={id}
            sicCheckStato={data.checkStatoOnSIC}
            roles="RELATORE"
            isEstensorePresidente={isEstensorePresidente}
          />
        );
        title =
          'Stato provvedimento fascicolo ' +
          data?.ricorso?.numero +
          '/' +
          data?.ricorso?.anno;
      } else {
        return;
      }
    } else if (param == 'reati') {
      content = <DetailReati reati={data} />;
      title = t('calendario.detailReati.visualizzaReati');
    } else if (param == 'riuniti') {
      content = (
        <Typography p={1} border={1} variant="body2" color="text.primary">
          {data.checkStatoOnSIC.ricorsoRiunito.numero}/
          {data.checkStatoOnSIC.ricorsoRiunito.anno}
        </Typography>
      );
      title = t('calendario.calendarioTable.riunitoAl');
    } else if (param == 'vediRiuniti') {
      content = (
        <DetailRiuniti idRicorsoUdienza={data?.idRicudien}></DetailRiuniti>
      );
      title = t('fascicolo.fascicoliRiuniti');
    } else {
      content = <DetailParti parti={data.ricorso.nrg} />;
      const formatteddate = formatDate(data?.dataUdienza, 'DD MMMM YYYY');
      title = `Parti fascicolo ${data.ricorso.numero || ''}/${
        data.ricorso.anno || ''
      }`;
    }

    setModalProps({
      ...modalProps,
      content: content!,
      title: title ?? '',
      isOpen: true,
    });
  };

  enum Icons {
    Source = 1,
    FileDownload,
  }

  const iconConfig = [
    {
      id: Icons.Source,
      name: 'SourceIcon',
      color: '#2E5A60',
      IconComponent: FolderIcon,
      tooltipKey: 'visualizzaFascicolo',
    },
    {
      id: Icons.FileDownload,
      name: 'FileDownloadIcon',
      color: '#2E5A60',
      IconComponent: FileDownloadIcon,
      tooltipKey: 'downloadIntestazione',
    },
  ];

  const icons = iconConfig.map(
    ({ id, name, color, IconComponent, tooltipKey }) => ({
      id,
      name,
      icon: (
        <NsTooltip
          title={t(`calendario.calendarioTable.${tooltipKey}`)}
          icon={<IconComponent fontSize="small" sx={{ color }} />}
        />
      ),
    })
  );

  const handleRoute = (row: any, iconId: number) => {
    switch (iconId) {
      case Icons.Source:
        router.push(`/fascicolo/${idUdienza}?params=${row.ricorso?.nrg}`);
        break;
      case Icons.FileDownload:
        setIsLoading(true);
        downloadFile(
          `${serviceUrl}/provvedimento/getTemplateDocx/${idUdienza}/${row.ricorso?.nrg}`,
          `Provvedimento_${row?.tipologia}_${row.ricorso.numero}_${row.ricorso.anno}.docx`,
          'Intestazione scaricata con successo',
          "Impossibile scaricare l'intestazione"
        ).finally(() => {
          setIsLoading(false);
        });
        break;
      default:
        handleModal('provvedimento', row);
    }
  };

  const checkIsVisibleCheckBox = (row: any) => {
   return   row.checkStatoOnSIC.statoProvvedimento !== 'RIUNITO' &&
    !Object.values(StatiSic).includes(row.stato) &&
    !row.esitoParziale?.esitoParziale &&
    row.isEstensore;
  }

  const getSelectableRows = () =>
    edges.filter((row: any) => checkIsVisibleCheckBox(row.node));

  const areAllCheckboxesSelected = (selectableRows?: any) => {
    const _selectableRows = selectableRows ?? getSelectableRows();
    return (
      _selectableRows.length > 0 &&
      _selectableRows.every((row: any) =>
        selected.includes(row.node.idRicudien)
      )
    );
  };

  const handleSelectAllClick = () => {
    const selectableRows = getSelectableRows();

    const newSelected = areAllCheckboxesSelected(selectableRows)
      ? []
      : selectableRows.map((row: any) => row.node.idRicudien);

    setSelected(newSelected);
  };

  const handleCheckboxClick = (row: any) => {
    if (selected.includes(row.node.idRicudien)) removeSelectedRow(row.node);
    else addSelectedRow(row.node);
    handleRowClick(row.node);
  };

  const addSelectedRow = (row: any) => {
    if (selected.includes(row.idRicudien)) return;
    setSelected([...selected, row.idRicudien]);
  };

  const removeSelectedRow = (row: any) => {
    if (!selected.includes(row.idRicudien)) return;
    setSelected(selected.filter((id: any) => id !== row.idRicudien));
  };

  useEffect(() => {
    if (!selectChecked) return;
    const notSelectedRows = edges
      .filter((row: any) => !selected.includes(row.node.idRicudien))
      .map((row: any) => row.node.idRicudien);
    setSelected([...selected, ...notSelectedRows]);
  }, [edges]);

  // Sincronizza nrgList con selected
  useEffect(() => {
    const selectedNrgs = edges
      .filter((edge: any) => selected.includes(edge.node.idRicudien))
      .map((edge: any) => {
        const nrg = edge.node.ricorso?.nrg;
        return nrg ? parseInt(nrg) : null;
      })
      .filter((nrg: any) => nrg !== null);

    setNrgList(selectedNrgs);
    setClickedRows(selectedNrgs);
  }, [selected, edges]);

  // Sincronizza selectedFascicoliForImport con selected
  useEffect(() => {
    const selectedFascicoli = edges
      .filter((edge: any) => selected.includes(edge.node.idRicudien))
      .map((edge: any) => edge.node)
      .filter((node: any) => {
        return (
          node.isEstensore &&
          (node.checkStatoOnSIC?.statoProvvedimento === 'DA_REDIGERE' ||
            !node.checkStatoOnSIC?.statoProvvedimento) 
        );
      });

    setSelectedFascicoliForImport(selectedFascicoli);
  }, [selected, edges]);

  const columns: ColumnDef<any>[] = [
    {
      id: 'select',
      header: () => {
        setSelectChecked(areAllCheckboxesSelected());
        if (selectChecked)
          setButtonDisabled(false);
        return (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            width="100%"
            height="100%"
            marginLeft={1}
          >
            <Checkbox
              checked={selectChecked}
              onChange={handleSelectAllClick}
              sx={checkboxStyle}
            />
          </Box>
        );
      },
      size: 50,
      cell: ({ row }) => {
        const index = row.index;
        return checkIsVisibleCheckBox(row.original.node) ? (
          <Checkbox
            color="primary"
            checked={selected.includes(row.original.node.idRicudien)}
            onChange={() => {
              handleCheckboxClick(row.original);
            }}
            className="selectCheckbox"
          />
        ) : null;
      },
    },
    {
      id: 'esito',
      header: t('calendario.esitoUdienza').toString(),
      size: 100,
      cell: ({ row }) => (
        <Typography
          variant="body2"
          color="text.primary"
          className="column-esito"
        >
          {row.original.node.esitoParziale?.motivo}
        </Typography>
      ),
    },
    {
      id: 'ordine',
      header: t('calendario.ordine').toString(),
      size: 70,
      cell: ({ row }) => (
        <Typography
          variant="body2"
          color="text.primary"
          className="column-ordine"
        >
          {row.original.node.numOrdine}
        </Typography>
      ),
    },
    {
      id: 'nrg',
      header: t('calendario.nrg').toString(),
      size: 150,
      cell: ({ row }) => {
        const handleClickRiunito = () =>
          handleModal('riuniti', row.original.node);
        const handleClickVediRiuniti = () =>
          handleModal('vediRiuniti', row.original.node);

        return (
          <Box
            sx={{ display: 'flex', alignItems: 'center' }}
            className={'ns-display-2col column-nrg'}
          >
            <Box>
              <Typography variant="body2" color="text.primary">
                {row.original?.node?.ricorso?.numero +
                  '/' +
                  row.original?.node?.ricorso?.anno}
              </Typography>
            </Box>
            <Box>
              {row.original?.node?.checkStatoOnSIC
                ?.isPrincipalRicorsoRiunito && (
                <NsButton
                  onClick={handleClickVediRiuniti}
                  sx={theme.custom.secondaryButton}
                >
                  {t('common.principale')}
                </NsButton>
              )}
              {row.original?.node?.checkStatoOnSIC?.ricorsoRiunito && (
                <NsButton
                  onClick={handleClickRiunito}
                  sx={theme.custom.secondaryButton}
                >
                  (R)
                </NsButton>
              )}
            </Box>
          </Box>
        );
      },
    },
    {
      id: 'parti',
      header: t('calendario.parti').toString(),
      size: 180,
      cell: ({ row }) => {
        const partePrincipale = row.original?.node?.ricorso?.detParti;
        return (
          <Box
            sx={{ display: 'flex', alignItems: 'center' }}
            className={'ns-display-2col column-parti'}
          >
            <Box>
              <Typography variant="body2" color="text.primary">
                {partePrincipale}
              </Typography>
            </Box>
            <Box>
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() => handleModal('parti', row.original.node)}
              >
                {t('common.vedi')}
              </NsButton>
            </Box>
          </Box>
        );
      },
    },
    {
      id: 'reato',
      header: t('calendario.reato').toString(),
      size: 180,
      cell: ({ row }) => {
        const reato = row.original?.node?.ricorso?.reatiRicorso.find(
          (reato: any) => reato?.principale == true
        )?.reato.displayReati;

        const reati = row.original?.node?.ricorso?.reatiRicorso.map(
          (reato: any) => {
            return (
              reato?.reato?.displayReati +
              (reato?.principale ? ' Principale' : '')
            );
          }
        );

        return (
          <Box
            sx={{ display: 'flex', alignItems: 'center' }}
            className={'ns-display-2col column-reato'}
          >
            <Box>
              <Typography variant="body2" color="text.primary">
                {reato}
              </Typography>
            </Box>
            <Box>
              {reati?.length > 1 && (
                <NsButton
                  sx={theme.custom.secondaryButton}
                  onClick={() => handleModal('reati', reati)}
                >
                  {t('common.vedi')}
                </NsButton>
              )}
            </Box>
          </Box>
        );
      },
    },
    {
      id: 'valore',
      header: t('calendario.valorePonderale').toString(),
      size: 100,
      cell: ({ row }) => (
        <Typography
          variant="body2"
          color="text.primary"
          className="column-valore"
        >
          {row.original?.node?.ricorso?.spoglio?.valPond}
        </Typography>
      ),
    },
    {
      id: 'tipologia',
      header: t('calendario.tipologia').toString(),
      size: 120,
      cell: ({ row }) => (
        <Typography
          variant="body2"
          color="text.primary"
          className="column-tipologia"
        >
          {row.original.node.tipologia}
        </Typography>
      ),
    },
    {
      id: 'semplificata',
      header: t('calendario.semplificata').toString(),
      size: 100,
      cell: ({ row }) => (
        <Typography
          variant="body2"
          color="text.primary"
          className="column-semplificata"
        >
          {row.original.node.esito?.semplificata == '1' ? 'SI' : 'NO'}
        </Typography>
      ),
    },
    {
      id: 'stato',
      header: t('calendario.stato').toString(),
      size: 170,
      cell: ({ row }) => {
        const res = row?.original.node;
        const clickableStyle = {
          cursor: 'pointer',
          textDecoration: 'underline',
        };
        const clickableStyle2 = { textDecoration: 'none' };
        const handleClick = () => handleModal('stato', res);

        const getStateContent = (
          state: string,
          styleUnlessUnderline?: boolean
        ) => {
          const isNoLink = [
            'RIUNITO',
            'DA_REDIGERE',
            'RIUNITO_CARTACEO',
            'REDAZIONE_ESTENSORE',
          ].includes(state);

          return (
            <Box
              display="flex"
              alignItems="center"
              onClick={handleClick}
              sx={
                isNoLink || styleUnlessUnderline
                  ? clickableStyle2
                  : clickableStyle
              }
              className={'column-stato'}
            >
              <Typography variant="body2" color="text.primary">
                {getStateNames('RELATORE', state)}
              </Typography>
            </Box>
          );
        };

        if (
          res.esitoParziale?.esitoParziale &&
          res.checkStatoOnSIC?.statoProvvedimento !==
            StatoProvvedimentiEnum.RIUNITO
        ) {
          return <Box display="flex">-</Box>;
        }

        if (
          !res.isEstensore &&
          res.checkStatoOnSIC?.statoProvvedimento !==
            StatoProvvedimentiEnum.RIUNITO
        ) {
          res.stato = 'REDAZIONE_ESTENSORE';
        }

        const { checkStatoOnSIC, provvedimento } = res;
        const stato = provvedimento?.stato || StatoProvvedimentiEnum.DA_REDIGERE;
        const statoProvvedimento = checkStatoOnSIC?.ricorsoRiunito
          ? StatoProvvedimentiEnum.RIUNITO
          : checkStatoOnSIC?.statoProvvedimento;

        if (
          [
            StatiSic.pubblicatoSic,
            StatiSic.minutaDepositataSic,
            StatiSic.provvedimentoDepositatoSic,
          ].includes(statoProvvedimento)
        ) {
          return getStateContent(statoProvvedimento);
        }

        if (
          stato === StatoProvvedimentiEnum.BOZZA &&
          (provvedimento?.isRevisione)
        ) {
          return getStateContent(StatoProvvedimentiEnum.MINUTA_IN_REVISIONE);
        }

        if (stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA) {
          return (
            <Box
              display="flex"
              alignItems="center"
              onClick={handleClick}
              sx={clickableStyle}
            >
              <Typography variant="body2" color="text.primary">
                {provvedimento?.changeStatus?.prevStato ===
                StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE
                  ? 'Busta rifiutata al Presidente'
                  : getStateNames('RELATORE', stato)}
              </Typography>
            </Box>
          );
        }

        if (
          [
            StatoProvvedimentiEnum.RIUNITO,
            StatoProvvedimentiEnum.RIUNITO_CARTACEO,
          ].includes(statoProvvedimento)
        ) {
          return getStateContent(statoProvvedimento, true);
        }

        const state = stato || StatoProvvedimentiEnum.DA_REDIGERE;
        return getStateContent(state, state == StatoProvvedimentiEnum.DA_REDIGERE);
      },
    },
    {
      id: 'oscuramento',
      header: t('calendario.oscuramentoSic').toString(),
      size: 120,
      cell: ({ row }) => (
        <Typography variant="body2" color="text.primary">
          {row.original.node.oscuratoSicComplessivo == true ? 'SI' : 'NO'}
        </Typography>
      ),
    },
    {
      id: 'azioni',
      header: t('calendario.azioni').toString(),
      size: 100,
      cell: ({ row }) => {
        const { stato, isEstensore, esitoParziale, checkStatoOnSIC } =
          row.original.node;

        const shouldRemoveFileDownload =
          !isEstensore ||
          (esitoParziale?.motivo !== 'PARZIALE' &&
            esitoParziale?.motivo !== 'DEFINITIVO') ||
          checkStatoOnSIC?.statoProvvedimento === StatoProvvedimentiEnum.RIUNITO ||
          Object.values(StatiSic).includes(stato);

        const filteredIcons = icons.filter((icon) => {
          if (shouldRemoveFileDownload && icon.id === Icons.FileDownload) {
            return false;
          }
          return true;
        });

        return (
          <Box display="flex" justifyContent="center">
            {filteredIcons.map(({ id, icon }) => (
              <Box
                onClick={() => handleRoute(row.original.node, id)}
                key={id}
                sx={iconBoxStyles}
              >
                {icon}
              </Box>
            ))}
          </Box>
        );
      },
    },
  ];

  // Visualizza un indicatore di caricamento durante il caricamento iniziale e non ci sono dati
  if (isLoadingFascicoli && edges.length === 0) {
    return (
      <Grid item p={3} mt={2} sx={{ background: 'white' }} xs={12}>
        <Typography>Caricamento in corso...</Typography>
      </Grid>
    );
  }

  // Gestisci eventuali errori
  if (error) {
    return (
      <Grid item p={3} mt={2} sx={{ background: 'white' }} xs={12}>
        <Typography color="error">
          Si è verificato un errore: {error.message}
        </Typography>
      </Grid>
    );
  }

  const getNestedValue = (obj: any, path: string) => {
    if (!obj) return undefined;
    const parts = path.split('.');
    let value = obj;
    for (const part of parts) {
      if (value === null || value === undefined) return undefined;
      value = value[part];
    }
    return value;
  };

  const initialSorting: SortingState = [];

  return (
    <>
      <Grid
        item
        p={3}
        mt={2}
        container
        alignItems="center"
        justifyContent="space-between"
        border={theme.custom.borders[0]}
        sx={{
          background: 'white',
        }}
        xs={12}
      >
        <Grid
          item
          xs={6}
          container
          alignItems="center"
          justifyContent={'start'}
        >
          <Typography variant="h2">
            {t('calendario.datiGeneraliUdienza.listaFascicoli')}
          </Typography>
        </Grid>
        <Grid
          item
          mt={2}
          mb={3}
          container
          alignItems="center"
          justifyContent={'start'}
        >
          <Box display="flex" alignItems="center" flex-direction="column">
            <Typography variant="h5" mr={3}>
              {t('calendario.datiGeneraliUdienza.numeroFascicoli')}:{' '}
              {fascitoloDetExtraInfo.totalCount}
            </Typography>
            <Typography variant="h5">
              {t('calendario.datiGeneraliUdienza.valorePonderaleTotale')}:{' '}
              {fascitoloDetExtraInfo?.valorePonderaleTotale?.toFixed(2)}
            </Typography>
          </Box>
          <Grid
            item
            xs={6}
            ml={15}
            container
            alignItems="center"
            justifyContent={'end'}
          ></Grid>
          <Grid container justifyContent="flex-end">
            <Box display="flex" alignItems="center" component="span" pr={2}>
              {t('calendario.datiGeneraliUdienza.statoProvvedimento')}:{' '}
            </Box>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <Select value={selectedOption} onChange={handleSelectedOption}>
                  {options.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.keyName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Grid>

        {fascicoli?.ricorsiUdienza?.edges && (
          <div className="calendario-table">
            <NsDataGridVirtualizedInfiniteScrolling
              columns={columns}
              mode="relay" // Aggiungi questa proprietà
              data={edges}
              totalRowCount={totalRowCount}
              onLoadMore={handleLoadMore}
              isLoading={isLoading || isLoadingNext}
              hasMore={hasNext}
              fetchSize={20}
              containerHeight="600px"
              estimatedRowHeight={50}
              enableColumnGrouping={true}
            />
          </div>
        )}

        <DialogModal {...modalProps} />
        <NsFullPageSpinner isOpen={isLoading || isLoadingFascicoli || isDownloadingIntestazioni} value={1} />
        <Grid item container justifyContent="space-between" mt={2}></Grid>
      </Grid>

      <Grid item mt={2} container justifyContent="flex-start">
        <ButtonMenu
          {...buttonProps}
          disabled={buttonDisabled || selected.length === 0}
          disableImport={selectedFascicoliForImport.length === 0}
          handleButton={(value) => {
            if (value === 'intesazioni') {
              downloadIntestazioniMassivo(idUdienza, nrgList);
            }
            if (value === 'provvedimenti') {
              localStorage.setItem(
                'importaProvvedimenti',
                JSON.stringify(selectedFascicoliForImport)
              );
              router.push({
                pathname: `/fascicolo/provvedimento/${idUdienza}`,
              });
            }
          }}
        />
      </Grid>
    </>
  );
}
