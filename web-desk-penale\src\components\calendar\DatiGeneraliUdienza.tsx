import { relayQueries_DatiGeneraliUdienzaQuery } from '@/generated/relayQueries_DatiGeneraliUdienzaQuery.graphql';
import AccessAlertIcon from '@mui/icons-material/AccessAlarm';
import {
  Box,
  Grid,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import moment from 'moment';
import {useEffect, useMemo, useState} from 'react';
import { useTranslation } from 'react-i18next';
import {STORE_OR_NETWORK, useQuery} from 'relay-hooks';
import { STORE_THEN_NETWORK } from 'relay-hooks/lib/RelayHooksTypes';
import {
  CalendarEvent,
  DatiGeneraliEventProps,
} from 'src/interfaces';
import {DatiGeneraliUdienzaSchema} from '../relay/relayQueries';
import UdienzaDatiCalendarHeader from '../scrivania/UdienzaDatiCalendarHeader';
import MainModal from '../shared/MainModal';
import { formatDate, udienzeColors } from '../shared/Utils';
import CalendarTabs from './CalendarTabs';
import CalendarioTable from './CalendarioTable';
import { DetailUdienzaCalendar } from './DetailUdienzaCalendar';
import {MainQueryCalendarioTable_ricorsiUdienza} from "../relay/relayQueriesUdienzeFragment";
import {
  relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery
} from "@/generated/relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery.graphql";

const DatiGeneraliUdienza = (props: DatiGeneraliEventProps) => {
  const { t } = useTranslation();

  const { selectedEvent, closeDatiUdienza, dayEvents } = props;
  const theme: any = useTheme();

  const [modal, setModal] = useState(false);
  const [collegio, setCollegio] = useState<any>([]);
  const [currentEvent, setCurrentEvent] =
    useState<CalendarEvent>(selectedEvent);
  const [termineDeposito, setTermineDeposito] = useState<any>();
  const [retrayGraphQl, setRetrayGraphQl] = useState<boolean>(false);
  const [isDownloadingIntestazioni, setIsDownloadingIntestazioni] =
    useState(false);

  const { data:datiGeneraliUdienza, isLoading, retry } =
    useQuery<relayQueries_DatiGeneraliUdienzaQuery>(
      DatiGeneraliUdienzaSchema,
      {
        id: Number(currentEvent.id),
      },
      {
        fetchPolicy: STORE_THEN_NETWORK,
      }
    );
  const queryVariables = useMemo(() => {
    return {
      idUdienza: Number(currentEvent.id),
      first: 20,
      after: null,
      before: null,
      last: null
    };
  }, [currentEvent.id]);

  const { error, data,  } =
    useQuery<relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery>
    (MainQueryCalendarioTable_ricorsiUdienza, queryVariables, {
    fetchPolicy: STORE_OR_NETWORK,
  });
  useEffect(() => {
    if(error) {
      alert(error)
    }
  }, [error])
  const openModal = () => setModal(true);
  const closeModal = () => setModal(false);

  useEffect(() => {
    const tempCollegio = [...(datiGeneraliUdienza?.udienza.collegio ?? [])];

    setCollegio(tempCollegio);
    setTermineDeposito(
      formatDate(datiGeneraliUdienza?.udienza?.termineDeposito, 'MM-DD-YYYY')
    );
  }, [datiGeneraliUdienza, currentEvent]);

  useEffect(() => {
    if (selectedEvent.id === currentEvent.id && retrayGraphQl) {
      retry();
      return;
    }
    setCurrentEvent(selectedEvent);
  }, [selectedEvent]);


  const style = {
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    p: 2,
  };
  const termineDepositoFormatted = formatDate(termineDeposito, 'DD-MM-YYYY');

  const modalTitle =
    'Collegio Udienza ' +
    moment(datiGeneraliUdienza?.udienza?.dataUdienza).format('D MMMM YYYY');

  const body = <DetailUdienzaCalendar collegio={collegio} />;

  const getEvent = (event: CalendarEvent) => {
    setCurrentEvent(event);
  };



  const modalProps = {
    modal,
    closeModal,
    openModal,
    style,
    title: modalTitle,
    body,
  };



  const closeDettaglioCalendario = () => {
    setRetrayGraphQl(true);
    if (closeDatiUdienza) {
      closeDatiUdienza();
    }
  };

  return isLoading || isDownloadingIntestazioni ? (
    <NsFullPageSpinner isOpen value={1} />
  ) : (
    <Grid container>
      <Grid
        item
        p={5}
        sx={{ background: '#EBEFEF' }}
        borderTop={`10px ${
          udienzeColors[selectedEvent.type as string] || '#068e87'
        } solid`}
        xs={12}
      >
        <Box display="flex" mb={2} justifyContent="space-between">
          {selectedEvent.type === 'SCADENZA' ? (
            <Typography alignItems="center" display="flex" variant="h1">
              <Box
                alignItems="center"
                height="35px"
                borderRadius="5px"
                display="flex"
                bgcolor={'#FFA500'}
                fontSize="15px"
                padding="8px 15px 8px 10px"
                marginRight="8px"
              >
                <AccessAlertIcon
                  sx={{
                    color: 'white',
                    borderRadius: '5px',
                    marginRight: '5px',
                    marginLeft: '0px'
                  }}
                />{' '}
                <Box color="white">
                  {t('calendario.datiGeneraliUdienza.scadenze')}
                </Box>
              </Box>
              {t('calendario.datiGeneraliUdienza.udienza')}{' '}
              {t('calendario.datiGeneraliUdienza.del')}{' '}
              {moment(datiGeneraliUdienza?.udienza?.dataUdienza).format('D MMMM YYYY')}
            </Typography>
          ) : (
            <Typography variant="h1">
              {t('calendario.datiGeneraliUdienza.udienza')}{' '}
              {t('calendario.datiGeneraliUdienza.del')}{' '}
              {moment(datiGeneraliUdienza?.udienza?.dataUdienza).format('D MMMM YYYY')}
            </Typography>
          )}
          <NsButton
            sx={theme.custom.secondaryButton}
            onClick={closeDettaglioCalendario}
          >
            {t('common.chiudi')}
          </NsButton>
        </Box>
        <CalendarTabs
          getEvent={(event: CalendarEvent) => getEvent(event)}
          dayEvents={dayEvents}
          currentEvent={currentEvent}
        />
        <UdienzaDatiCalendarHeader
          mainCollegio={collegio}
          termineDeposito={termineDepositoFormatted}
        />
        {currentEvent?.id && data && <CalendarioTable
          idUdienza={Number(currentEvent.id)}
          query={data}
        />}

      </Grid>
      <MainModal {...modalProps} />
    </Grid>
  );
};

export default DatiGeneraliUdienza;
