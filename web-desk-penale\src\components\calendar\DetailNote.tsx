import { relayQueries_ProvvedimentoNoteQuery } from '@/generated/relayQueries_ProvvedimentoNoteQuery.graphql';
import { Box, Grid, Typography, useTheme } from '@mui/material';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { ProvvedimentoNoteSchema } from '../relay/relayQueries';
import { formatDate, StatoInserimentoNoteEnum } from '../shared/Utils';
import { StatoProvvedimentiEnum } from '../../types/types';
import { useEffect, useState } from 'react';

const ROOT_QUERY = ProvvedimentoNoteSchema;

export default function DetailNote({
  id,
  stato,
  role,
}: {
  id: String;
  stato?: String;
  role: String;
}) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const [notes, setNotes] = useState<any[]>([]);

  const { data, isLoading } = useQuery<relayQueries_ProvvedimentoNoteQuery>(
    ROOT_QUERY,
    {
      id: String(id),
    },
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  useEffect(() => {
    if(data?.provvedimentoNote){
      const notesTmp = data?.provvedimentoNote.map((item: any) => item);
      //rimuovo se presente la nota di busta accettata della cancelleria (il relatore non deve visualizzarla)
      if(role === 'RELATORE'){
        const notesFiltered = notesTmp.filter((nota: any) => nota.statoInserimento !== StatoInserimentoNoteEnum.MINUTA_ACCETTATA)
        setNotes([...notesFiltered]);
      } else {
        setNotes([...notesTmp]);
      }       
    }
  }, [data, role]);

  const renderHeaderNota = (nota: any, index: number) => {
    if (stato == StatoProvvedimentiEnum.BUSTA_RIFIUTATA){
      return <Typography
                variant="h2"
                sx={{ color: 'black', mb: 1, display: 'flex', flexGrow: 1 }}
              >
                {t('provvedimenti.motivoRifiuto')}
              </Typography>
    } else if (nota.statoInserimento === StatoInserimentoNoteEnum.MINUTA_ACCETTATA ){
        return <Typography
                  variant="h2"
                  sx={{ color: 'black', mb: 1, display: 'flex', flexGrow: 1 }}
                >
                  {t('provvedimenti.bustaAccettate')}
                </Typography>
    } else {
        return <Typography
                  variant="h2"
                  sx={{ color: 'black', mb: 1, display: 'flex', flexGrow: 1 }}
                >
                  {t('provvedimenti.motivoRichiestaModifica')}
                </Typography>
    }
  }

  return isLoading ? (
    <NsFullPageSpinner isOpen={true} value={1} />
  ) : (
    <Grid container border={1} sx={{ width: '100%' }}>
      {notes?.map((note, index) => (
        <Grid key={index} item xs={12}>
          <Box p={2}>
            <Grid item xs={12} sx={{ display: 'flex' }}>
              {renderHeaderNota(note, index)}
              <Typography sx={{ color: 'black', mb: 1 }}>
                {formatDate(note.dataInserimento, 'DD/MM/YYYY HH:mm')}
              </Typography>
            </Grid>
            <Typography variant="h6">{note.note}</Typography>
          </Box>
        </Grid>
      ))}
    </Grid>
  );
}
