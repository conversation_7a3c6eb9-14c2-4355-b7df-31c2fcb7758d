import { relayQueries_DatiPartiQuery } from '@/generated/relayQueries_DatiPartiQuery.graphql';
import { Box, Grid, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { DetailPartiProps } from 'src/interfaces';
import { DatiPartiSchema } from '../relay/relayQueries';

export const ROOT_QUERY = DatiPartiSchema;

export default function DetailParti({ parti }: DetailPartiProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const iconsStyle = { color: '#2e5a60' };
  const scrollBar = {
    overflow: 'auto',
    maxHeight: 400,
  };
  const { data, isLoading } = useQuery<relayQueries_DatiPartiQuery>(
    ROOT_QUERY,
    {
      id: Number(parti),
    },
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  if (isLoading) {
    return <>Loading...</>;
  }

  const caseData = data?.ricorso?.listaParti;

  return (
    <Box sx={{ border: 1, p: 2, marginBottom: 2, ...scrollBar }}>
      <Grid container spacing={2}>
        {caseData?.controParteList?.map((controParte, index) => (
          <Grid item xs={12} key={index}>
            <Typography variant="h6">
              <Typography component="span" style={{ fontWeight: 'bold' }}>
                {controParte.parte.displayParti}
              </Typography>{' '}
              -
              <Typography
                variant="body1"
                component="span"
                style={{ fontSize: 'medium' }}
              >
                {controParte.parte.anagraficaParte?.cognome}{' '}
                {controParte.parte.anagraficaParte?.nome}
              </Typography>
            </Typography>
            {controParte.parte.difensori?.map((difensore, index) => (
              <Box key={index}>
                <Typography
                  variant="body1"
                  component="span"
                  style={{ fontSize: 'medium', marginLeft: '2.5rem' }}
                >
                  <Typography component="span">-</Typography>{' '}
                  {difensore.difensoreAnagrafica?.cognome}{' '}
                  {difensore.difensoreAnagrafica?.nome} -{' '}
                  <em>{difensore.tipoDifensore?.descrizione}</em>{' '}
                  <Typography component="span">
                    {difensore?.deceduto ? '- DEC.' : ''}
                  </Typography>{' '}
                </Typography>
              </Box>
            ))}
            {controParte.parte.parteLegata && (
              <Typography
                variant="body1"
                component="span"
                style={{ fontSize: 'medium', marginLeft: '2.5rem' }}
              >
                <Typography component="span">
                  {controParte.parte.parteLegata.tipoLegame?.descrizione} -{' '}
                  {controParte.parte.parteLegata.anagraficaParte?.cognome}{' '}
                  {controParte.parte.parteLegata.anagraficaParte?.nome}
                </Typography>
              </Typography>
            )}
            {controParte.controParte && controParte.controParte.length > 0 && (
              <Box>
                <Typography
                  variant="body1"
                  component="span"
                  style={{ fontSize: 'medium', marginLeft: '2.5rem' }}
                >
                  <Typography component="span" style={{ fontWeight: 'bold' }}>
                    C/
                  </Typography>
                </Typography>
              </Box>
            )}
            {controParte.controParte &&
              controParte.controParte.map((controParte, index) => (
                <Box key={index}>
                  <Typography
                    variant="body1"
                    component="span"
                    style={{ fontSize: 'medium', marginLeft: '3.5rem' }}
                  >
                    <Typography component="span" style={{ fontWeight: 'bold' }}>
                      {' '}
                      {controParte.displayParti}
                    </Typography>{' '}
                    - {controParte.anagraficaParte?.cognome}{' '}
                    {controParte.anagraficaParte?.nome}
                  </Typography>
                </Box>
              ))}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
