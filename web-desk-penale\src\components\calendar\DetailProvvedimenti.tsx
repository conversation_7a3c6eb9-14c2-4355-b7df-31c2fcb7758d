import { Box, Grid, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function DetailProvvedimenti({ provvedimenti }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const iconsStyle = { color: '#2e5a60' };
  const scrollBar = {
    overflow: 'auto',
    maxHeight: 400,
  };
  return (
    <Grid container>
      <Grid item xs={12} p={1}>
        <Grid
          container
          justifyContent="space-between"
          sx={{ background: '#FFFFFF' }}
        >
          <Grid item xs={12} mt={3}>
            <Box sx={scrollBar}>
              <Grid item p={1} mt={1} border={theme.custom.borders[1]} xs={12}>
                {provvedimenti?.map((provvedimento: any, i: number) => {
                  return (
                    <Typography key={i} variant="h4" ml={2} mt={1}>
                      {provvedimento}
                    </Typography>
                  );
                })}
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
}
