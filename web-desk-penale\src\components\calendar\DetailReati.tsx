import { Box, Grid, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function DetailReati({ reati }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const iconsStyle = { color: '#2e5a60' };
  const scrollBar = {
    overflow: 'auto',
    maxHeight: 400,
  };
  return (
    <Grid container>
      <Grid item xs={12}>
        <Grid item xs={12}>
          <Box sx={scrollBar}>
            <Grid item p={1} border={1} xs={12}>
              {reati?.map((reato: any, i: number) => {
                return (
                  <Typography key={i} variant="h4">
                    {t('calendario.detailReati.reato')} {reato}{' '}
                    {reato.principale ? ' Principale' : ''}
                  </Typography>
                );
              })}
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Grid>
  );
}
