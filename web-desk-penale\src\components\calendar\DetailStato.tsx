import * as React from 'react';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Typography from '@mui/material/Typography';
import { DetailStatoSchema } from '../relay/relayQueries';
import { STORE_THEN_NETWORK, useQuery } from 'relay-hooks';
import { relayQueries_DetailStatoQuery } from '@/generated/relayQueries_DetailStatoQuery.graphql';
import { DetailStatoProps } from 'src/interfaces';
import WarningIcon from '@mui/icons-material/Warning';
import { useTranslation } from 'next-i18next';
import { getStateNames, TrackingState } from '../shared/Utils';
import { StatoProvvedimentiEnum } from '../../types/types';
import moment from 'moment';

const steps: Array<{
  label: string;
  description: string;
  dateChange: string;
  stato: string;
  completed: boolean;
  prevStato?: string;
  isRevisione?: boolean;
}> = [
  {
    label: 'Minuta in revisione',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_IN_REVISIONE,
    completed: false,
  },
  {
    label: 'In bozza',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.BOZZA,
    completed: false,
  },
  {
    label: 'Inviata in cancelleria',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.INVIATO_IN_CANCELLERIA_RELATORE,
    completed: false,
  },
  {
    label: 'Minuta depositata e inviata al Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_DEPOSITATA,
    completed: false,
  },
  {
    label: 'Busta rifutata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.BUSTA_RIFIUTATA,
    completed: false,
  },
  {
    label: 'Busta rifiutata al Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE,
    completed: false,
  },
  {
    label: 'Minuta accettata e inviata al Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_ACCETTATA,
    completed: false,
  },
  {
    label: 'Richiesta modifica',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE,
    completed: false,
  },
  {
    label: 'Minuta modificata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_MODIFICATA,
    completed: false,
  },
  {
    label: 'Inviata in cancelleria dal Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE,
    completed: false,
  },
  {
    label: 'Pubblicata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.PUBBLICATA,
    completed: false,
  },
  {
    label: 'In coda di firma',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.CODA_DI_FIRMA,
    completed: false,
  },
  {
    label: 'In coda di firma 1',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.IN_CODE_FIRMA_REL,
    completed: false,
  },
  {
    label: 'Bozza',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.BOZZA_PRESIDENTE,
    completed: false,

  },
  {
    label : 'Minuta modificata dal Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_MODIFICATA_PRESIDENTE,
    completed: false,
  }
];

const ROOT_QUERY = DetailStatoSchema;

export default function DetailStato({ id, roles }: DetailStatoProps) {
  const [activeStep, setActiveStep] = React.useState(0);
  const { t } = useTranslation();
  const { data, isLoading } = useQuery<relayQueries_DetailStatoQuery>(
    ROOT_QUERY,
    {
      id,
      roles
    },
    {
      fetchPolicy: STORE_THEN_NETWORK,
    }
  );

  React.useEffect(() => {
    if (data && data.provvedimentoChangeStatusByIdProvvedimento) {
      const completedSteps =
        data.provvedimentoChangeStatusByIdProvvedimento.map((status) => {
          return steps.findIndex((step) => step.stato == status.stato);
        });
      const maxCompletedStep = Math.max(...completedSteps);
      setActiveStep(maxCompletedStep);
      steps.forEach((step, index) => {
        step.completed = index <= maxCompletedStep;

        step.dateChange = data.provvedimentoChangeStatusByIdProvvedimento.find(
          (status) => status.stato === step.stato
        )?.dateChange;
        step.prevStato =
          data.provvedimentoChangeStatusByIdProvvedimento.find(
            (status) => status.stato === step.stato
          )?.prevStato || undefined;
        step.isRevisione =
          data.provvedimentoChangeStatusByIdProvvedimento.find(
            (status) => status.stato === step.stato
          )?.isRevisione || false;

        if (step.dateChange) {
          const date = new Date(step.dateChange);
          step.dateChange = date.toLocaleString('it-IT', {
            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          });
        }

      });
    }
  }, [data]);

  const filteredSteps = data?.provvedimentoChangeStatusByIdProvvedimento
    ?.map((status) => {
      const step = steps.find((step) => step.stato === status.stato);
      return step ? step.label : '';
    })
    .filter((label) => label !== '');

  const sortedSteps = steps
    .filter((step) => filteredSteps?.includes(step.label))
    .sort((a, b) => {
      const dateA = moment(a.dateChange, 'DD/MM/YYYY HH:mm:ss');
      const dateB = moment(b.dateChange, 'DD/MM/YYYY HH:mm:ss');
      return dateA.valueOf() - dateB.valueOf();
    });

  return (
    <Box sx={{ maxWidth: 400 }}>
      {sortedSteps?.length ? (
        <Stepper activeStep={activeStep} orientation="vertical">
          {sortedSteps.map((step, index) => (
            <Step key={step.label} completed={step.completed}>
              <StepLabel
                style={{
                  color: step.completed ? 'inherit' : 'gray',
                  display: 'flex',
                  alignItems: 'center',
                }}
                icon={
                  step.label === 'Busta rifutata' ||
                  step.label === 'Busta rifiutata al Presidente' ||
                  step.label === 'Richiesta modifica' ? (
                    <WarningIcon color="error" />
                  ) : undefined
                }
              >
                {step.isRevisione
                  ? TrackingState[StatoProvvedimentiEnum.MINUTA_IN_REVISIONE]
                  : getStateNames(roles, step.stato)}
                - {step.dateChange}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      ) : (
        <Typography variant="h6">{t('spinner.loading')}</Typography>
      )}
    </Box>
  );
}
