import { relayQueries_PenaleCollegioDetailsQuery } from '@/generated/relayQueries_PenaleCollegioDetailsQuery.graphql';
import { Box, Typography, useTheme } from '@mui/material';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { MagType } from 'src/interfaces';
import { PenaleCollegioDetailsSchema } from '../relay/relayQueries';

const ruolo: MagType = {
  PRE: 'Presidente',
  CON: 'Consigliere',
  PM: 'PM',
  RI: 'Riserva',
  REL: 'Relatore',
};

export const ROOT_QUERY = PenaleCollegioDetailsSchema;

export function DetailUdienza({ idUdienza, nrg }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const [collegio, setCollegio] = useState<any>();

  const { data, isLoading } = useQuery<relayQueries_PenaleCollegioDetailsQuery>(
    ROOT_QUERY,
    {
      idUdienza: Number(idUdienza),
      nrg: Number(nrg),
    },
    {
      fetchPolicy: STORE_OR_NETWORK,
      skip: !idUdienza || !nrg,
    }
  );

  useEffect(() => {
    const tempCollegioMagistratiList =
      data?.colleggioDetails?.colleggioMagistrati;
    setCollegio(tempCollegioMagistratiList);
  });

  return (
    <Box sx={{ width: '100%' }}>
      {isLoading ? (
        <NsFullPageSpinner isOpen={true} value={1} />
      ) : (
        <Typography sx={{ display: 'inline-block' }} variant="h4">
          {collegio?.map((colleg: any, index: number, row: any) => {
            const role = ruolo[colleg.tipoMag];
            let name = `${colleg.magistrato.anagraficaMagistrato.nome} ${colleg.magistrato.anagraficaMagistrato.cognome}`;
            if (colleg.isRelatore && role === 'Presidente') {
              name += ' (Presidente Relatore)';
            } else if (role === 'Presidente') {
              name += ' (Presidente)';
            } else if (colleg.isRelatore) {
              name += ' (Relatore)';
            } else if (colleg.isEstensore) {
              name += ' (Estensore)';
            }
            if (index + 1 === row.length) {
              // Last one.
              return (
                <React.Fragment key={colleg.magistrato.id ?? index}>
                  {name}
                </React.Fragment>
              );
            } else {
              return (
                <React.Fragment key={colleg.magistrato.id ?? index}>
                  {name},&nbsp;
                </React.Fragment>
              );
            }
          })}
        </Typography>
      )}
    </Box>
  );
}
