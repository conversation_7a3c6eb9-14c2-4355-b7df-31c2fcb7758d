import { Box, Grid, Typography, useTheme } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MagType } from 'src/interfaces';

const ruolo: MagType = {
  PRE: 'Presidente',
  CON: 'Consigliere',
  PM: 'PM',
  RI: 'Riserva',
};

export function DetailUdienzaCalendar({ collegio }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();

  const borders = {
    border: theme.custom.borders[0],
    borderBottom: theme.custom.borders[0],
    borderRight: theme.custom.borders[0],
  };
  const boxProps = { sx: { display: 'flex', ...borders } };
  const cellProps = { sx: { p: 1, ...borders } };
  const firstCellProps = { sx: { p: 1, background: '#EBEFEF', ...borders } };

  return (
    <Box {...boxProps}>
      <Grid container>
        <Grid item xs={6} {...firstCellProps}>
          <Typography variant="h4">{t('common.nomeCognome')}</Typography>
        </Grid>
        <Grid item xs={6} {...firstCellProps}>
          <Typography variant="h4">{t('common.ruolo')}</Typography>
        </Grid>
        {collegio?.map((colleg: any) => {
          return (
            <React.Fragment key={colleg.magistrato.id}>
              <Grid item xs={6} {...cellProps}>
                <Typography variant="h5">
                  {colleg.magistrato.anagraficaMagistrato.nome}{' '}
                  {colleg.magistrato.anagraficaMagistrato.cognome}{' '}
                </Typography>
              </Grid>
              <Grid item xs={6} {...cellProps}>
                <Typography variant="h5">{ruolo[colleg.tipoMag]}</Typography>
              </Grid>
            </React.Fragment>
          );
        })}
      </Grid>
    </Box>
  );
}
