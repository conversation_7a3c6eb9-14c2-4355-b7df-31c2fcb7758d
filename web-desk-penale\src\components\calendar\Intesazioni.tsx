import {
  Box,
  Button,
  Grid,
  Typography,
  useTheme
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import DatiUdienza from '../shared/DatiUdienza';
import IntestazioniTable from './IntestazioniTable';

export default function Intestazioni() {
  const theme: any = useTheme();
  const { t } = useTranslation();

  const handleClick = () => {
    window.location.href = '/calendario';
  };

  return (
    <Grid container sx={{ flex: 1 }}>
      <Grid
        mb={5}
        item
        mt={5}
        display="flex"
        justifyContent="space-between"
        xs={12}
      >
        <Box>
          <Box component={'span'} sx={{ color: '#8D8D8D' }}>
            {t('fascicolo.calendarioUdienza')}
          </Box>
          /Scarica Intestazioni
        </Box>
      </Grid>
      <Grid container item xs={12}>
        <Grid container item md={6} xs={12}>
          <DatiUdienza />
          <Grid item container justifyContent="flex-star" mt={2}>
            <Button variant="contained" color="primary" onClick={handleClick}>
              {t('calendar.intestazioni.calendarioUdienze')}
            </Button>
          </Grid>
        </Grid>
        <Grid
          p={2}
          item
          xs={12}
          md={6}
          border={theme.custom.borders[0]}
          sx={{ height: 'fit-content' }}
        >
          <Grid container item justifyContent={'space-between'} xs={12}>
            <Box>
              <Typography variant="h2">
                {t('intestazioni.scaricaintestazioni')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} mt={3}>
            <IntestazioniTable />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
}
