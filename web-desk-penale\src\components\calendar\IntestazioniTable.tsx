import InfoIcon from '@mui/icons-material/Info';
import { Box } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Column } from 'src/interfaces';
import RelayTable from '../shared/RelayTable';

interface Data {
    nrg: string;
    parti: string;
    reato: string;
    valore: string;
    stato: string;
    oscuramento: string;

    tipologia: string;
}

function createData(
    nrg: string,
    parti: string,
    reato: string,
    valore: string,
    stato: string,
    oscuramento: string,
    tipologia: string
): Data {
    return { nrg, parti, reato, valore, stato, oscuramento, tipologia };
}

const renderTipologia = (cell: any, row: any) => {
    const style = {
        border: '1px solid',
        borderRadius: '5px',
        background: '#C8E3F7',
        color: '#005694',
        textAlign: 'center',
    };
    return (
        <TableCell key={cell.id} align={cell.align}>
            <Box sx={style}>{row.tipologia}</Box>
        </TableCell>
    );
};

const renderStato = (cell: any, row: any) => {
    return (
        <TableCell key={cell.id} align={cell.align}>
            <Box display="flex">
                <InfoIcon sx={{ color: '#C4C4C4' }} />{' '}
                <Box ml={1} component="span">
                    {' '}
                    {row.stato}
                </Box>
            </Box>
        </TableCell>
    );
};

const renderParti = (cell: any, row: any) => {
    return (
        <TableCell key={cell.id} align={cell.align}>
            <Box display="flex">
                <Box ml={1} component="span">
                    {' '}
                    {row.arti}
                </Box>
            </Box>
        </TableCell>
    );
};

const renderReato = (cell: any, row: any) => {
    return (
        <TableCell key={cell.id} align={cell.align}>
            <Box display="flex">
                <Box ml={1} component="span">
                    {' '}
                    {row.reato}
                </Box>
            </Box>
        </TableCell>
    );
};

const renderValore = (cell: any, row: any) => {
    return (
        <TableCell key={cell.id} align={cell.align}>
            <Box display="flex">
                <Box ml={1} component="span">
                    {' '}
                    {row.valore}
                </Box>
            </Box>
        </TableCell>
    );
};

const renderOscuramento = (cell: any, row: any) => {
    return (
        <TableCell key={cell.id} align={cell.align}>
            <Box display="flex">
                <Box ml={1} component="span">
                    {' '}
                    {row.oscuramento}
                </Box>
            </Box>
        </TableCell>
    );
};

const renderNrg = (cell: any, row: any) => {
    return (
        <TableCell key={cell.id} align={cell.align}>
            <Box display="flex">
                <Box ml={1} component="span">
                    {' '}
                    {row.nrg}
                </Box>
            </Box>
        </TableCell>
    );
};

export default function IntestazioniTable() {
    const { t } = useTranslation();
    const [selected, setSelected] = useState<any[]>([]);

    const handleSelectAllClick = (
        event: React.ChangeEvent<HTMLInputElement>,
        rows: any
    ) => {
        if (event.target.checked) {
            const newSelected = rows.map((n: any, i: number) => i);
            setSelected(newSelected);
            return;
        }
        setSelected([]);
    };

    const handleClick = (_: React.MouseEvent<unknown>, index: number) => {
        const array = selected.includes(index)
            ? selected.filter((i) => i !== index)
            : [...selected, index];
        setSelected([...array]);
    };

    const columns: Column[] = [
        {
            id: 'NRG',
            align: 'left',
            label: t('intestazioni.nrg') as string,
            minWidth: 170,
            render: renderNrg,
        },
        {
            id: 'Parti',
            minWidth: 170,
            label: t('intestazioni.parti') as string,
            align: 'left',
            render: renderParti,
        },
        {
            id: 'reato',
            align: 'left',
            label: t('intestazioni.reato') as string,
            minWidth: 170,
            render: renderReato,
        },
        {
            id: 'valore',
            minWidth: 170,
            label: t('intestazioni.valore') as string,
            align: 'left',
            render: renderValore,
        },
        {
            id: 'stato',
            minWidth: 170,
            label: t('intestazioni.stato') as string,
            align: 'left',
            render: renderStato,
        },
        {
            id: 'oscuramento',
            minWidth: 170,
            label: t('intestazioni.oscuramento') as string,
            align: 'left',
            render: renderOscuramento,
        },
        {
            id: 'tipologia',
            minWidth: 170,
            label: t('intestazioni.tipologia') as string,
            align: 'left',
            render: renderTipologia,
        },
    ];

    const rows = [
        createData(
            '27/2019',
            'visualizza',
            'Immigrazione',
            '1',
            'In bozza',
            'Si',
            'Sent'
        ),
    ];

    return <RelayTable rows={rows} columns={columns} />;
}
