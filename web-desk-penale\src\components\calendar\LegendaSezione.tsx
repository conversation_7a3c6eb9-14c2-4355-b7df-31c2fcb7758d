import AccessAlertIcon from '@mui/icons-material/AccessAlarm';
import { Box, Grid, Typography } from '@mui/material';

export const texts: {
  V: string;
  VII: string;
  SU: string;
  S: string;
  [key: string]: string;
} = {
  V: 'Sezione semplice',
  VII: 'VII sezione',
  SU: 'Sezioni Unite',
  S: 'Scadenze',
};

export const colors: {
  V: string;
  VII: string;
  SU: string;
  [key: string]: string;
} = {
  V: '#068e87' /*VERDE ACQUA*/,
  VII: '#0082e0' /*BLUE*/,
  SU: '#990381' /*LILLA*/,
};

const Legend = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-around',
        margin: 2,
      }}
    >
      <Grid container spacing={2} alignItems="center">
        {Object.keys(texts).map((key) => (
          <Grid key={key} item xs={3} sx={{ xs: 6, md: 3 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              {key === 'S' ? (
                <AccessAlertIcon
                  sx={{
                    color: 'white',
                    backgroundColor: 'orange',
                    borderRadius: '20%',
                    margin: 0.8,
                  }}
                />
              ) : (
                <Box
                  sx={{
                    width: 20,
                    height: 20,
                    padding: 0.5,
                    bgcolor: colors[key],
                    marginRight: 1,
                    borderRadius: '50%',
                  }}
                />
              )}
              <Typography variant="body2">{texts[key]}</Typography>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Legend;
