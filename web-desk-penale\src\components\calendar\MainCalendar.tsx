//@ts-nocheck
import { relayQueries_MainCalendarQuery } from '@/generated/relayQueries_MainCalendarQuery.graphql';
import AccessAlarmIcon from '@mui/icons-material/AccessAlarm';
import { Box, Container, Grid, Typography } from '@mui/material';
import { NsFullPageSpinner, NsButton } from '@netservice/astrea-react-ds';
import moment from 'moment';
import { useMemo, useState } from 'react';
import { Calendar, DayPropGetter, momentLocalizer } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { renderToString } from 'react-dom/server';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { CalendarEvent, MainCalendarProps } from 'src/interfaces';
import { MainCalendarSchema } from '../relay/relayQueries';
import { formatDate } from '../shared/Utils';
import LegendaSezione, { colors } from './LegendaSezione';
import MyCustomAgenda from './MyCustomAgenda';
import { useTranslation } from 'next-i18next';

require('moment/locale/it');

const ROOT_QUERY = MainCalendarSchema;

export default function MainCalendar({
  onSelectEvent,
}: Readonly<MainCalendarProps>) {
  const { data: data1, isLoading } = useQuery<relayQueries_MainCalendarQuery>(
    ROOT_QUERY,
    {},
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  const { t } = useTranslation();
  const [currentView, setCurrentView] = useState('month');
  const [groupedEventsMonth, setGroupedEventsMonth] = useState([]);
  const [allEvents, setAllEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );

  // TODO - Rivedere la scrittura dello useMemo
  useMemo(() => {
    const events =
      data1?.udienze
        .filter((udienza: any) => udienza.dataUdienza !== undefined)
        .map((udienza: any) => {
          const start = udienza.inizioUdienza
            ? new Date(Date.parse(udienza.inizioUdienza))
            : new Date(Date.parse(udienza.dataUdienza));

          if (
            start.getHours() === 0 &&
            start.getMinutes() === 0 &&
            start.getSeconds() === 0
          ) {
            start.setHours(start.getMinutes() + 1);
          }
          const dataUdienzaStart = new Date(Date.parse(udienza.dataUdienza));
          let dataUdienzaEnd = new Date(Date.parse(udienza.dataUdienza));
          dataUdienzaEnd = dataUdienzaEnd.setHours(
            dataUdienzaEnd.getHours() + 12
          );
          const end =
            udienza.inizioUdienza && udienza.fineUdienza
              ? new Date(Date.parse(udienza.fineUdienza))
              : new Date(Date.parse(udienza.dataUdienza));
          return {
            id: udienza.idUdien,
            dataUdienzaStart: dataUdienzaStart,
            dataUdienzaEnd: dataUdienzaEnd,
            start,
            end,
            title: udienza.tipoUdienza.sigla,
            type: udienza.sezione.sigla,
            descrizione: udienza.tipoUdienza.descrizione,
            aula: udienza.aula.descrizione,
            termineDeposito: udienza.termineDeposito,
            sezione: udienza.sezione.sigla,
          };
        }) ?? [];
    const eventsScadenze =
      data1?.udienze
        .filter(
          (udienza: any) =>
            udienza.isEstensore &&
            udienza.dataUdienza !== undefined &&
            !udienza.allPubblicate
        )
        .map((udienza: any) => {
          const start = new Date(Date.parse(udienza.termineDeposito));
          const end = new Date(Date.parse(udienza.termineDeposito));
          start.setHours(0, 0, 0, 0);
          end.setHours(0, 0, 0, 0);
          return {
            id: udienza.idUdien,
            dataUdienza: new Date(Date.parse(udienza.termineDeposito)),
            dataUdienzaStart: start,
            dataUdienzaEnd: end,
            start,
            end,
            titleScadenza: udienza.tipoUdienza.sigla,
            type: 'SCADENZA',
            descrizione: udienza.tipoUdienza.descrizione,
            aula: udienza.aula.descrizione,
            termineDeposito: udienza.termineDeposito,
            sezione: udienza.sezione.sigla,
          };
        }) ?? [];

    const totalEvents = events.concat(eventsScadenze);

    setAllEvents([...totalEvents]);
    const filteredEvents = [];

    for (let event of totalEvents) {
      const duplicated = filteredEvents.some((item) => {
        if (
          (event.title === item.title &&
            event.sezione === item.sezione &&
            formatDate(event.start, 'YYYY/MM/DD') ==
              formatDate(item.start, 'YYYY/MM/DD')) ||
          (item.type === 'SCADENZA' &&
            formatDate(event.start, 'YYYY/MM/DD') ==
              formatDate(item.start, 'YYYY/MM/DD'))
        ) {
          return true;
        }
      });
      if (!duplicated) {
        filteredEvents.push(event);
      }
    }

    const filteredEventsMount = [];

    for (let event of totalEvents) {
      const duplicated = filteredEventsMount.some((item) => {
        if (
          (event.title === item.title &&
            event.sezione === item.sezione &&
            formatDate(event.dataUdienzaStart, 'YYYY/MM/DD') ==
              formatDate(item.dataUdienzaStart, 'YYYY/MM/DD')) ||
          (item.type === 'SCADENZA' &&
            formatDate(event.dataUdienzaStart, 'YYYY/MM/DD') ==
              formatDate(item.dataUdienzaStart, 'YYYY/MM/DD'))
        ) {
          return true;
        }
      });
      if (!duplicated) {
        filteredEventsMount.push(event);
      }
    }

    setGroupedEventsMonth(filteredEventsMount);
  }, [data1]);

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const handleSelectEvent = (event: CalendarEvent) => {
    const currentDayEvents = allEvents.filter((e) => {
      if (
        event.type !== 'SCADENZA' &&
        e.title === event.title &&
        e.sezione === event.sezione &&
        formatDate(e.start, 'YYYY/MM/DD') ==
          formatDate(event.start, 'YYYY/MM/DD')
      ) {
        return e;
      }

      if (
        event.type === 'SCADENZA' &&
        e.type === 'SCADENZA' &&
        formatDate(e.start, 'YYYY/MM/DD') ==
          formatDate(event.start, 'YYYY/MM/DD')
      ) {
        return e;
      }
    });

    onSelectEvent!(event, [...currentDayEvents]);
    setSelectedEvent(event);
  };
  const handleSelectEventMonth = (event: CalendarEvent) => {
    const currentDayEvents = allEvents.filter((e) => {
      if (
        event.type !== 'SCADENZA' &&
        e.title === event.title &&
        e.sezione === event.sezione &&
        formatDate(e.dataUdienzaStart, 'YYYY/MM/DD') ==
          formatDate(event.dataUdienzaStart, 'YYYY/MM/DD')
      ) {
        return e;
      }

      if (
        event.type === 'SCADENZA' &&
        e.type === 'SCADENZA' &&
        formatDate(e.start, 'YYYY/MM/DD') ==
          formatDate(event.start, 'YYYY/MM/DD')
      ) {
        return e;
      }
    });

    onSelectEvent!(event, [...currentDayEvents]);
    setSelectedEvent(event);
  };

  const dayPropGetter: DayPropGetter = (date: Date) => {
    if (selectedEvent && moment(date).isSame(selectedEvent.start, 'day')) {
      return {
        className: 'selectedEvent',
      };
    }
    return {};
  };
  const dayPropGetterMonth: DayPropGetter = (date: Date) => {
    if (
      selectedEvent &&
      moment(date).isSame(selectedEvent.dataUdienzaStart, 'day')
    ) {
      return {
        className: 'selectedEvent',
      };
    }
    return {};
  };

  const formats = {
    dayFormat: 'DD/MM/YYYY',
    dayHeaderFormat: 'DD/MM/YYYY',
    agendaDateFormat: 'DD/MM/YYYY',
  };

  const localizer = momentLocalizer(moment);

  const CustomAgenda = ({ event }: any) => {
    return (
      <Grid container>
        <Grid item xs={12}>
          <Typography variant="h5" textAlign="center">
            {event.title}
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <Typography variant="body1" textAlign="center">
            {event.type === 'S1' && 'Prima Sezione'}
            {event.type === 'S2' && 'Seconda Sezione'}
            {event.type === 'S3' && 'Terza Sezione'}
            {event.type === 'S4' && 'Quarta Sezione'}
            {event.type === 'S5' && 'Quinta Sezione'}
            {event.type === 'S6' && 'Sesta Sezione'}
            {event.type === 'S7' && 'Settima Sezione'}
            {event.type === 'SU' && 'Sezioni Unite'}
            {event.type === 'SCADENZA' && 'SCADENZE'}
          </Typography>
        </Grid>
      </Grid>
    );
  };

  const CustomDay = ({ event }: any) => {
    return (
      <Grid container>
        <Grid item xs={12}>
          <Typography variant="h5" textAlign="start">
            {(event.title || event.titleScadenza) + ' - '}
            {event.type === 'S1' && 'Prima Sezione'}
            {event.type === 'S2' && 'Seconda Sezione'}
            {event.type === 'S3' && 'Terza Sezione'}
            {event.type === 'S4' && 'Quarta Sezione'}
            {event.type === 'S5' && 'Quinta Sezione'}
            {event.type === 'S6' && 'Sesta Sezione'}
            {event.type === 'S7' && 'Settima Sezione'}
            {event.type === 'SU' && 'Sezioni Unite'}
            {' Collegio: ' + event.aula}
          </Typography>
        </Grid>
      </Grid>
    );
  };

  type CustomToolbarProps = {
    label: string;
    onNavigate: (action: 'PREV' | 'NEXT') => void;
    onView: (view: 'month' | 'day' | 'agenda') => void;
  };

  const CustomToolbar = ({ label, onNavigate, onView }: CustomToolbarProps) => {
    return (
      <Grid container justifyContent="space-between" alignItems="center" mb={1}>
        <Grid item xs={12} mb={2}>
          <Typography variant="h1" textAlign="center">
            {label.charAt(0).toUpperCase() + label.slice(1)}
          </Typography>
        </Grid>
        <Grid item xs={6} md={5}>
          <Grid container alignItems="center">
            <Grid item>
              <NsButton
                size="small"
                variant="contained"
                onClick={() => onNavigate('PREV')}
                sx={{
                  backgroundColor: '#068e87',
                  color: 'white',
                  marginRight: 1,
                  line: '1.75em !important',
                }}
              >
                {'<'}
              </NsButton>
            </Grid>
            <Grid item>
              <NsButton
                size="small"
                variant="contained"
                onClick={() => onNavigate('NEXT')}
                sx={{
                  backgroundColor: '#068e87',
                  color: 'white',
                  line: '1.75em !important',
                }}
              >
                {'>'}
              </NsButton>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={6} md={7}>
          <Grid container justifyContent="flex-end">
            <Grid item>
              <NsButton
                className={
                  currentView == 'month'
                    ? 'ButtonWithCustomBoardHoverActive'
                    : 'ButtonWithCustomBoardHover '
                }
                size="small"
                variant="contained"
                onClick={() => onView('month')}
                sx={{
                  backgroundColor: '#068e87',
                  color: 'white',
                  marginRight: 1,
                  border: '1px solid #e6e6e6 !important',
                }}
              >
                {t('calendario.mainCalendar.mese')}
              </NsButton>
            </Grid>
            <Grid item>
              <NsButton
                className={
                  currentView == 'agenda'
                    ? 'ButtonWithCustomBoardHoverActive'
                    : 'ButtonWithCustomBoardHover '
                }
                size="small"
                variant="contained"
                onClick={() => onView('agenda')}
                sx={{
                  backgroundColor: '#068e87',
                  color: 'white',
                  marginRight: 1,
                  border: '1px solid #e6e6e6 !important',
                }}
              >
                {t('calendario.mainCalendar.lista')}
              </NsButton>
            </Grid>
            <Grid item>
              <NsButton
                className={
                  currentView == 'day'
                    ? 'ButtonWithCustomBoardHoverActive'
                    : 'ButtonWithCustomBoardHover '
                }
                size="small"
                variant="contained"
                onClick={() => onView('day')}
                sx={{
                  backgroundColor: '#068e87',
                  color: 'white',
                  border: '1px solid #e6e6e6 !important',
                }}
              >
                {t('calendario.mainCalendar.giorno')}
              </NsButton>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    );
  };

  const lang = {
    it: {
      date: 'Data',
      time: 'Ora',
      event: 'Evento',
      allDay: 'tutto il giorno',
      week: 'Settimana',
      work_week: 'Settimana lavorativa',
      day: 'Giorno',
      month: 'Mese',
      previous: '< Indietro',
      next: 'Avanti >',
      yesterday: 'Ieri',
      tomorrow: 'Domani',
      today: 'Oggi',
      agenda: 'Lista',
      noEventsInRange: 'Nessun evento in questo periodo',
      showMore: (total: number) => `+${total} altro`,
    },
  };

  const getEventStyle = (event: any, param: any) => {
    let style = {
      display: 'flex',
      justifyContent: 'center',
      backgroundColor: '',
      color: '',
      border: '1px solid #D5D6D7',
      backgroundImage: '',
      backgroundSize: '',
      backgroundRepeat: '',
      backgroundPosition: '',
      outline: 'none',
    };

    switch (event.type) {
      case 'S1':
        style.backgroundColor = colors.V;
        break;
      case 'S2':
        style.backgroundColor = colors.V;
        break;
      case 'S3':
        style.backgroundColor = colors.V;
        break;
      case 'S4':
        style.backgroundColor = colors.V;
        break;
      case 'S5':
        style.backgroundColor = colors.V;
        break;
      case 'S6':
        style.backgroundColor = colors.V;
        break;
      case 'S7':
        style.backgroundColor = colors.VII;
        break;
      case 'SU':
        style.backgroundColor = colors.SU;
        break;
      case 'SCADENZA':
        style.backgroundImage = `url('data:image/svg+xml;utf8,${encodeURIComponent(
          `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">${renderToString(
            <AccessAlarmIcon />
          )}</svg>`
        )}')`;
        style.backgroundRepeat = 'no-repeat';
        style.backgroundColor = 'orange';
        if (param === 'month') {
          style.height = '22px'; // Previously it was 25px
          style.width = '22px'; // Previously it was 25px
          style.position = 'absolute';
          style.top = '1px';
        }
        // style.border = '1px solid black'; If you want to show the 'border' of the clock, uncomment this line
        style.backgroundPosition = 'left center';
        style.backgroundSize = '20px';
        break;
      default:
        style.backgroundColor = 'white';
    }
    return {
      style,
    };
  };

  return (
    <Box sx={{ border: '1px solid #D5D6D7', borderRadius: 1 }}>
      <NsFullPageSpinner isOpen={isLoading} value={100} />
      <Container
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          padding: 2,
          overflow: 'hidden',
        }}
      >
        {currentView === 'month' ? (
          <Calendar
            views={{
              month: true,
              day: true,
              agenda: MyCustomAgenda,
            }}
            localizer={localizer}
            formats={formats}
            events={groupedEventsMonth}
            onView={handleViewChange}
            startAccessor="dataUdienzaStart"
            endAccessor="dataUdienzaEnd"
            messages={lang['it']}
            style={{ height: 600 }}
            components={
              {
                toolbar: CustomToolbar,
                agenda: {
                  event: CustomAgenda,
                },
                day: {
                  event: CustomDay,
                },
              } as any
            }
            onSelectEvent={handleSelectEventMonth}
            eventPropGetter={(event) => getEventStyle(event, currentView)}
            dayPropGetter={dayPropGetterMonth}
          />
        ) : (
          <Calendar
            views={{
              month: true,
              day: true,
              agenda: MyCustomAgenda,
            }}
            localizer={localizer}
            formats={formats}
            events={currentView === 'day' ? allEvents : groupedEventsMonth}
            onView={handleViewChange}
            startAccessor="start"
            endAccessor="end"
            messages={lang['it']}
            style={{ height: 600 }}
            components={
              {
                toolbar: CustomToolbar,
                agenda: {
                  event: CustomAgenda,
                },
                day: {
                  event: CustomDay,
                },
              } as any
            }
            onSelectEvent={handleSelectEvent}
            eventPropGetter={(event) => getEventStyle(event, currentView)}
            dayPropGetter={dayPropGetter}
          />
        )}
        <LegendaSezione />
      </Container>
    </Box>
  );
}
