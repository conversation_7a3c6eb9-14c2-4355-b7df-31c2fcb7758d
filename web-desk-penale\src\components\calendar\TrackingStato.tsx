import { relayQueries_TrackingStatoQuery } from '@/generated/relayQueries_TrackingStatoQuery.graphql';
import CheckCircle from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import Box from '@mui/material/Box';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Stepper from '@mui/material/Stepper';
import Typography from '@mui/material/Typography';
import * as React from 'react';
import { STORE_THEN_NETWORK, useQuery } from 'relay-hooks';
import { TrackingStatoProps } from 'src/interfaces';
import { TrackingStatoSchema } from '../relay/relayQueries';
import {
  formatDate,
  getStatesNamesTracking,
  stepsRelatore,
  stepsPresidente,
} from '../shared/Utils';
import { StatoProvvedimentiEnum } from '../../types/types';
import { useTranslation } from 'next-i18next';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import { useState } from 'react';

const styledStatoSic = {
  background: '#E9D7D7',
  color: '#8E3030',
  fontSize: '15px',
  borderRadius: '5px',
  height: '20px',
  alignItems: 'center',
  padding: '0 7.1px 0 7.1px',
  marginTop: '5px',
  width: '95px',
};

const ROOT_QUERY = TrackingStatoSchema;

export default function TrackingStato({
  id,
  sicCheckStato,
  roles,
  isEstensorePresidente,
}: Readonly<TrackingStatoProps>) {
  const actionVisibleStates = [
    StatoProvvedimentiEnum.BOZZA,
    StatoProvvedimentiEnum.INVIATO_IN_CANCELLERIA_RELATORE,
    // 'MINUTA_DEPOSITATA',
    StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE,
    StatoProvvedimentiEnum.PUBBLICATA,
  ];

  const { t } = useTranslation();
  const [activeStep] = React.useState(0);
  const [loading, setLoading] = React.useState(true);
  const [checkStatoOnSICQuery, setCheckStatoOnSICQuery] = useState<any>();

  let steps = roles === 'PRESIDENTE' ? stepsPresidente : stepsRelatore;

  const { data } = useQuery<relayQueries_TrackingStatoQuery>(
    ROOT_QUERY,
    {
      id,
      roles,
    },
    {
      fetchPolicy: STORE_THEN_NETWORK,
      skip: !id,
    }
  );

  if (isEstensorePresidente) {
    actionVisibleStates.splice(
      actionVisibleStates.indexOf(StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE),
      1
    );
  }

  const [stati, setStati] = React.useState<any[]>([]);
  const [sortedSteps, setSortedSteps] = React.useState<any[]>([]);

  React.useEffect(() => {
    if (data?.provvedimentoTrackingByIdProvvedimento) {
      const filteredSteps = data?.provvedimentoTrackingByIdProvvedimento
        ?.map((status: any) => {
          if (status.stato == StatoProvvedimentiEnum.IN_CODE_FIRMA_REL) {
            return steps.find(
              (step: { stato: string }) => step.stato == StatoProvvedimentiEnum.IN_CODE_FIRMA_REL
            );
          }
          if (status.isRevisione) {
            return steps.find(
              (step: { stato: string }) => step.stato == StatoProvvedimentiEnum.MINUTA_IN_REVISIONE
            );
          }
          const step = steps.find(
            (step: { stato: any }) => step.stato === status.stato
          );

          return step ? step.label : '';
        })
        .filter((label: any) => label !== '');

      setSortedSteps(
        steps.filter((step: { label: any }) =>
          filteredSteps?.includes(step.label)
        )
      );

      const orderStati = Array.from(
        data?.provvedimentoTrackingByIdProvvedimento || []
      ).sort(
        (a: any, b: any) =>
          new Date(a.dateChange).getTime() - new Date(b.dateChange).getTime()
      );
      setStati(orderStati);
      setLoading(false);

      if (id) {
        const checkStatoOnSICQuery1 = data?.checkStatoOnSICQuery;
        setCheckStatoOnSICQuery(checkStatoOnSICQuery1);
      } else {
        setCheckStatoOnSICQuery(sicCheckStato);
      }
    }

    if (!id) {
      setSortedSteps(steps);

      const orderStati = Array.from(
        data?.provvedimentoTrackingByIdProvvedimento || []
      ).sort(
        (a: any, b: any) =>
          new Date(a.dateChange).getTime() - new Date(b.dateChange).getTime()
      );
      setStati(orderStati);
      setCheckStatoOnSICQuery(sicCheckStato);
      setLoading(false);
    }
  }, [data]);

  return (
    <>
      {loading ? (
        <Box sx={{ maxWidth: 400, height: 250, overflowY: 'auto' }}>
          <NsFullPageSpinner value={10} isOpen={loading} />
        </Box>
      ) : (
        <Box sx={{ maxWidth: 400, maxHeight: 500, overflowY: 'auto' }}>
          {checkStatoOnSICQuery?.statoProvvedimento == StatoProvvedimentiEnum.PUBBLICATO_SIC ? (
            <Typography
              variant="h2"
              sx={{
                marginBottom: '16px',
              }}
            >
              <Box component={'span'} sx={styledStatoSic}>
                {t('fascicolo.detailFascicolo.pubblicatoSic')}
              </Box>{' '}
              <Box component={'span'} sx={{ fontSize: '0.7em' }}>
                N. Racc. Gen. :{' '}
                {checkStatoOnSICQuery?.numRaccoltaGeneraleString}
                {', '}
                {formatDate(
                  checkStatoOnSICQuery?.dataPubblicazione,
                  'DD/MM/YYYY'
                )}
              </Box>
            </Typography>
          ) : (
            ''
          )}{' '}
          {checkStatoOnSICQuery?.statoProvvedimento ==
          StatoProvvedimentiEnum.PROVV_DEPOSITATO_SIC ? (
            <Typography
              variant="h2"
              sx={{
                marginBottom: '16px',
              }}
            >
              <Box component={'span'} sx={styledStatoSic}>
                {t('fascicolo.detailFascicolo.provvDepositatoSic')}
              </Box>{' '}
              <Box component={'span'} sx={{ fontSize: '0.7em' }}>
                {formatDate(
                  checkStatoOnSICQuery?.dataPubblicazione,
                  'DD/MM/YYYY'
                )}
              </Box>
            </Typography>
          ) : (
            ''
          )}
          {checkStatoOnSICQuery?.statoProvvedimento ==
          StatoProvvedimentiEnum.MINUTA_DEPOSITATA_SIC ? (
            <Typography
              variant="h2"
              sx={{
                marginBottom: '16px',
              }}
            >
              <Box component={'span'} sx={styledStatoSic}>
                {t('fascicolo.detailFascicolo.depositatoSic')}
              </Box>{' '}
              <Box component={'span'} sx={{ fontSize: '0.7em' }}>
                {formatDate(checkStatoOnSICQuery?.dataMinuta, 'DD/MM/YYYY')}
              </Box>
            </Typography>
          ) : (
            ''
          )}
          {sortedSteps?.length ? (
            <>
              <Stepper activeStep={activeStep} orientation="vertical">
                {stati?.map((stati, index) => (
                  <Step key={`${stati.stato}-${index}`} completed={true}>
                    <StepLabel
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                      }}
                      icon={
                        stati.stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA ||
                        stati.stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE ||
                        stati.stato === StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE ? (
                          <WarningIcon color="error" />
                        ) : (
                          <CheckCircle color="success" />
                        )
                      }
                    >
                      {stati.isRevisione
                        ? getStatesNamesTracking(roles, StatoProvvedimentiEnum.MINUTA_IN_REVISIONE)
                        : stati.stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA &&
                          stati.prevStato === StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE &&
                          roles != 'PRESIDENTE'
                        ? 'Busta rifiutata al Presidente'
                        : stati.stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE
                        ? 'Busta rifiutata al Presidente'
                        : getStatesNamesTracking(roles, stati.stato)}
                      -{' '}
                      {new Date(stati.dateChange).toLocaleString('it-IT', {
                        timeZone:
                          Intl.DateTimeFormat().resolvedOptions().timeZone,
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </StepLabel>
                  </Step>
                ))}
              </Stepper>

              {/* Empty */}
              <Stepper activeStep={activeStep} orientation="vertical">
                {steps.map((step: { stato: string; label: string }) => {
                  const statuses = stati.filter(
                    (status) => status.stato === step.stato
                  );
                  return (
                    <React.Fragment key={step.label}>
                      {!statuses?.length &&
                        actionVisibleStates.includes(step.stato as StatoProvvedimentiEnum) && (
                          <Step key={`${step.label}-grey`} completed={false}>
                            <StepLabel
                              style={{
                                color: 'gray',
                                display: 'flex',
                                alignItems: 'center',
                              }}
                              icon={<CheckCircle color="disabled" />}
                            >
                              {step.label}
                            </StepLabel>
                          </Step>
                        )}
                    </React.Fragment>
                  );
                })}
              </Stepper>
            </>
          ) : (
            <Typography sx={{ mt: 2, mb: 1 }}>
              {t('calendario.trackingStato.minutaDaRedigere')}
            </Typography>
          )}
        </Box>
      )}
    </>
  );
}
