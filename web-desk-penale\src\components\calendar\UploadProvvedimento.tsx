import {
  ProvvedimentiOrigineEnum,
  UploadProvvedimentoMutation,
} from '@/generated/UploadProvvedimentoMutation.graphql';
import ErrorIcon from '@mui/icons-material/Error';
import { Box, CircularProgress, Grid, MenuItem, Typography } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsDragAndDrop,
  NsTextInput,
  useNotifier
} from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import { graphql, useMutation } from 'relay-hooks';
import { UploadProvvedimentoProps } from 'src/interfaces';
import axios from 'axios';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useForm } from 'relay-forms';
import { useConfig } from '../shared/configuration.context';
import { backToUdienza, modalStyle, tipoProvvedimentoToString } from '../shared/Utils';
import { Prosegui } from '../shared/Conferma';
import MainModal from '../shared/MainModal';
import { ProvvedimentiTipoEnum } from '../../types/types';

const mutationImport = graphql`
  mutation UploadProvvedimentoMutation($input: CreateProvvLavorazioneInput!) {
    GenerazioneProvvedimentoCreateMutation(
      createProvvLavorazioneInput: $input
    ) {
      idProvvedimento
    }
  }
`;

export default function UploadProvvedimento({ data, closeModal }: UploadProvvedimentoProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { notify } = useNotifier();
  const { servizi } = useConfig();
  const [mutateChange] = useMutation<UploadProvvedimentoMutation>(mutationImport);

  const [tipoProvvedimento, setTipoProvvedimento] = useState<ProvvedimentiTipoEnum>();
  const [semplificata, setSemplificata] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [fileSelected, setFileSelected] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadFile, setUploadFile] = useState<File[] | null>(null);
  const [uploadCodeDiFirma, setUploadCodeDiFirma] = useState(false);

  useEffect(() => {
    setFileSelected(!!uploadFile && uploadFile.length > 0);
  }, [uploadFile]);

  const { submit } = useForm({
    onSubmit: () => setIsModalOpen(true),
  });

  useEffect(() => {
    const getInitialData = async () => {
      try {
        const response = await axios.get(
          `${servizi}/provvedimento/getTipoProvvedimentoAndSemplificata/${data.id}/${data.nrg}`
        );
        if (response.data) {
          setTipoProvvedimento(response.data.tipoProvvedimento);
          setSemplificata(response.data.semplificata);
        }
      } catch (error) {}
    };
    getInitialData();
  }, [data.id, data.nrg, servizi]);

  const convertDocxToPdf = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    try {
      const response = await axios.post(`${servizi}/provvedimento/convertToPdf`, formData, {
        headers: { 'X-Origin': 'IMPORTAZIONE' },
        responseType: 'blob',
      });
      return new File([response.data], file.name.replace('.docx', '.pdf'), {
        type: 'application/pdf',
      });
    } catch (error) {
      notify({ type: 'error', message: t('calendario.uploadProvvedimento.conversionFailed') });
      throw error;
    }
  };

  const handleUpload = async () => {
    if (!uploadFile?.length) return;

    try {
      setIsModalOpen(false);
      setIsLoading(true);      
      let fileToUpload = uploadFile[0];

      if (fileToUpload.name.toLowerCase().endsWith('.docx')) {
        fileToUpload = await convertDocxToPdf(fileToUpload);
      }

      const formData = new FormData();
      formData.append('files', fileToUpload);
      formData.append('codeFirma', JSON.stringify(uploadCodeDiFirma));

      let idProvvedimento: string;

      if (data.duplicato) {
        idProvvedimento = data.idProvvedimento;
      } else {
        if (!tipoProvvedimento) {
          throw new Error('Tipo provvedimento is required');
        }

        const input = {
          nrg: parseInt(data.ricorso.nrg),
          origine: 'LOCALE' as ProvvedimentiOrigineEnum,
          idUdienza: parseInt(data.id),
          allegatoOscurato: false,
          argsProvvedimento: {
            tipologiaProvvedimento: tipoProvvedimento,
            anRuolo: data.ricorso.anno,
            numRuolo: data.ricorso.numero,
          },
        };

        const result = await mutateChange({ variables: { input } });
        idProvvedimento = result.GenerazioneProvvedimentoCreateMutation.idProvvedimento;
      }

      await axios.post(
        `${servizi}/provvedimento/saveFileLavorazione/${idProvvedimento}`,
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' } }
      );

      if (uploadCodeDiFirma) {
        notify({ type: 'success', message: t('provvedimenti.createProvvCoda') });
        backToUdienza(router, idProvvedimento, data.nrg);
      } else {
        notify({ type: 'success', message: t('calendario.uploadProvvedimento.fileCaricatoConSuccesso') });
        router.push({
          pathname: '/firmadeposita',
          query: {
            idUdienza: data.id,
            params: data.nrg,
            idProvvedimento,
            uploadedFile: true,
          },
        });
      }
    } catch (error) {
      console.error(error);
      notify({ type: 'error', message: t('calendario.uploadProvvedimento.uploadFailed') });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <NsFullPageSpinner isOpen={isLoading} value={1} />
      <form onSubmit={submit}>
        <Grid container sx={{ display: 'flex', flexWrap: 'wrap' }}>
          <Grid item xs={12}>
            <Box sx={{ mb: 2, bgcolor: '#fef1e8', display: 'flex', alignItems: 'center' }}>
              <ErrorIcon sx={{ color: '#f26a1c', ml: 1 }} />
              <Typography variant="h6" sx={{ mb: 2, color: '#f26a1c', ml: 3, mt: 2 }}>
                {t('fascicolo.uploadAvviso')}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <NsTextInput
              name="tipologiaProvvedimento"
              defaultValue={tipoProvvedimentoToString(tipoProvvedimento!)}
              disabled
              label={t('fascicolo.tipologiaProvvedimento')}
            >
              <MenuItem value={tipoProvvedimento}>{tipoProvvedimento}</MenuItem>
            </NsTextInput>

            {semplificata && (
              <Typography mt={2} variant="h3">
                {t('calendario.uploadProvvedimento.motivazioneSemplificata')}
              </Typography>
            )}
          </Grid>

            <Grid item xs={12}>
            <NsDragAndDrop
              name="uploadFile"
              value={data?.uploadFile}
              displayForm
              buttonStatus
              onFileLoaded={() => setFileSelected(true)}
              onChange={(files) => {
                // If we get an empty array, it means a file was selected but rejected
                if (Array.isArray(files) && files.length === 0) {
                  notify({
                    type: 'warning',
                    message: t('calendario.uploadProvvedimento.fileNonValido'),
                  });
                  setUploadFile(null);
                  return;
                }

                // Handle valid files
                if (files && files.length > 0) {
                  setUploadFile(Array.isArray(files) ? files : [files]);
                } else {
                  setUploadFile(null);
                }
              }}
              multiple={false}
              validationFile={{
              'application/pdf': ['.pdf'],
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
              }}
            />
            </Grid>

          <Grid item xs={12}>
            <Box sx={{ mt: 2, mb: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <NsButton sx={{ mb: 1 }} onClick={closeModal} variant="outlined" color="primary">
                {t('fascicolo.annulla')}
              </NsButton>

              <NsButton
                sx={{ mt: 1 }}
                variant="contained"
                color="primary"
                type="submit"
                disabled={!fileSelected}
                onClick={() => setUploadCodeDiFirma(true)}
              >
                {t('calendario.uploadProvvedimento.inserisciInCodaDiFirma')}
              </NsButton>

              <NsButton
                sx={{ mt: 1 }}
                variant="contained"
                color="primary"
                type="submit"
                disabled={!fileSelected}
              >
                {t('fascicolo.firmaDeposita')}
              </NsButton>

            </Box>
          </Grid>
        </Grid>
      </form>      

      <MainModal
        modal={isModalOpen}
        closeModal={() => setIsModalOpen(false)}
        style={modalStyle}
        title={t('pages.libero.provvedimentoNonOscurato') as string}
        body={
          <Prosegui
            closeModal={() => setIsModalOpen(false)}
            body={t('pages.libero.confermaProseguiSenzaSott')}
            confirm={handleUpload}
          />
        }
      />      

      <style jsx global>{`
        .css-193tv05 { height: 100%; overflow: auto; }
      `}</style>
    </>
  );
}
