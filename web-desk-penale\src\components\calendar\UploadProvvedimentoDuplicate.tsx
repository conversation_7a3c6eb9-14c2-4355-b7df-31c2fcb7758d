import ErrorIcon from '@mui/icons-material/Error';
import { Box, Grid, MenuItem, Typography, useTheme } from '@mui/material';
import {
  NsButton,
  NsDragAndDrop,
  NsTextInput,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import { UploadProvvedimentoDuplicateProps } from 'src/interfaces';

import axios from 'axios';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useForm } from 'relay-forms';
import { useConfig } from '../shared/configuration.context';
import { ProvvedimentiTipoEnum } from '../../types/types';

export default function UploadProvvedimentoDuplicate({
  data,
  idProvvOld,
  closeModal,
}: UploadProvvedimentoDuplicateProps) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const router = useRouter();

  const { notify } = useNotifier();

  const [tipoProvvedimento, setTipoProvvedimento] =
    useState<ProvvedimentiTipoEnum>(data.tipoProvvedimento);
  const [semplificata, setSemplificata] = useState<boolean>(false);

  const { submit, reset } = useForm<any>({
    onSubmit: (uploadedFile: any) => {
      callUploadFile(uploadedFile.uploadFile, data.id, idProvvOld);
    },
  });

  const [selectedValue, setSelectedValue] = useState('');

  const [fileSelected, setFileSelected] = useState(false);

  const tipoProvvedimentoToString = (
    tipoProvvedimento: ProvvedimentiTipoEnum
  ) => {
    switch (tipoProvvedimento) {
      case ProvvedimentiTipoEnum.ORDINANZA:
        return 'MINUTA_ORDINANZA';
      case ProvvedimentiTipoEnum.SENTENZA:
        return 'MINUTA_SENTENZA';
      default:
        return tipoProvvedimento;
    }
  };

  const { servizi } = useConfig();

  const serviceUrl3 = `${servizi}`;

  const serviceUrl = `${servizi}/provvedimento/saveFileLavorazione/`;

  const serviceUrl2 = `${servizi}/provvedimento/getTipoProvvedimentoAndSemplificata/`;

  const getTestiIniziali = async () => {
    try {
      const response = await axios.get(serviceUrl2 + data.id + '/' + data.nrg);
      if (response.data) {
        setTipoProvvedimento(response.data.tipoProvvedimento);
        setSemplificata(response.data.semplificata);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const callUploadFile = (file: any, idUdienza: string, idProvvOld: string) => {
    const formData = new FormData();
    formData.append('files', file[0]);
    return axios
      .get(serviceUrl3 + '/provvedimento/duplicate/' + idProvvOld)
      .then(async (response) => {
        notify({
          message: t(
            'calendar.uploadProvvedimentoDuplicate.provvedimentoDuplicatoConSuccesso'
          ),
          type: 'success',
        });
        try {
          const result = await axios.post(
            serviceUrl + `${response.data}`,
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }
          );
          notify({
            type: 'success',
            message: t(
              'calendar.uploadProvvedimentoDuplicate.fileCaricatoConSuccesso'
            ),
          });
          router.push({
            pathname: '/firmadeposita',
            query: {
              idUdienza,
              params: data.nrg,
              idProvvedimento: response.data,
              uploadedFile: true,
            },
          });
          return true;
        } catch (err) {
          console.error(err);
          notify({
            type: 'error',
            message: 'Errore nella duplicazione del provvedimento',
          });
          return false;
        }
      })
      .catch((err) => {
        console.error(err);
        notify({
          type: 'error',
          message: 'Errore nel caricamento del file',
        });
        return false;
      });
  };

  useEffect(() => {
    getTestiIniziali();
  }, []);
  return (
    <>
      <form onSubmit={submit}>
        <Grid container sx={{ display: 'flex', flexWrap: 'wrap' }}>
          <Grid item xs={12}>
            <Box
              sx={{
                mb: 2,
                bgcolor: '#fef1e8',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <ErrorIcon sx={{ color: '#f26a1c', ml: 1 }} />
              <Typography
                variant="h6"
                sx={{ mb: 2, color: '#f26a1c', ml: 3, mt: 2 }}
              >
                {t('fascicolo.uploadAvviso')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            {
              <NsTextInput
                name="tipologiaProvvedimento"
                defaultValue={tipoProvvedimentoToString(tipoProvvedimento)}
                disabled={true}
                label={t('fascicolo.tipologiaProvvedimento')}
              >
                <MenuItem
                  key={1}
                  value={tipoProvvedimentoToString(tipoProvvedimento)}
                >
                  {tipoProvvedimentoToString(tipoProvvedimento)}
                </MenuItem>
              </NsTextInput>
            }
            {semplificata && (
              <Typography mt={2} variant="h3">
                {t(
                  'calendario.uploadProvvedimentoDuplicate.motivazioneSemplificata'
                )}
              </Typography>
            )}
          </Grid>
          <Grid item xs={12}>
            <NsDragAndDrop
              name="uploadFile"
              defaultValue={data && data.uploadFile}
              displayForm={true}
              buttonStatus={true}
              onFileLoaded={() => {
                setFileSelected(true);
              }}
              multiple={false}
              // disabled={false}
              validationFile={{ 'text/html': ['.pdf', '.docx'] }}
            />
          </Grid>
          <Grid item xs={12}>
            <Box
              sx={{
                mt: 2,
                mb: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              <NsButton
                sx={{ mb: 1 }}
                onClick={closeModal}
                variant="outlined"
                color="primary"
                type="button"
              >
                {t('fascicolo.annulla')}
              </NsButton>

              <NsButton
                sx={{ mt: 1 }}
                variant="contained"
                color="primary"
                type="submit"
                disabled={!fileSelected}
              >
                {t('fascicolo.firmaDeposita')}
              </NsButton>
              <Box
                sx={{
                  mt: 2,
                  mb: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              ></Box>
            </Box>
          </Grid>
        </Grid>
      </form>
    </>
  );
}
