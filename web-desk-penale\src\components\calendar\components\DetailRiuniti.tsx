import {Grid, Typography, useTheme} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {DetailRiunitiProps} from 'src/interfaces';
import {GetRiunitiDetailsSchema} from "../../relay/relayQueriesRiunitDetails";
import {STORE_OR_NETWORK, useQuery} from "relay-hooks";
import {
  relayQueriesRiunitDetails_GetRiunitiDetailsQuery
} from "@/generated/relayQueriesRiunitDetails_GetRiunitiDetailsQuery.graphql";
import {Box} from "@mui/system";

export const ROOT_QUERY = GetRiunitiDetailsSchema;

export default function DetailRiuniti({ idRicorsoUdienza}: DetailRiunitiProps) {
  const scrollBar = {
    overflow: 'auto',
    maxHeight: 400,
  };
  const { data, isLoading } = useQuery<relayQueriesRiunitDetails_GetRiunitiDetailsQuery>(
    ROOT_QUERY,
    {
      idRicUdin: Number(idRicorsoUdienza),
    },
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );
  if (isLoading) {
    return <>Loading...</>;
  }
  const caseData = data?.getRiuntiByIdRicUdien;

  return <>
    <Grid container>
      <Grid item xs={12}>
        <Box sx={scrollBar}>
          <Grid item p={2} xs={12} border={1}>
            {caseData?.map((caseDate: any, index: number) => <>
              <Typography key={'riu-' + index} variant="h4" p={2}>
                + {caseDate.numero}/{caseDate.anno} (R)
              </Typography>{' '}
            </>)}
          </Grid>
        </Box>
      </Grid>
    </Grid>
  </>
}
