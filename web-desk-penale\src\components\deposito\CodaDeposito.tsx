import { useTheme } from '@emotion/react';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { Box, Checkbox, Grid, TableCell, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import RelayTable from '../shared/RelayTable';
import { azioniButton } from '../shared/Utils';

export default function CodaDeposito({ deleteCoda, depositi, ruolo }: any) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const azioni = azioniButton();
  const router = useRouter();
  const [selected, setSelected] = useState<any[]>([]);

  const handleFirma = () => {
    // Salva gli id selezionati nello sessionStorage per evitare query-string troppo lunghe
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('depositiSelected', JSON.stringify(selected));
    }

    if (ruolo === 'ESTENSORE') {
      return router.push({
        pathname: '/deposito/massivo',
        query: {
          calendar: 'true',
        },
      });
    }
    router.push({
      pathname: '/deposito/massivo',
    });
  };

  const renderAzioni = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box display="flex" justifyContent="space-between">
          {azioni.getIcons('delete').map((icon: any) => (
            <Box
              key={icon.id}
              sx={{ ...azioni.iconBoxStyles, background: icon.bgColor }}
              onClick={() => deleteCoda(row?.idProvv)}
            >
              {icon.icon}
            </Box>
          ))}
        </Box>
      </TableCell>
    );
  };

  const renderUdienza = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box /*sx={{color: '#308A7D', textDecoration: 'underline'}}*/>
          {row.udienza}
        </Box>
      </TableCell>
    );
  };

  const handleClick = (_: React.MouseEvent<unknown>, index: number) => {
    const array = selected.includes(index)
      ? selected.filter((i) => i !== index)
      : [...selected, index];
    setSelected([...array]);
  };

  const handleSelectAllClick = (
    event: React.ChangeEvent<HTMLInputElement>,
    rows: any
  ) => {
    if (event.target.checked) {
      const newSelected = rows.map((r: any) => r.idProvv);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const renderCheckbox = (cell: any, row: any, index: any) => {
    const isSelected = selected.includes(row.idProvv);
    return (
      <TableCell key={cell.id} sx={{ border: theme.custom.borders[0] }}>
        <Checkbox
          color="primary"
          checked={isSelected}
          onClick={(event) => handleClick(event, row.idProvv)}
        />
      </TableCell>
    );
  };

  const renderHeadCheckbox = (name: string, rows: any) => {
    return (
      <Checkbox
        color="primary"
        checked={selected.length === depositi.length}
        onChange={(event) => handleSelectAllClick(event, rows)}
        inputProps={{
          'aria-label': 'select all',
        }}
      />
    );
  };

  const columns: any[] = [
    {
      id: 'checked',
      minWidth: 170,
      label: '',
      render: renderCheckbox,
      renderHeadCell: renderHeadCheckbox,
    },
    {
      id: 'numOrdine',
      minWidth: 170,
      label: t('deposito.codaDeposito.ordine') as string,
      align: 'left',
    },
    {
      id: 'nrg',
      align: 'left',
      label: t('deposito.codaDeposito.nrg') as string,
      minWidth: 170,
    },
    {
      id: 'dataUdienza',
      minWidth: 170,
      label: t('deposito.codaDeposito.udienza'),
      align: 'left',
      render: renderUdienza,
    },
    {
      id: 'azioni',
      minWidth: 170,
      label: t('deposito.codaDeposito.azioni') as string,
      align: 'center',
      render: renderAzioni,
    },
  ];

  return (
    <Grid container>
      <Grid item display="flex" justifyItems="center" xs={12}>
        <CheckCircleIcon sx={{ marginRight: '10px' }} color="success" />
        <Typography variant="h2">
          {t('deposito.codaDeposito.codaDiDeposito')}
        </Typography>
      </Grid>
      <Grid item xs={12} mt={3}>
        <Box sx={{ overflow: 'auto' }}>
          <RelayTable rows={depositi} columns={columns} />
        </Box>
        <NsButton
          size="small"
          onClick={handleFirma}
          variant="contained"
          disabled={selected.length === 0}
        >
          {t('deposito.codaDeposito.procediFirma')}
        </NsButton>
      </Grid>
    </Grid>
  );
}
