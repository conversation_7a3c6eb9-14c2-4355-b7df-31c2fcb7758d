import { relayQueries_CodaDepositoSchemaQuery } from '@/generated/relayQueries_CodaDepositoSchemaQuery.graphql';
import { PublicClientApplication } from '@azure/msal-browser';
import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier,
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { NETWORK_ONLY, useMutation, useQuery } from 'relay-hooks';
import { CodaDepositoProps } from 'src/interfaces';
import FirmaDeposita from '../fascicolo/FirmaDeposita';
import {
  CodaDeleteDepositoMutation,
  CodaDepositoSchema,
} from '../relay/relayQueries';
import MainModal from '../shared/MainModal';
import { formatDate } from '../shared/Utils';
import { useConfig } from '../shared/configuration.context';
import MinutaOrdinanza from './MinutaOrdinanza';
import { relayQueries_CodaDeleteDepositoMutation } from '@/generated/relayQueries_CodaDeleteDepositoMutation.graphql';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 2,
};

const ROOT_QUERY = CodaDepositoSchema;
const ROOT_DELETE = CodaDeleteDepositoMutation;

type Provvedimento = {
  idProvv: string;
  stato: string;
};

export default function DepositoMassivo() {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const [depositi, setDepositi] = useState<CodaDepositoProps[]>([]);
  const [finalDepositiSelected, setFinalDepositiSelected] =
    useState<string[]>([]);
  const { notify } = useNotifier();
  const { servizi } = useConfig();
  const router = useRouter();

  const { calendar, isImportaMassivo } = router.query;
  const [udienza, setUdienza] = useState<String>();
  const [isLoadingDownload, setIsLoadingDownload] = useState<boolean>(false);
  const [isLoadingNavigation, setIsLoadingNavigation] =
    useState<boolean>(false);

  const [conformitaChecked, setConformitaChecked] = useState<string[]>([]);

  const [risultatoDeposito, setRisultatoDeposito] = useState<any>();

  const handleConformitaChange = (id: string, checked: boolean) => {
    if (checked) {
      setConformitaChecked((prev) => [...prev, id]);
    } else {
      setConformitaChecked((prev) => prev.filter((item) => item !== id));
    }
  };

  const toggleAllCheckboxes = () => {
    if (checkedMassivo === true)
      setConformitaChecked(depositi.map(d => d.idProvv));
    else
      setConformitaChecked([]);
  }

  const [checkedMassivo, setCheckedMassivo] = useState(false);

  useEffect(() => {
    toggleAllCheckboxes();
  }, [checkedMassivo]);

  const handleCheckboxMassivoChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCheckedMassivo(event.target.checked);
  };

  // Carica gli id selezionati da sessionStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = sessionStorage.getItem('depositiSelected');
      if (stored) {
        try {
          setFinalDepositiSelected(JSON.parse(stored));
        } catch (_) {
          setFinalDepositiSelected([]);
        }
        sessionStorage.removeItem('depositiSelected');
      }
    }
  }, []);

  const { data, isLoading } = useQuery<relayQueries_CodaDepositoSchemaQuery>(
    ROOT_QUERY,
    { role: calendar === 'true' ? 'ESTENSORE' : 'PRESIDENTE' },
    { fetchPolicy: NETWORK_ONLY }
  );

  const [deletebyidprovv] =
    useMutation<relayQueries_CodaDeleteDepositoMutation>(ROOT_DELETE);

  const closeModal = () => {
    setModalProps({ ...modalProps, modal: false });
  };

  const [modalProps, setModalProps] = useState({
    modal: false,
    closeModal,
    style,
    title: t('deposito.depositoMassivo.firmaEDepositoMassivo'),
    body: (
      <FirmaDeposita
        type={'depositoMassivo'}
        closeModal={closeModal}
        daDepositare={conformitaChecked}
        provvedimentiResult={(provvedimenti) =>
          setRisultatoDeposito(provvedimenti)
        }
      />
    ),
  });

  useEffect(() => {
    const codeDepositoByCf = data?.codeDepositoByCf || [];
    if (codeDepositoByCf.length > 0 && finalDepositiSelected.length > 0) {
      const mapped = codeDepositoByCf
        .filter((e) => finalDepositiSelected.includes(e.idProvv))
        .map((code) => ({
          idProvv: code.idProvv,
          numOrdine: code.codeDepositoDto?.numOrdine,
          nrg: code.codeDepositoDto?.nrg,
          dataUdienza: formatDate(
            code.codeDepositoDto?.dataUdienza,
            'DD/MM/YYYY'
          ),
          udienza: code.codeDepositoDto?.udienza,
          tipoProvvedimento: code.codeDepositoDto?.tipoProvvedimento,
          stato: null,
        }));
      setDepositi(mapped);
      if (mapped != null && mapped.length > 0 && mapped[0].udienza != null)
        setUdienza(mapped[0].udienza);
    }
  }, [data, finalDepositiSelected]);

  const updateDepositi = (provvedimenti: Provvedimento[]) => {
    const newDepositi = depositi.map((deposito) => {
      const provv = provvedimenti.find((p) => p.idProvv === deposito.idProvv);
      if (provv) {
        return { ...deposito, stato: provv.stato };
      }
      return deposito;
    });
    setDepositi([...newDepositi]);
    const failed = !!newDepositi.find(
      (depositi) => depositi.stato !== 'LAVORATO'
    );
    if (provvedimenti.length === 0) {
      notify({
        type: 'error',
        message: t('deposito.depositoMassivo.codaDiFirmaScaduta'),
      });
    }

    if (!failed) {
      notify({
        type: 'success',
        message: t('deposito.depositoMassivo.firmaEDepositoOK'),
      });
    }
    closeModal();
  };

  const firmaEdeposita = async () => {
    setModalProps({
      ...modalProps,
      modal: true,
      body: (
        <FirmaDeposita
          type={'depositoMassivo'}
          closeModal={closeModal}
          daDepositare={conformitaChecked}
          provvedimentiResult={(provvedimenti) => updateDepositi(provvedimenti)}
        />
      ),
    });
  };

  const publicAzureClient = async (msalConfig: any) => {
    return new PublicClientApplication(msalConfig);
  };

  const downloadTutti = async () => {
    setIsLoadingDownload(true);
    const { data } = await axios.post<Blob>(
      `${servizi}/presidente/downloadCodaDeposito`,
      { daScaricare: depositi.map((d) => d.idProvv) },
      { responseType: 'blob' }
    );
    const filename = 'codaDeposito.zip';
    const binaryData = [];
    binaryData.push(data);
    const url = window.URL.createObjectURL(
      new Blob(binaryData, { type: 'application/zip' })
    );
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    setIsLoadingDownload(false);
    notify({
      message: t('deposito.depositoMassivo.downloadCompletato'),
      type: 'success',
    });
  };

  const deleteDeposito = async (idProvv: string) => {
    const allDepositi = depositi.filter(
      (deposito) => deposito.idProvv != idProvv
    );
    await deletebyidprovv({
      variables: {
        input: idProvv,
      },
    })
      .then((res) => {
        setDepositi([...allDepositi]);
        setConformitaChecked((prev) => prev.filter((item) => item !== idProvv));

        notify({
          type: 'success',
          message: t('deposito.depositoMassivo.eliminaDepositoCompletato'),
        });
      })
      .catch((err) => {
        notify({
          type: 'error',
          message: "Errore nell'eliminazione",
        });
      });
  };

  // funzione che mi serve per ritornare al calendario una volta che clicco su esci da coda deposito senza firmare
  const navigateToRoute = (route: string) => {
    setIsLoadingNavigation(true); // Set isLoadingNavigation to true
    router.push({
      pathname: route,
    });
  };
  const goBackTo = () => {
    if (isImportaMassivo == 'true') {
      navigateToRoute('/calendario'); // Navigate to calendario route
    } else {
      router.back();
    }
  };

  return (
    <>
      <Grid container p={3}>
        <Box display="flex" alignItems="center">
          <Link
            style={{ textDecoration: 'none' }}
            href={calendar === 'true' ? '/calendario' : '/scrivania'}
          >
            <Typography color="#4F7479" variant="h2">
              {calendar === 'true'
                ? t('deposito.depositoMassivo.calendario')
                : t('deposito.depositoMassivo.scrivania')}{' '}
              {'>'}{' '}
            </Typography>
          </Link>{' '}
          <Typography ml={1} variant="h5">
            {t('deposito.depositoMassivo.firmaDepositoMassivo')}
          </Typography>
        </Box>
        <Grid item mt={2} mb={2} xs={12}>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
          >
            <Box>
              <Typography variant="h5">
                {t('deposito.depositoMassivo.notaFirmaDepositoMassivo')}
              </Typography>
              <Box>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={checkedMassivo}
                      onChange={handleCheckboxMassivoChange}
                    />
                  }
                  label={t(
                    'deposito.depositoMassivo.conformitaVerificataMassivo'
                  )}
                />
              </Box>
            </Box>

            <NsButton
              variant="contained"
              disabled={depositi?.length <= 0}
              onClick={downloadTutti}
            >
              {t('deposito.depositoMassivo.scaricaTutti')}
            </NsButton>
          </Box>
        </Grid>
        <Grid item xs={12}>
          {depositi.length > 0 ? (
            depositi.map((deposito: any) => (
              <MinutaOrdinanza
                deleteDeposito={(idProvv) => deleteDeposito(idProvv)}
                deposito={deposito}
                key={deposito.idProvv}
                onConformitaChange={handleConformitaChange}
                depositato={(idProvv, stato) => {
                  const newDepositi = depositi.map((d) => {
                    if (d.idProvv === idProvv) {
                      return { ...d, stato: stato };
                    }
                    return d;
                  });
                  setDepositi(newDepositi);
                  handleConformitaChange(idProvv, false);
                }}
                checked={checkedMassivo}
              />
            ))
          ) : (
            <Typography variant="h4">
              {t('deposito.depositoMassivo.nessunDeposito')}{' '}
            </Typography>
          )}
        </Grid>
        <Grid mt={2} item xs={12}>
          <Box display="flex" justifyContent="space-between">
            <NsButton variant="contained" onClick={() => goBackTo()}>
              {' '}
              {t('deposito.depositoMassivo.esciDallaCoda')}
            </NsButton>
            <NsButton
              onClick={firmaEdeposita}
              variant="contained"
              disabled={conformitaChecked.length === 0}
            >
              {t('deposito.depositoMassivo.firmaDeposita')}
            </NsButton>
          </Box>
        </Grid>
      </Grid>
      <MainModal {...modalProps} />
      {isLoading && <NsFullPageSpinner isOpen={true} value={1} />}
      {isLoadingDownload && <NsFullPageSpinner isOpen={true} value={1} />}
      {isLoadingNavigation && (
        <Box position={'fixed'} top={0} left={0} width={'100%'} height={'100%'} zIndex={1500}>
          <NsFullPageSpinner isOpen={true} value={1} />
        </Box>
      )}
    </>
  );
}
