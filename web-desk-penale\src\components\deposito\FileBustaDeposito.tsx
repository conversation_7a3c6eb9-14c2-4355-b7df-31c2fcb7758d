import DownloadIcon from '@mui/icons-material/Download';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import { Box, Grid, Typography, useTheme } from '@mui/material';
import { NsFullPageSpinner, NsTooltip, useNotifier } from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useConfig } from '../shared/configuration.context';

export default function FileBustaDeposito({ provvedimentiDaFirmare }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const { servizi } = useConfig();

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const serviceUrl = `${servizi}`;

  const previewPdfByIdCat = (provv: any) => {
    const idCat = provv.idCategoria;
    setIsLoading(true);
    axios
      .get(serviceUrl + '/provvedimento/downloadByIdCat/' + idCat, {
        responseType: 'blob',
      })
      .then((response: any) => {
        const tipoFile = provv.tipoFile;
        const pdfUrl = window.URL.createObjectURL(response.data);
        window.open(pdfUrl, '_blank');
      })
      .catch((error: any) => {
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  const downloadPdfByIdCat = (provv: any) => {
    const idCat = provv.idCategoria;
    setIsLoading(true);
    axios
      .get(serviceUrl + '/provvedimento/downloadByIdCat/' + idCat, {
        responseType: 'blob',
      })
      .then((response: any) => {
        const tipoFile = provv.tipoFile;
        const nomeFile = provv.nomeFile;
        const pdfBlob = new Blob([response.data], {
          type: 'application/' + tipoFile,
        });
        const downloadUrl = window.URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.download = nomeFile;
        link.href = downloadUrl;
        link.click();
      })
      .then(() => {
        notify({
          message:
            t('deposito.fileBustaDeposito.downloadDelFile') +
            ' ' +
            provv.tipoFile +
            ' ' +
            t('deposito.fileBustaDeposito.avvenutoConSuccesso'),
          type: 'success',
        });
      })
      .catch((error: any) => {
        notify({
          message:
            t('deposito.fileBustaDeposito.erroreNelDownload') + provv.tipoFile,
          type: 'error',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <>
      {provvedimentiDaFirmare
        ?.map((provv: any, i: number) => {
          return (
            <Grid key={i} item xs={12} md={6}>
              <Grid
                container
                mt={3}
                justifyContent="space-between"
                alignItems="center"
                border={theme.custom.borders[0]}
                p={2}
                xs={11.9}
                item
              >
                <Box display="flex" alignItems="center">
                  <InsertDriveFileIcon color="primary" />
                  <Typography ml={2} mr={2} variant="h5">
                    {provv.nomeFile}
                  </Typography>
                  {provv.tipoFile === 'pdf' && provv.oscurato && (
                    <Typography
                      sx={{
                        ...theme.custom.atto,
                        ...theme.custom.atto.secondary,
                      }}
                    >
                      {t('deposito.fileBustaDeposito.oscurato')}
                    </Typography>
                  )}
                  {provv.tipoFile === 'pdf' && !provv.oscurato && (
                    <Typography
                      sx={{
                        ...theme.custom.atto,
                        ...theme.custom.atto.secondary,
                      }}
                    >
                      {t('deposito.fileBustaDeposito.nonOscurato')}
                    </Typography>
                  )}
                </Box>
                <Box display="flex">
                  {provv.tipoFile === 'pdf' && (
                    <Box
                      mr={1}
                      ml={2}
                      sx={theme.custom.options}
                      display={{ xs: 'none', md: 'flex' }}
                      onClick={() => previewPdfByIdCat(provv)}
                      style={{ cursor: 'pointer' }}
                    >
                      <NsTooltip
                        title={t('deposito.fileBustaDeposito.anteprima')}
                        icon={<RemoveRedEyeIcon fontSize="small" />}
                      />
                    </Box>
                  )}
                  <Box
                    sx={theme.custom.options}
                    onClick={() => downloadPdfByIdCat(provv)}
                    style={{ cursor: 'pointer' }}
                  >
                    <NsTooltip
                      title={`Download ${provv.tipoFile.toUpperCase()}`}
                      icon={<DownloadIcon fontSize="small" />}
                    />
                  </Box>
                </Box>
              </Grid>
            </Grid>
          );
        })}
      {isLoading && <NsFullPageSpinner isOpen={true} value={1} />}
    </>
  );
}
