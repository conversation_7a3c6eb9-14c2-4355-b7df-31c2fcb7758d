import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { Box, Checkbox, Grid, Typography, useTheme } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CodaDepositoProps } from 'src/interfaces';
import { useConfig } from '../shared/configuration.context';
import FileBustaDeposito from './FileBustaDeposito';

const deleteCss = {
  background: '#F9E1DD',
  height: ' 40px',
  width: '40px',
  alignItems: 'center',
  display: 'flex',
  justifyContent: 'center',
  marginLeft: '10px',
  cursor: 'pointer',
};

interface MinutaOrdinanzaProps {
  deposito: CodaDepositoProps;
  deleteDeposito: (id: string) => void;
  onConformitaChange: (id: string, checked: boolean) => void;
  depositato: (id: string, stato: string) => void;
  checkboxDisabled?: boolean | undefined;
  checked?: boolean | undefined;
}

export default function MinutaOrdinanza({
  deposito,
  deleteDeposito,
  onConformitaChange,
  depositato,
  checked,
}: MinutaOrdinanzaProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const { servizi } = useConfig();
  const { notify } = useNotifier();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [files, setFiles] = useState<any>([]);

  const [isChecked, setIsChecked] = useState<boolean>(false);
  useEffect(() => {
    setIsChecked(checked === true);
  }, [checked]);

  useEffect(() => {
    getProvvedimenti();
  }, []);

  const handleConformitaChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    onConformitaChange(deposito.idProvv, event.target.checked);
    setIsChecked(!isChecked);
  };

  const getProvvedimenti = async () => {
    try {
      setIsLoading(true);
      const { data } = await axios.get(
        `${servizi}/provvedimento/getProvvedimentiDaFirmare/${deposito.idProvv}`
      );
      setFiles([...data]);
    } catch (e: any) {
      notify({
        type: 'error',
        message: t('deposito.minutaDiOrdinanza.erroreNelRecuperoFile'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const downloadProvv = async () => {
    setIsLoading(true);
    try {
      await Promise.all(files.map(downloadPdfByIdCat));
      notify({
        message: t('deposito.minutaDiOrdinanza.downloadCompletato'),
        type: 'success',
      });
    } catch (error) {
      notify({
        message: t('deposito.minutaDiOrdinanza.erroreNelDownload'),
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const downloadPdfByIdCat = async (provv: any) => {
    const idCat = provv.idCategoria;
    const response = await axios.get(
      `${servizi}/provvedimento/downloadByIdCat/${idCat}`,
      {
        responseType: 'blob',
      }
    );

    const pdfBlob = new Blob([response.data], {
      type: 'application/' + provv.tipoFile,
    });
    const downloadUrl = window.URL.createObjectURL(pdfBlob);
    const link = document.createElement('a');
    link.download = provv.nomeFile;
    link.href = downloadUrl;
    link.click();
  };

  useEffect(() => {
    if (
      deposito.stato === 'LAVORATO' ||
      deposito.stato === 'PROVV_GIA_FIRMATO_DEPOSITATO'
    ) {
      depositato(deposito.idProvv, deposito.stato);
    }
  }, [deposito.stato]);

  // Stati che indicano successo
  const successStates = ['LAVORATO'];

  // Stati che indicano warning (errori non bloccanti)
  const warningStates = ['PDF_CORRUPTED_SIGN_ERROR', 'FILE_NOT_FOUND', 'PROVV_LOCKED'];

  // Determina il tipo di stato
  const getStateType = (stato: string) => {
    if (successStates.includes(stato)) return 'success';
    if (warningStates.includes(stato)) return 'warning';
    return 'error';
  };

  const getBackgroundColor = (stato: string) => {
    const stateType = getStateType(stato);
    switch (stateType) {
      case 'success':
        return theme.custom.atto.primary.background; // Verde
      case 'warning':
        return theme.custom.atto.warning.background; // Arancione per warning
      case 'error':
      default:
        return theme.custom.atto.red.background; // Rosso per errori
    }
  };

  const tooltipText = (() => {
    if (!deposito.stato) return t('deposito.minutaDiOrdinanza.tooltipUndefined');

    // Caso specifico per PDF corrotto
    if (deposito.stato === 'PDF_CORRUPTED_SIGN_ERROR') {
      return t('deposito.minutaDiOrdinanza.tooltipPdfCorrupted');
    }

    const stateType = getStateType(deposito.stato);
    switch (stateType) {
      case 'success':
        return t('deposito.minutaDiOrdinanza.tooltipSuccess');
      case 'warning':
        return t('deposito.minutaDiOrdinanza.tooltipWarning');
      case 'error':
      default:
        return t('deposito.minutaDiOrdinanza.tooltipError');
    }
  })();

  return (
    <>
      <Grid item p={2} mt={2} border={theme.custom.borders[0]} xs={12}>
        <Box display="flex" justifyContent="space-between">
          <Typography variant="h1">
            {deposito.tipoProvvedimento?.replace('_', ' ')}{' '}
            {t('deposito.minutaDiOrdinanza.fascicolo')} {deposito.nrg} -{' '}
            {t('deposito.minutaDiOrdinanza.udienza')}: {deposito.udienza}
          </Typography>
          <Box display="flex" alignItems="center">
            {deposito.stato && (
              <NsTooltip
                title={tooltipText}
                icon={
                  <Typography
                    variant="h1"
                    sx={{
                      ...theme.custom.atto,
                      marginRight: '10px',
                      background: deposito.stato ? getBackgroundColor(deposito.stato) : theme.custom.atto.red.background,
                      color: 'white',
                    }}
                  >
                    {t(`server.errorCode.${deposito.stato}`)}
                  </Typography>
                }
              />
            )}

            <NsButton size="small" variant="contained" onClick={downloadProvv}>
              {t('deposito.minutaDiOrdinanza.scarica')}
            </NsButton>
            {deposito.stato !== 'LAVORATO' &&
            deposito.stato !== 'PROVV_GIA_FIRMATO_DEPOSITATO' ? (
              <Box sx={deleteCss}>
                <DeleteOutlineIcon
                  onClick={() => deleteDeposito(deposito.idProvv)}
                  color="error"
                />
              </Box>
            ) : (
              <></>
            )}
          </Box>
        </Box>
        <Grid mt={2} container>
          <FileBustaDeposito
            key={deposito.idProvv}
            provvedimentiDaFirmare={files}
          />
          <Grid item xs={12}>
            {deposito.stato !== 'LAVORATO' &&
            deposito.stato !== 'PROVV_GIA_FIRMATO_DEPOSITATO' ? (
              <Box mt={2} display="flex" alignItems="center">
                <Checkbox
                  defaultChecked={false}
                  onChange={handleConformitaChange}
                  checked={isChecked}
                />
                <Typography variant="h5">
                  {t('deposito.minutaDiOrdinanza.conformitaVerificata')}
                </Typography>
              </Box>
            ) : (
              <></>
            )}
          </Grid>
        </Grid>
      </Grid>
      {isLoading && <NsFullPageSpinner isOpen={true} value={1} />}
    </>
  );
}
