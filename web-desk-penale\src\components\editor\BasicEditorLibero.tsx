import {useEffect, useState} from 'react';
import {editorEventInput, editorEventInputKeyPress} from "../shared/Utils";
import {BasicEditorProps} from "./editor.interfaces";
import {DynamicEditor} from "./editor.utils";

/**
 * Componente base usato per caricare una textarea con editor tinymce e include un piccola parte della gestione degli eventi.
 *  utilizza l'interfaccia {@link BasicEditorProps} per fare eventi e parametri
 * @param defaultValue
 * @param height
 * @param disabled
 * @param changed
 * @param dirty
 * @param clickEvent
 * @param showSecondEditor
 * @param templates
 * @param templateValues
 * @param showTemplate
 * @param isPresidente
 */
export function BasicEditorLibero({
                              defaultValue = '',
                              height = 400,
                              disabled = false,
                              changed,
                              dirty,
                              clickEvent,
                              showSecondEditor,
                              templates,
                              templateValues,
                              showTemplate = 'template',
                              isPresidente,
                              isMinutaModificataDalPresidente,
                            }: Readonly<BasicEditorProps>) {
  const [value, setValue] = useState<string>(defaultValue);

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  const handleEditorChange = (content: any,) => {
    setValue(content);
    changed?.(content);
  };

  const setDirty = (content: any, editor: any) => {
    // non utilizzarlo restitusice false solo in caso che non è stato mai valorizzato
    console.log(`baseEditor dirty: ${editor.isDirty()}`);
  };

  const blurEvent = (editor: any) => {
    editor.on('blur', (_: Event) => {
      // viene sollevato quando esce ed entra nell'input
      console.log(`Editor was blurred! ${editor.isDirty()}`);
    });
    editor.on('change', (_: Event) => {
      // viene lanciato l'evento solo nel caso che il valore dell'editor subisce cambiamenti
      console.log('Editor was change editor!');
      dirty?.(true);
      // usa anche questo evento perche si ha problemi quando si usano i template
      clickEvent?.(true)
    });

    // altri eventi js
    editor.on('click', (_: Event) => {
      sessionStorage.setItem('clickEvent', 'true');
      clickEvent?.(true)
    });

    if (isPresidente) {
      // attenzione questi sono 2 eventi solo per il presidente
      editor.on('beforeinput', (e: any) => {
        if (editorEventInput(e.inputType)) {
          setTimeout(() => {
            sessionStorage.setItem('clickEvent', 'true');
          }, 10);
          return;
        }
        if (sessionStorage.getItem('clickEvent') == 'true') {
          sessionStorage.setItem('clickEvent', 'false');
          editor.execCommand('mceApplyTextcolor', 'forecolor', sessionStorage.getItem(editor.id) ?? 'blue');

        }

      });
      editor.on('keydown', (e: any) => {
        if (e?.key && editorEventInputKeyPress(e.key)) {
          setTimeout(() => {
            localStorage.setItem('clickEvent', 'true');
          }, 10);
        } else if (e?.key && e?.key == 'Enter') {
          editor.execCommand('mceApplyTextcolor', 'forecolor', sessionStorage.getItem(editor.id) ?? 'blue');
        }
      });
    } else {
      sessionStorage.setItem('clickEvent', 'false');
      console.log('non è il presidente');
    }

    //altri eventi utili per il futuro
    /*    editor.on('contextmenu', (e: Event) => {
          tasto destro del mouse
          console.log('contextmenu event')
          if(isPresidente) {
            editor.execCommand('ForeColor', false, 'blue');
          }
        });
        editor.on('touchstart', (e: Event) => {
          console.log('touchstart event')
          if(isPresidente) {
            editor.execCommand('ForeColor', false, 'blue');
          }
        }); editor.on('compositionupdate', (e: Event) => {
          console.log('compositionupdate event')
          if(isPresidente) {
            editor.execCommand('ForeColor', false, 'blue');
          }
        }); editor.on('compositionstart', (e: Event) => {
          console.log('compositionstart event')
          if(isPresidente) {
            editor.execCommand('ForeColor', false, 'blue');
          }
        });*/
  };
  const setBlur = () => {
    console.log(`baseEditor blur:`);
  };

  return <>
      {!showSecondEditor && <DynamicEditor
          value={value}
          init={{
            language: 'it',
          /*  selector: 'textarea' || undefined,*/
            force_hex_color: 'always',
            height: height,
            menubar: false,
            // Add the nonbreaking plugin to handle spaces better
            plugins: ['charmap', 'print', 'preview', 'searchreplace', 'visualblocks', 'fullscreen', 'insertdatetime', 'paste', 'wordcount', 'template', 'nonbreaking'],
            text_patterns: false,
            quickbars_insert_toolbar: true,
            // Add indent controls to the toolbar
            toolbar: `undo redo | formatselect | bold italic | ${showTemplate && showTemplate.length > 0 ? showTemplate : ''} | alignleft aligncenter alignright alignjustify | forecolor |`,
            color_map: isPresidente ? ['0000FF', 'Blue'] : ['008000', 'Green',],
            custom_colors: false,
            template_replace_values: templateValues,
            template_preview_replace_values: templateValues,
            init_instance_callback: blurEvent,
            templates,
            // Paste configuration to preserve all HTML formatting including tabs
            paste_retain_style_properties: "all",
            paste_word_valid_elements: "*[*]",
            paste_data_images: false,
            paste_as_text: false,
            paste_merge_formats: false,
            paste_convert_word_fake_lists: false,
            paste_remove_styles_if_webkit: false,
            paste_webkit_styles: "all",
            paste_tab_spaces: 0, // Don't convert tabs to spaces
            // HTML validation settings
            valid_elements: "*[*]",
            extended_valid_elements: "*[*]",
            invalid_elements: "",
            verify_html: false,

            // Whitespace handling
            whitespace_elements: "pre,script,style,textarea,pre,code",
            // Add CSS to preserve whitespace in the editor
            content_css: "body { white-space: pre-wrap; }",
            content_style: "pre { white-space: pre-wrap; } p { white-space: pre-wrap; } .mce-content-body { white-space: pre-wrap; }",
            // Set up the editor to show invisible characters
            visualchars_default_state: true,
            // Preserve all whitespace
            preserve_cdata: true,
            setup: (editor: any) => {
              if (isPresidente) {
                editor.on('ExecCommand', (e: any) => {
                  if (e.command == 'mceApplyTextcolor' && e?.ui == 'forecolor') {
                    console.log('ExecCommand:', e.value);
                    sessionStorage.setItem(editor.id, e.value);
                  }
                });
              }

              // Add custom paste handling if necessary
              editor.on('PastePreProcess', function(e: { content: string; }) {
                // Ensure tabs are preserved
                e.content = e.content.replace(/\t/g, '&emsp;');
              });
            },
          }}
          disabled={disabled}
          onDirty={setDirty}
          onBlur={setBlur}
          onEditorChange={handleEditorChange}
        />}
    </>
}
