import {Editor} from '@tinymce/tinymce-react';
import React from 'react';

// TinyMCE so the global var exists
// eslint-disable-next-line no-unused-vars
import tinymce from 'tinymce';
// DOM model
import 'tinymce/models/dom/model';
// Theme
import 'tinymce/themes/silver';
// Toolbar icons
import 'tinymce/icons/default';
// Editor styles
import 'tinymce/skins/ui/oxide/skin.min.css';

// importing the plugin js.
// if you use a plugin that is not listed here the editor will fail to load
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/anchor';
import 'tinymce/plugins/autolink';
import 'tinymce/plugins/autoresize';
import 'tinymce/plugins/autosave';
import 'tinymce/plugins/charmap';
import 'tinymce/plugins/code';
import 'tinymce/plugins/codesample';
import 'tinymce/plugins/directionality';
import 'tinymce/plugins/emoticons';
import 'tinymce/plugins/fullscreen';
import 'tinymce/plugins/help';
import 'tinymce/plugins/importcss';
import 'tinymce/plugins/insertdatetime';
import 'tinymce/plugins/link';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/media';
import 'tinymce/plugins/nonbreaking';
import 'tinymce/plugins/pagebreak';
import 'tinymce/plugins/preview';
import 'tinymce/plugins/quickbars';
import 'tinymce/plugins/save';
import 'tinymce/plugins/searchreplace';
import 'tinymce/plugins/table';
import 'tinymce/plugins/template';
import 'tinymce/plugins/visualblocks';
import 'tinymce/plugins/visualchars';
import 'tinymce/plugins/wordcount';
import {IAllProps} from "@tinymce/tinymce-react/lib/cjs/main/ts/components/Editor";

/**
 * Creazione della test area utilizzando tinymce con piccola gestione degli eventi di tinymce
 * @param props sono tutti i parametri che si possono usare in tinymce {@link IAllProps}
 * @constructor
 */
export default function BundledEditor(props: Readonly<IAllProps>) {
  const {init, ...rest} = props;
  const tiny_ = React.useMemo(() => tinymce, [tinymce]);
  // note that skin and content_css is disabled to avoid the normal
  // loading process and is instead loaded as a string via content_style

  return (
    <Editor
      init={{
        language: 'it',
        language_url: '/langsEditor/it.js',
        ...init,
        skin: false,
        content_css: false,
        branding: false,
        forced_root_block_attrs: {
          style: 'text-align: justify;',
        },
        content_style: [
          '.mce-content-body {font-size:16px;font-family:Verdana;}',

          init?.content_style || '',
        ].join('\n'),
      }}
      {...rest}
    />
  );
}
