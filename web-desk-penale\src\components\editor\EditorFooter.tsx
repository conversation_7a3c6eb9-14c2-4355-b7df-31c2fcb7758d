//data1?.ricorsoByIdUdienAndNrg?.
//data2?.colleggioDetails.

import {formatDate, oscuramentoTxt} from "../shared/Utils";
import {Grid, MenuItem, TextareaAutosize} from "@mui/material";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import {NsSelect} from "@netservice/astrea-react-ds";
import InfoIcon from "@mui/icons-material/Info";
import {useTranslation} from "react-i18next";
import {EditorEpigrafe} from "./editor.utils";
import {EditorFooterProps} from "./editor.interfaces";

export default function EditorFooter({disabled, oscuramento, handleOscurato, dataDecisione, epigrafeNames}: Readonly<EditorFooterProps>) {
  const { t } = useTranslation();
  return (
    <Grid item mt={2} xs={12}>
      <Box mb={2}>
        <Typography>*{t('pages.libero.oscuramentoDati')}</Typography>
        <Box display="flex" alignItems="center">
          <NsSelect
            sx={{ width: '190px' }}
            disabled={disabled}
            size="small"
            defaultValue={oscuramento ? 'si': 'no' }
            changed={handleOscurato}
          >
            <MenuItem key={0} value="si">
              {t('pages.libero.si')}
            </MenuItem>
            <MenuItem key={1} value="no">
              {t('pages.libero.no')}
            </MenuItem>
          </NsSelect>
          {oscuramento && (
            <Box className={'msgEpigrafe'}>
              <InfoIcon fontSize="small" style={{marginLeft: '0.3rem'}} />
              <Typography ml={1} variant="h5">
                {t('pages.libero.avvisoOmissioneGeneralita')}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
      {oscuramento && (
        <>
          <Typography mt={2} mb={2} marginLeft={'2%'}>
            *{t('pages.libero.avvisoOscuramentoDati')}
          </Typography>
          <TextareaAutosize
            disabled={true}
            style={{ width: '100%', height: '70px', fontSize: '16px' }}
            defaultValue={oscuramentoTxt}
          />
        </>
      )}
      <Box display="flex" alignItems="center">
        <Typography mt={2} mb={2} mr={2}>
          {t('pages.libero.cosiEDeciso')}{' '}
          {formatDate(
            dataDecisione as string,
            'DD/MM/YYYY'
          )}
        </Typography>
      </Box>
      <Typography mt={2} mb={2}>
        {t('pages.libero.epigrafe')}
      </Typography>
      <EditorEpigrafe epigrafeNames={epigrafeNames} />
    </Grid>
  );
}
