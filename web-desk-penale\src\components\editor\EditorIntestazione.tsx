//data1?.ricorsoByIdUdienAndNrg?.
//data2?.colleggioDetails.

import {convertTipoMagPreToString, convertTipoProvvedimentoForRedazione, formatDate} from "../shared/Utils";
import {Grid} from "@mui/material";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";

export default function EditorIntestazione({tipoProvvedimento, udienza, colleggioMagistrati, idSezionale, ricorsoDetails, semplificata}: Readonly<any>) {

  const calculateSezionale = () => {
    if(idSezionale) {
      let resultSezionale = convertTipoProvvedimentoForRedazione(tipoProvvedimento) === 'SENTENZA' ? 'Sent' : 'Ord';
      resultSezionale = `${resultSezionale}. n. sez. ${idSezionale}`;
      return resultSezionale;
    }
    return '';
  }
  const getRowColleggioMagistrato = (magistrato: any, i: number) => {
    if (magistrato.magistrato?.anagraficaMagistrato) {
      let title = '';
      if (magistrato.isRelatore && !magistrato.isEstensore) {
        title = magistrato.tipoMag === 'PRE' ? '' : 'Relatore';
      } else if (magistrato.isEstensore && magistrato.tipoMag === 'PRE') {
        title = 'Presidente Relatore';
      } else if (magistrato.isEstensore) {
        title = 'Estensore';
      } else {
       title = convertTipoMagPreToString(magistrato.tipoMag);
      }
      title =  title ? `-  ${title}  -` : '' ;
      return <Box  key={'collegioEditor-'+i} style={{display: "flex", justifyContent: "space-between"}}>
        {magistrato.magistrato.anagraficaMagistrato.nome + ' '} {magistrato.magistrato.anagraficaMagistrato.cognome}
        <Box> {title} </Box>
      </Box>
    }
    return <></>
  }
  return (
    <Grid item={true} xs={12} lg={12} className={'editor'}>
      <Box style={{textAlign: "center"}}>
        <Box className={'editor-intestazione'}>
        <img src="/images/logoMinisteroBlack.png" width="91" height="103" alt="Logo ministero"/>
        <Typography ><b>Repubblica Italiana</b></Typography>
        {tipoProvvedimento !== 'SENTENZA' ? '' : <Typography>In nome del Popolo Italiano</Typography>}
        <Typography >LA CORTE SUPREMA DI CASSAZIONE</Typography>
        <Typography>{udienza?.sezione?.descrizione} PENALE</Typography>
      </Box>
      </Box>
      <Box style={{display: "flex"}}>
        <Box style={{flex: 1, textAlign: "left"}}>
          <Box>Composta da</Box>
          <Box></Box>
          {colleggioMagistrati && colleggioMagistrati.map((magistrato: any, i: number) => getRowColleggioMagistrato(magistrato, i))}
        </Box>
        <Box style={{flex: 1, textAlign: "right"}}>
          <Box>
            {calculateSezionale()}
          </Box>
            <Box style={{ textAlign: "right"}}>
            {udienza?.tipoUdienza?.sigla?.split('').reverse().join('')} - {formatDate(udienza?.dataUdienza, 'DD/MM/YYYY')}
            </Box>
          <Box>R.G.N {ricorsoDetails.numero}/{ricorsoDetails.anno}</Box>
          {semplificata  && <Box><b>Motivazione semplificata</b></Box>}
        </Box>
      </Box>
      <Box style={{flex: 1,textAlign: "left",  marginTop: "1rem"}} >
        <Box>ha pronunciato la seguente</Box>
      </Box>
      <Box  style={{flex: 1,textAlign: "center"}}>
        <Box style={{paddingBottom: "0.5rem"}}><b>{convertTipoProvvedimentoForRedazione(tipoProvvedimento)}</b></Box>
      </Box>
    </Grid>
  );
}
