import { Box, Typography } from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { EditorValue,} from 'src/interfaces';
import {EditorProps} from "./editor.interfaces";
import { BasicEditorLibero } from './BasicEditorLibero';



export default function EditorLibero({
    dirtyEvent,
    placeholders,
    isPresidente,
    clickEvent,
    editorsValueDefault,
    updateEditorValue,
    templates,
    isMinutaModificataDalPresidente,
}: Readonly<EditorProps>) {
  const { t } = useTranslation();

  const [editorsValueCurrent, setEditorsValueCurrent] = useState<EditorValue>(editorsValueDefault);
  const setDirtyInput = (value: boolean) => {
    console.log(`dirty alert :${value}`);
    dirtyEvent?.(true);
  };
  const setClickInput = (value: boolean) => {
    console.log(`click alert :${value}`);
    clickEvent?.(value);
    // setClickEditor(value);
  };
  const updateEditor = (state: string, value: string) => {
    setEditorsValueCurrent({ ...editorsValueCurrent, [state]: value })
    updateEditorValue?.({ [state]: value })
  };
  return (
    <>
      <div className="editors">
        {placeholders && templates && (
          <>
            <BasicEditorLibero
              changed={(value: string) => updateEditor('textLibero', value)}
              dirty={(value: boolean) => setDirtyInput(value)}
              clickEvent={(value: boolean) => setClickInput(value)}
              defaultValue={editorsValueDefault.textLibero}
              showTemplate=""
              isPresidente={isPresidente ?? false}
              isMinutaModificataDalPresidente={isMinutaModificataDalPresidente}
            />
            <Typography mt={2} mb={2}>
              <Box component="span" color={'red'} >
                *
              </Box>
              {t('editor.strutturaEditor.PQM')}
            </Typography>
            <BasicEditorLibero
              changed={(value: string) => updateEditor('pqm', value)}
              dirty={(value: boolean) => setDirtyInput(value)}
              clickEvent={(value: boolean) => setClickInput(value)}
              defaultValue={editorsValueDefault.pqm}
              showTemplate=""
              isPresidente={isPresidente ?? false}
              isMinutaModificataDalPresidente={isMinutaModificataDalPresidente}
            />
          </>
        )}
      </div>
      <style jsx global>{`
        s {
          background: black !important;
        }
      `}</style>
    </>
  );
}
