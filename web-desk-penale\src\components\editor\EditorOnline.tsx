import { relayQueries_PenaleCollegioDetailsByOrdineQuery } from '@/generated/relayQueries_PenaleCollegioDetailsByOrdineQuery.graphql';
import { relayQueries_TrackingStatoQuery } from '@/generated/relayQueries_TrackingStatoQuery.graphql';
import { ProvvedimentiTipoEnum } from '../../types/types';

import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsButton,
  NsFullPageSpinner,
  useNotifier,
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useRouter } from 'next/router';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { Conferma, Prosegui } from '../shared/Conferma';
import MainModal from '../shared/MainModal';
import {
  clearHtmlTag,
  enabledStepOscuramento,
  fixedButtons,
  isNullOrUndefined,
  modalStyle,
  regexForTestoSottolineato,
  validationStyle,
} from '../shared/Utils';
import { useConfig } from '../shared/configuration.context';
import {
  EditorTemplates,
  EditorValue,
  PlaceholdersProps,
} from '../../interfaces';
import { PenaleCollegioDetailsByOrdineSchema } from '../relay/relayQueries';
import CustomizedSteppers from './EditorWizard';
import { Api } from '../shared/Api';
import { MinimalRicorsoDetailSchema } from '../relay/relayQueriesRicorsoDetails';
import { relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery } from '@/generated/relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery.graphql';
import EditorLibero from './EditorLibero';
import EditorStrutturato from './EditorStrutturato';
import EditorOscuramento from './EditorOscuramento';
import EditorIntestazione from './EditorIntestazione';
import { MethodAxios, useApiAxios } from '../../hooks/useDataFetch';
import {
  convertEditorValueResult,
  convertTemplatePlaceholders,
  convertTemplateResult,
  getCallCorrectUrl,
  tipologie,
} from './editor.service';
import EditorPreviewModal from './EditorPreviewModal';
import EditorPreviewModalLibero from './EditorPreviewModalLibero';
import EditorFooter from './EditorFooter';
import {
  EditorAction,
  EditorEpigrafesProps,
  EditorQueryParams,
  EditorRicorsiDetailsProps,
  EditorTypology,
  SalvaBozzaProps,
} from './editor.interfaces';
import { calculateTextOscurato } from './editor.utils';
import InfoIcon from '@mui/icons-material/Info';
import moment from 'moment';
import InviaMinutaModificataModal from '../scrivania/InviaMinutaModificataModal';
import { StatoProvvedimentiEnum } from '../../types/types';
import { TrackingStatoSchema } from '../relay/relayQueries';

export const ROOT_QUERY = MinimalRicorsoDetailSchema;

export const ROOT_QUERY2 = PenaleCollegioDetailsByOrdineSchema;

export const ROOT_QUERY_TRACKING = TrackingStatoSchema;

const circleStyle = {
  background: '#308A7D',
  borderRadius: '50%',
  width: '20px',
  height: '20px',
};

function EditorOnline() {
  const router = useRouter();
  const {
    idUdienza,
    params,
    tipoProvvedimento,
    edit,
    idProvvedimento,
    //IRP (isRuoloPresidente) se a true vuol dire che sono presidente
    IRP,
  } = useMemo(() => {
    return {
      idUdienza: parseInt(router.query?.idUdienza?.toString?.() as string),
      params: parseInt(router.query?.params?.toString?.() as string),
      tipoProvvedimento: router.query?.tipoProvvedimento?.toString?.() ?? '',
      edit: router.query?.edit?.toString?.()?.toLowerCase() === 'true',
      idProvvedimento: router.query?.idProvvedimento?.toString?.() ?? '',
      IRP: router.query?.IRP?.toString?.()?.toLowerCase() === 'true',
    };
  }, [router.query]);
  const theme: any = useTheme();
  const { t } = useTranslation();
  const { notify } = useNotifier();

  const [content, setContent] = useState<EditorValue | null>(null);
  const [retryTemplates, setRetryTemplates] = useState<boolean>(false);
  const [dirty, setDirty] = useState<boolean>(false);
  const [showSecondEditor, setShowSecondEditor] = useState<boolean>(false);
  const [aSteps, setASteps] = useState<number>(0);
  const [lastSavedTextOscurato, setLastSavedTextOscurato] = useState<string>('');
  const [textOscuratoStrutturato, setTextOscuratoStrutturato] = useState<string>('');
  const [textOscuratoLibero, setTextOscuratoLibero] = useState<string>('');
  const [ricorsoDetails, setRicorsoDetails] =
    useState<EditorRicorsiDetailsProps>({
      anno: null,
      numero: null,
      nrgCompleto: null,
      dataDecisione: null,
      nrg: null,
    });
  const [epigrafeNames, setEpigrafeNames] = useState<EditorEpigrafesProps>({
    estensore: '',
    presidente: '',
  });
  const [myInterval, setMyInterval] = useState<any | null>(null);
  const [isOpenPreviewModal, setIsOpenPreviewModal] = useState<boolean>(false);
  const showAlertRef = useRef(false);
  const autoSaveRef = useRef(false);
  const [validationMessages, setValidationMessages] = useState<string[]>([]);
  const { CallCreazioneProvvedimentoESalvaInBozza, CallSalvataggioBozza } =
    Api();
  const [lastAutoSave, setLastAutoSave] = useState<string | undefined>(
    undefined
  );
  const [isPresidente, setIsPresidente] = useState<boolean>(false);

  const [isMinutaModificataPresidente, setIsMinutaModificataPresidente] =
    useState<boolean>(false);

  const { data: data1, isLoading: isLoading1 } =
    useQuery<relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery>(
      ROOT_QUERY,
      {
        idUdien: Number(idUdienza),
        nrg: Number(params),
      },
      {
        fetchPolicy: STORE_OR_NETWORK,
      }
    );

  const { data: trackingData, isLoading: isTrackingLoading } =
    useQuery<relayQueries_TrackingStatoQuery>(
      ROOT_QUERY_TRACKING,
      {
        id: idProvvedimento,
        roles: isPresidente ? 'PRESIDENTE' : 'RELATORE',
      },
      {
        fetchPolicy: STORE_OR_NETWORK,
        skip: !idProvvedimento,
      }
    );

  useEffect(() => {
    if (trackingData?.provvedimentoTrackingByIdProvvedimento) {
      const tracking = trackingData.provvedimentoTrackingByIdProvvedimento;
      const isMinutaModificata = tracking.some(
        (track) =>
          track.idProvvedimento === idProvvedimento &&
          track.prevStato ===
            StatoProvvedimentiEnum.MINUTA_MODIFICATA_PRESIDENTE
      );
      console.log('isMinutaModificata', isMinutaModificata);
      setIsMinutaModificataPresidente(isMinutaModificata);
    }
  }, [trackingData, idProvvedimento]);

  const selectedStyle = {
    background: 'white',
    cursor: 'pointer',
    p: 1,
    border: theme.custom.borders[0],
  };
  const NoSelectedStyle = {
    background: '#e8e8e8',
    cursor: 'pointer',
    p: 1,
    border: theme.custom.borders[0],
  };

  const { data: data2, isLoading: isLoading2 } =
    useQuery<relayQueries_PenaleCollegioDetailsByOrdineQuery>(
      ROOT_QUERY2,
      {
        idUdienza: Number(idUdienza),
        nrg: Number(params),
      },
      {
        fetchPolicy: STORE_OR_NETWORK,
      }
    );
  const { servizi, autoSaveTimer } = useConfig();

  const { data: placeholders, loading: loadingSaveBox } = useApiAxios<
    PlaceholdersProps,
    any
  >(
    `/provvedimento/placeholder/${idUdienza}/${params}`,
    MethodAxios.GET,
    null,
    convertTemplatePlaceholders
  );

  const { data: editorsValue, loading: loadingEditorValue } = useApiAxios<
    EditorValue,
    any
  >(
    getCallCorrectUrl(idProvvedimento, idUdienza, params),
    MethodAxios.GET,
    null,
    convertEditorValueResult,
    [data1?.ricorsoByIdUdienAndNrg?.udienza.sezione.sigla ?? '']
  );
  const { data: templates, loading: loadingTemplate } = useApiAxios<
    EditorTemplates,
    any
  >(
    `/template/search`,
    MethodAxios.POST,
    { tipologiaList: tipologie },
    convertTemplateResult,
    [placeholders ?? ''],
    false,
    retryTemplates
  );

  const showLoader = () => {
    return (
      loading ||
      isLoading1 ||
      isLoading2 ||
      loadingSaveBox ||
      loadingTemplate ||
      loadingEditorValue ||
      isTrackingLoading
    );
  };

  useEffect(() => {
    if (placeholders) {
      setRetryTemplates(true);
    }
  }, [placeholders]);

  useEffect(() => {
    if (editorsValue) {
      if (isNullOrUndefined(editorsValue.strutturato)) {
        editorsValue.strutturato = true;
      }
      setContent(editorsValue);
      // Inizializza lastSavedTextOscurato con il valore caricato dal backend
      setLastSavedTextOscurato(editorsValue.textOscurato || '');
      // Inizializza i textOscurato per entrambe le tipologie con il valore iniziale
      if (editorsValue.strutturato) {
        setTextOscuratoStrutturato(editorsValue.textOscurato || '');
        setTextOscuratoLibero('');
      } else {
        setTextOscuratoLibero(editorsValue.textOscurato || '');
        setTextOscuratoStrutturato('');
      }
      if (
        editorsValue.strutturato === undefined ||
        (editorsValue.strutturato &&
          (!editorsValue.textLibero || editorsValue.textLibero.trim() == ''))
      ) {
        setContent({ ...editorsValue, textLibero: editorsValue?.introduzione });
      }
    } else {
      const defaultEditor: EditorValue = {
        strutturato: true,
        textOscurato: '',
      };
      setContent(defaultEditor);
      setLastSavedTextOscurato('');
      setTextOscuratoStrutturato('');
      setTextOscuratoLibero('');
    }
  }, [editorsValue]);

  useEffect(() => {
    if (data1?.ricorsoByIdUdienAndNrg) {
      setRicorsoDetails({
        anno: data1?.ricorsoByIdUdienAndNrg?.anno,
        numero: data1?.ricorsoByIdUdienAndNrg?.numero,
        nrgCompleto: data1.ricorsoByIdUdienAndNrg.nrg,
        dataDecisione: data1?.ricorsoByIdUdienAndNrg?.udienza?.dataUdienza,
        nrg: `${data1?.ricorsoByIdUdienAndNrg?.numero}/${data1?.ricorsoByIdUdienAndNrg?.anno}`,
      });
    }
    if (data2?.colleggioDetails) {
      const obj: { presidente?: string; estensore?: string } = {};
      let idMag = '';
      data2?.colleggioDetails?.colleggioMagistrati?.map((magistrato: any) => {
        const nameCalcolato = `${magistrato.magistrato.anagraficaMagistrato.nome} ${magistrato.magistrato.anagraficaMagistrato.cognome}`;
        if (magistrato.tipoMag === 'PRE') {
          obj['presidente'] = nameCalcolato;
          idMag = magistrato.magistrato.idMagis;
        }
        if (magistrato.isEstensore && magistrato.magistrato.idMagis !== idMag) {
          obj['estensore'] = nameCalcolato;
        }
      });
      setEpigrafeNames({ ...epigrafeNames, ...obj });
    }
  }, [data1?.ricorsoByIdUdienAndNrg, data2]);

  const timer = useCallback(
    (autoSaveNew: boolean) => {
      if (autoSaveNew) {
        const interval = setInterval(
          () => {
            handleClickSalvaBozza();
          },
          autoSaveTimer ? autoSaveTimer * 1000 * 60 : 180000
        );
        setMyInterval(interval);
      } else if (myInterval) {
        console.log(`clear Launch auto save`);
        clearInterval(myInterval);
        setMyInterval(null);
      }
      // return () => clearInterval(interval);
    },
    [autoSaveRef]
  );

  useEffect(() => {
    if (IRP) {
      setIsPresidente(true);
    }

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const handlePopState = async () => {
    const message =
      "E' sicuro di voler uscire? Tutti i dati non salvati saranno persi.";
    if (confirm(message)) {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.location.reload();
    } else {
      const queryParam: EditorQueryParams = {
        idUdienza: idUdienza,
        params: params,
        tipoProvvedimento: tipoProvvedimento,
      };
      if (edit) {
        queryParam.edit = edit;
      }
      if (idProvvedimento) {
        queryParam.idProvvedimento = idProvvedimento;
      }
      if (IRP) {
        queryParam.IRP = IRP;
      }
      router.push({
        pathname: window.location.pathname,
        query: queryParam,
      });
    }
  };

  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    if (showAlertRef.current) {
      const message =
        "E' sicuro di voler uscire? Tutti i dati non salvati saranno persi.";
      e.preventDefault();
      e.returnValue = message;
    }
  };


  const handleNextClick = () => {
    if (aSteps < 1) {
      setShowSecondEditor(true);
      setASteps(aSteps + 1);
      closeModalPreview();
      // La logica di aggiornamento del textOscurato è gestita nelle funzioni di salvataggio
    } else {
      setShowSecondEditor(false);
    }
  };

  const handleIndietroClick = () => {
    if (aSteps > 0 && content?.oscurato) {
      setASteps(aSteps - 1);
      setShowSecondEditor(false);
      setDirtyInput(false);

      // Ripristina il textOscurato dall'ultimo valore salvato quando si torna indietro
      // Questo evita che le sottolineature non salvate rimangano visibili
      if (content) {
        // Usa lastSavedTextOscurato se disponibile, altrimenti editorsValue.textOscurato
        const textOscuratoToRestore = lastSavedTextOscurato || editorsValue?.textOscurato || '';
        setContent({ ...content, textOscurato: textOscuratoToRestore });
      }
    }

    if (content?.oscurato && !showSecondEditor) {
      setShowSecondEditor(true);
    }
  };
  const closeInviaMinutaModificataModal = () => {
    console.log('closeInviaMinutaModificataModal');
    setModalPropsInviaMinutaModificata({
      ...modalPropsInviaMinutaModificata,
      modal: false,
    });
  };
  const [modalPropsInviaMinutaModificata, setModalPropsInviaMinutaModificata] =
    useState({
      modal: false,
      onClose: closeInviaMinutaModificataModal,
      title: 'Invia Minuta Modificata',
      body: <InviaMinutaModificataModal provv={idProvvedimento} />,
      // openFromParent: true,
      ddisableBackdropClick: true,
      style: modalStyle,
    });

  const handleCreazioneProvvedimento = async () => {
    await handleClickEditoAction(EditorAction.MODIFICA_DIRETTA_PRESIDENTE);
    setModalPropsInviaMinutaModificata({
      ...modalPropsInviaMinutaModificata,
      body: <InviaMinutaModificataModal provv={idProvvedimento} />,
      modal: true,
    });
  };

  const [loading, setLoading] = useState(false);

  const handleClickSalvaBozza = async (openPreviewModal?: boolean) => {
    setLoading(true);

    if (params && idUdienza) {
      const tipoProvvedimentoEnum = tipoProvvedimento as ProvvedimentiTipoEnum;
      const calculateOscurato: boolean = content?.oscurato ?? false;

      // Aggiorna il textOscurato solo se ci sono state modifiche (showAlertRef.current è true)
      let updatedTextOscurato = content?.textOscurato ?? '';
      if (calculateOscurato && showAlertRef.current) {
        updatedTextOscurato = calculateTextOscurato(content);
        // Aggiorna anche lo stato locale per il prossimo utilizzo
        setContent({ ...content, textOscurato: updatedTextOscurato });
        // Reset del flag delle modifiche dopo aver aggiornato il textOscurato
        showAlertRef.current = false;
      }

      const salvaBozzaParams: SalvaBozzaProps = {
        idProvvedimento: idProvvedimento,
        tipologiaProvvedimento: tipoProvvedimentoEnum,
        nrg: params,
        origine: 'SYSTEM',
        idUdienza: idUdienza,
        anRuolo: ricorsoDetails.anno,
        numRuolo: ricorsoDetails.numero,
        text: content?.textLibero,
        introduzione: content?.introduzione,
        motivoRicorso: content?.motivoRicorso,
        finaleDeposito: content?.finaleDeposito,
        textOscurato: updatedTextOscurato,
        generaOscurato: calculateOscurato,
        dataDecisione: ricorsoDetails.dataDecisione,
        params: params,
        pqm: content?.pqm ?? '',
        edit: edit,
        IRP: IRP,
        strutturato: content?.strutturato ?? false,
      };
      const operationOk = await CallCreazioneProvvedimentoESalvaInBozza(
        salvaBozzaParams
      );
      if (operationOk) {
        // Aggiorna l'ultimo textOscurato salvato per il ripristino corretto
        setLastSavedTextOscurato(updatedTextOscurato);
        // Aggiorna anche il textOscurato salvato per la tipologia corrente
        if (content?.strutturato) {
          setTextOscuratoStrutturato(updatedTextOscurato);
        } else {
          setTextOscuratoLibero(updatedTextOscurato);
        }
        if (openPreviewModal) {
          openModalPreview();
        }
      }
    }
    if (autoSaveRef.current) {
      setLastAutoSave(moment(new Date()).format('DD/MM/yyyy HH:mm:ss'));
    }
    setLoading(false);
  };

  const nextStep = () => {
    handleClickSalvaBozza(true);
  };
  const openModalPreview = () => {
    if (
      (!showSecondEditor && validateEditor()) ||
      (showSecondEditor && setIsEvidenziatoInput())
    ) {
      setIsOpenPreviewModal(true);
    }
  };
  const handleClickTypology = (input: EditorTypology) => {
    const strutturato = input === EditorTypology.STRUTTURATO;

    if (content?.oscurato) {
      // Salva il textOscurato corrente per la tipologia attuale
      if (content.strutturato) {
        setTextOscuratoStrutturato(content.textOscurato || '');
      } else {
        setTextOscuratoLibero(content.textOscurato || '');
      }

      // Ripristina il textOscurato per la nuova tipologia
      let textOscuratoToRestore = '';
      if (strutturato) {
        textOscuratoToRestore = textOscuratoStrutturato;
      } else {
        textOscuratoToRestore = textOscuratoLibero;
      }

      // Se non abbiamo un textOscurato salvato per questa tipologia, forza il ricalcolo
      if (!textOscuratoToRestore) {
        showAlertRef.current = true;
      }

      const newContent = {
        ...content,
        strutturato: strutturato,
        textOscurato: textOscuratoToRestore
      };
      setContent(newContent);
    } else {
      // Se l'oscuramento non è abilitato, cambia solo la tipologia
      const newContent = { ...content, strutturato: strutturato };
      setContent(newContent);
    }
  };
  const handleClickEditoAction = async (input: EditorAction) => {
    setLoading(true);
    const tipoProvvedimentoEnum = tipoProvvedimento as ProvvedimentiTipoEnum;
    const calculateOscurato: boolean = content?.oscurato ?? false;

    // Aggiorna il textOscurato solo se ci sono state modifiche (showAlertRef.current è true)
    let updatedTextOscurato = content?.textOscurato ?? '';
    if (calculateOscurato && showAlertRef.current) {
      updatedTextOscurato = calculateTextOscurato(content);
      // Aggiorna anche lo stato locale per il prossimo utilizzo
      setContent({ ...content, textOscurato: updatedTextOscurato });
      // Reset del flag delle modifiche dopo aver aggiornato il textOscurato
      showAlertRef.current = false;
    }

    const salvaBozzaParams: SalvaBozzaProps = {
      idProvvedimento: idProvvedimento,
      tipologiaProvvedimento: tipoProvvedimentoEnum,
      nrg: params,
      origine: 'SYSTEM',
      idUdienza: idUdienza,
      anRuolo: ricorsoDetails.anno,
      numRuolo: ricorsoDetails.numero,
      text: content?.textLibero,
      introduzione: content?.introduzione,
      motivoRicorso: content?.motivoRicorso,
      finaleDeposito: content?.finaleDeposito,
      textOscurato: updatedTextOscurato,
      generaOscurato: calculateOscurato,
      dataDecisione: ricorsoDetails.dataDecisione,
      params: params,
      pqm: content?.pqm ?? '',
      edit: edit,
      IRP: IRP,
      strutturato: content?.strutturato ?? false,
      action: input,
    };
    try {
      await CallSalvataggioBozza(salvaBozzaParams);
      // Aggiorna l'ultimo textOscurato salvato per il ripristino corretto
      setLastSavedTextOscurato(updatedTextOscurato);
      // Aggiorna anche il textOscurato salvato per la tipologia corrente
      if (content?.strutturato) {
        setTextOscuratoStrutturato(updatedTextOscurato);
      } else {
        setTextOscuratoLibero(updatedTextOscurato);
      }
      setIsOpenPreviewModal(false);
    } catch (error) {
      console.error("Errore durante l'esecuzione dell'azione", error);
    } finally {
      setLoading(false);
    }

  };

  const setDirtyInput = (value: boolean) => {
    setDirty(value);
    console.log(`Dirty value libero:${dirty}, dirtyEvent:${value}`);
    showAlertRef.current = value;
  };
  const updateEditorValue = (editorValueNew: EditorValue) => {
    // alert('Devi aggiornare le variabili.'+JSON.stringify(value))
    console.log('Devi aggiornare le variabili.', editorValueNew);

    setContent(content ? { ...content, ...editorValueNew } : null);
  };
  const validateEditor = () => {
    const prevMessages: string[] = [];
    if (clearHtmlTag(content?.pqm) === '') {
      prevMessages.push(
        'E necessario compilare il campo "P.Q.M." per proseguire'
      );
    }
    if (content?.strutturato) {
      if (clearHtmlTag(content?.finaleDeposito) === '') {
        prevMessages.push(
          'E necessario compilare il campo "CONSIDERATO IN DIRITTO" per proseguire'
        );
      }
      if (clearHtmlTag(content?.motivoRicorso) === '') {
        prevMessages.push(
          'E necessario compilare il campo "RITENUTO IN FATTO" per proseguire'
        );
      }

      if (clearHtmlTag(content?.introduzione) === '') {
        prevMessages.push(
          'E necessario compilare il campo "INTRODUZIONE" per proseguire'
        );
      }
    } else if (clearHtmlTag(content?.textLibero) === '') {
      prevMessages.push(
        'E necessario compilare il campo "INTRODUZIONE" per proseguire'
      );
    }

    if (prevMessages.length == 0) {
      return true;
    }
    setValidationMessages(prevMessages);
    return false;
  };
  const setClickEventInput = (value: boolean) => {
    console.log(`Dirty value libero:${dirty}, dirtyEvent:${value}`);
    showAlertRef.current = value;
  };
  const modalConfirm = () => {
    closeModal();
    setIsOpenPreviewModal(true);
  };
  const setIsEvidenziatoInput = () => {
    const isTextOscurato = regexForTestoSottolineato(content?.textOscurato);
    if (!isTextOscurato) {
      setModalProps({
        ...modalProps,
        modal: true,
        title: '',
        body: (
          <Prosegui
            closeModal={closeModal}
            body={t('pages.libero.confermaProseguiSenzaSottEditor')}
            confirm={modalConfirm}
          />
        ),
      });
    }
    return isTextOscurato;
  };

  const handleClose = () => {
    let urlRedirenct = '/fascicolo/';
    if (IRP) {
      urlRedirenct = '/scrivania/fascicolo/';
    }
    window.location.href = urlRedirenct + idUdienza + '?params=' + params;
  };
  const handleAnnullaAndDeleteBozza = () => {
    setModalProps({
      ...modalProps,
      modal: true,
      title: t('common.warning'),
      body: (
        <Conferma
          closeModal={closeModal}
          body={t('pages.libero.confermaAnnullaEElimina')}
          confirm={handleAnnullaAndDeleteBozzaStep2}
        />
      ),
    });
  };
  const handleAnnullaAndDeleteBozzaStep2 = () => {
    setLoading(true);
    axios
      .delete(servizi + '/provvedimento/deleteProvvedimento/' + idProvvedimento)
      .then((_) => {
        notify({
          message: t('pages.libero.provvedimentoEliminatoBozza'),
          type: 'success',
        });
        let urlRedirenct = '/fascicolo/';
        if (IRP) {
          urlRedirenct = '/scrivania/fascicolo/';
        }
        router.push({
          pathname: urlRedirenct + idUdienza,
          query: {
            params: params,
          },
        });
      })
      .finally(() => setLoading(false));
  };

  const handleOscurato: any = (event: any) => {
    const selectedValue = event.target.value;
    setContent(
      content
        ? { ...content, oscurato: enabledStepOscuramento(selectedValue) ?? '' }
        : null
    );
    showAlertRef.current = true;
  };
  const handleEnabledAutoSave: any = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selectedValue = event.target.checked;
    autoSaveRef.current = selectedValue;
    if (myInterval) {
      clearInterval(myInterval);
      const highestTimeoutId = setInterval(';');
      for (let i = 0; i < highestTimeoutId; i++) {
        clearInterval(i);
      }
    }

    if (selectedValue) {
      timer(selectedValue);
    }
  };
  const updateTextOscurato = (oscuratoValue: string) => {
    setContent(
      content ? { ...content, textOscurato: oscuratoValue ?? '' } : null
    );
  };

  const closeModal = () => {
    setModalProps({ ...modalProps, modal: false });
  };
  const closeModalPreview = () => {
    setIsOpenPreviewModal(false);
  };

  const [modalProps, setModalProps] = useState({
    modal: false,
    closeModal,
    style: modalStyle,
    title: t('pages.libero.saltaSottolineaturaDati'),
    body: <></>,
  });

  return (
    <Box style={{ padding: '15px' }} className="App editor">
      <h1>
        {t('pages.libero.redazioneFascicolo')} {ricorsoDetails.nrg}
      </h1>
      <Grid item mt={1} alignItems="center" container mb={1}>
        <Box
          mr={1}
          p={0.5}
          sx={{
            backgroundColor: '#BDC5C7',
            fontWeight: 700,
            borderRadius: '5px',
          }}
        >
          {t('pages.libero.udienzaChiusa')}
        </Box>
        <Box mr={1} ml={1} sx={circleStyle}></Box>
        <Box mr={1}>
          {data1?.ricorsoByIdUdienAndNrg?.udienza?.sezione.descrizione} |{' '}
        </Box>
        <Box mr={1}>
          {data1?.ricorsoByIdUdienAndNrg?.udienza?.tipoUdienza.descrizione} |
        </Box>
        <Box>{`Collegio ${
          data1?.ricorsoByIdUdienAndNrg?.udienza?.aula?.descrizione ?? ''
        }`}</Box>
      </Grid>
      <Box
        border={1}
        borderColor="grey.300"
        borderRadius={1}
        padding={2}
        marginBottom={5}
        marginTop={2}
      >
        <CustomizedSteppers activeStep={aSteps} />
      </Box>

      <Grid mt={2} container justifyContent="center" mb={5}>
        {!showSecondEditor && (
          <>
            <Grid
              item
              xs={6}
              md={6}
              onClick={() => handleClickTypology(EditorTypology.STRUTTURATO)}
            >
              <Typography
                sx={content?.strutturato ? selectedStyle : NoSelectedStyle}
              >
                {t('pages.libero.strutturata')}
              </Typography>
            </Grid>
            <Grid
              item
              xs={6}
              md={6}
              onClick={() => handleClickTypology(EditorTypology.LIBERO)}
            >
              <Typography
                sx={!content?.strutturato ? selectedStyle : NoSelectedStyle}
              >
                {t('pages.libero.testoLibero')}
              </Typography>
            </Grid>
          </>
        )}

        <Grid item mt={2} xs={8}>
          <Box>
            {data2?.colleggioDetails?.colleggioMagistrati &&
              data1?.ricorsoByIdUdienAndNrg?.udienza && (
                <EditorIntestazione
                  tipoProvvedimento={tipoProvvedimento}
                  udienza={data1?.ricorsoByIdUdienAndNrg.udienza}
                  idSezionale={editorsValue?.idSezionale}
                  colleggioMagistrati={
                    data2?.colleggioDetails.colleggioMagistrati
                  }
                  ricorsoDetails={ricorsoDetails}
                  semplificata={editorsValue?.semplificata}
                />
              )}
            {!showSecondEditor && (
              <>
                {!showSecondEditor && !isTrackingLoading && (
                  <>
                    {content?.strutturato && editorsValue && templates && (
                      <EditorStrutturato
                        updateEditorValue={(value: EditorValue) =>
                          updateEditorValue(value)
                        }
                        dirtyEvent={(value: boolean) => setDirtyInput(value)}
                        clickEvent={(value: boolean) =>
                          setClickEventInput(value)
                        }
                        placeholders={placeholders}
                        isPresidente={isPresidente}
                        templates={templates}
                        editorsValueDefault={
                          content?.introduzione ? content : editorsValue
                        }
                        isMinutaModificataDalPresidente={
                          isMinutaModificataPresidente
                        }
                      />
                    )}
                    {!content?.strutturato && editorsValue && (
                      <EditorLibero
                        updateEditorValue={(value: EditorValue) =>
                          updateEditorValue(value)
                        }
                        dirtyEvent={(value: boolean) => setDirtyInput(value)}
                        clickEvent={(value: boolean) =>
                          setClickEventInput(value)
                        }
                        placeholders={placeholders}
                        isPresidente={isPresidente}
                        templates={templates}
                        editorsValueDefault={
                          content?.textLibero ? content : editorsValue
                        }
                        isMinutaModificataDalPresidente={
                          isMinutaModificataPresidente
                        }
                      />
                    )}
                  </>
                )}
              </>
            )}
            {validationMessages.map((message, i) => (
              <Typography
                display="flex"
                alignItems="center"
                sx={validationStyle}
                mt={2}
                key={'message-' + i}
              >
                <InfoIcon style={{ color: '#D4351C' }} />
                {message}
              </Typography>
            ))}

            {showSecondEditor && (
              <>
                {' '}
                <EditorOscuramento
                  textOscurato={content?.textOscurato ?? ''}
                  changeAllValues={updateTextOscurato}
                />
              </>
            )}
            {isOpenPreviewModal && (
              <>
                {content?.strutturato ? (
                  <EditorPreviewModal
                    isOpenPreviewModal={isOpenPreviewModal}
                    handleIndietroClick={handleIndietroClick}
                    handleConfermaClick={handleNextClick}
                    content={content}
                    handleClickAction={(data: EditorAction) =>
                      handleClickEditoAction(data)
                    }
                    handleInviaMinutaModificata={handleCreazioneProvvedimento}
                    dataDecisione={ricorsoDetails?.dataDecisione}
                    epigrafeNames={epigrafeNames}
                    isPresidente={isPresidente}
                    tipoProvvedimento={tipoProvvedimento}
                    udienza={data1?.ricorsoByIdUdienAndNrg?.udienza}
                    idSezionale={editorsValue?.idSezionale}
                    colleggioMagistrati={
                      data2?.colleggioDetails?.colleggioMagistrati
                    }
                    ricorsoDetails={ricorsoDetails}
                    semplificata={editorsValue?.semplificata}
                    enabledFinalStep={
                      (content?.oscurato && showSecondEditor) || !content?.oscurato
                    }
                    closeModal={closeModalPreview}
                  />
                ) : (
                  <EditorPreviewModalLibero
                    isOpenPreviewModal={isOpenPreviewModal}
                    handleIndietroClick={handleIndietroClick}
                    handleConfermaClick={handleNextClick}
                    content={content}
                    handleClickAction={(data: EditorAction) =>
                      handleClickEditoAction(data)
                    }
                    handleInviaMinutaModificata={handleCreazioneProvvedimento}
                    dataDecisione={ricorsoDetails?.dataDecisione}
                    epigrafeNames={epigrafeNames}
                    isPresidente={isPresidente}
                    tipoProvvedimento={tipoProvvedimento}
                    udienza={data1?.ricorsoByIdUdienAndNrg?.udienza}
                    idSezionale={editorsValue?.idSezionale}
                    colleggioMagistrati={
                      data2?.colleggioDetails?.colleggioMagistrati
                    }
                    ricorsoDetails={ricorsoDetails}
                    semplificata={editorsValue?.semplificata}
                    enabledFinalStep={
                      (content?.oscurato && showSecondEditor) || !content?.oscurato
                    }
                    closeModal={closeModalPreview}
                  />
                )}
              </>
            )}
          </Box>
        </Grid>
        <Grid
          container
          xs={8}
          item
          alignItems="center"
          justifyContent="space-between"
        >
          <EditorFooter
            disabled={showSecondEditor}
            oscuramento={content?.oscurato ?? false}
            handleOscurato={handleOscurato}
            dataDecisione={ricorsoDetails?.dataDecisione}
            epigrafeNames={epigrafeNames}
          />
          <Grid
            item
            sx={fixedButtons}
            xs={12}
            container
            justifyContent="space-between"
          >
            <Box>
              <NsButton
                id={'editorClose'}
                onClick={handleClose}
                variant="outlined"
                color="primary"
              >
                {t('common.chiudi')}
              </NsButton>
              {isPresidente && (
                <NsButton
                  id={'editorCancel'}
                  onClick={handleAnnullaAndDeleteBozza}
                  variant="outlined"
                  color="primary"
                  sx={{ ml: '1.2rem' }}
                >
                  {t('common.annulla')}
                </NsButton>
              )}
            </Box>
            <FormControlLabel
              control={
                <Checkbox
                  id={'editorSalvataggioAutomatico'}
                  color="primary"
                  checked={autoSaveRef.current}
                  onChange={handleEnabledAutoSave}
                />
              }
              label={`Salvataggio automatico ${
                content?.strutturato ? 'testo strutturato ' : 'testo libero '
              }  ${lastAutoSave ?? ''}`}
            />
            <Box>
              {showSecondEditor && (
                <NsButton
                  id={'editorBack'}
                  variant="outlined"
                  color="primary"
                  onClick={handleIndietroClick}
                  sx={{ mr: '10px' }}
                >
                  {t('common.indietro')}
                </NsButton>
              )}
              <NsButton
                id={'salvaBozza'}
                variant="outlined"
                color="primary"
                sx={{ mr: '10px' }}
                onClick={() => handleClickSalvaBozza()}
              >
                {t('fascicolo.salvaInBozza')}
              </NsButton>
              <NsButton
                variant="contained"
                id={'editorNext'}
                onClick={nextStep}
              >
                {t('common.forward')}
              </NsButton>
            </Box>
          </Grid>
        </Grid>
      </Grid>
      <MainModal {...modalProps} />
      <div className="nsLoader">
        <NsFullPageSpinner value={1} isOpen={showLoader()} />
      </div>
      <MainModal {...modalPropsInviaMinutaModificata} />
      <style jsx>{`
        .hidden {
          display: none;
        }
      `}</style>
    </Box>
  );
}

export default EditorOnline;
