import {setupEditor} from '../shared/Utils';
import {OscuratoEditorProps} from "./editor.interfaces";
import {DynamicEditor} from "./editor.utils";

export default function EditorOscuramento({
                                       textOscurato,
                                       changeAllValues,
                                     }: Readonly<OscuratoEditorProps>) {
  return (
    <>
      <DynamicEditor
        value={textOscurato}
        init={{
          height: 400,
          menubar: false,
          toolbar: 'undo redo | strikethrough | oscuramentoButton  | clear',
          formats: {
            strikethrough: {
              inline: 's',
              styles: {background: 'yellow'},
              exact: true,
            },
          },
          setup: (editor: any) => setupEditor(editor),
        }}
        onEditorChange={(data: string) => {
          changeAllValues(data);
        }}
        onKeyDown={(e: any) => {
          if (!((e.ctrlKey || e.metaKey) && e.key === 'c')) {
            e.stopPropagation();
            e.preventDefault();
          }
        }}
        onPaste={(e: any) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onDrop={(e: any) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onDragOver={(e: any) => {
          e.stopPropagation();
          e.preventDefault();
        }}
      />
      <style jsx global>{`
        button[title='Trova e sottolinea'] .tox-tbtn__icon-wrap {
          background-color: #cce2df !important;
          box-shadow: 3px 3px #2f8a7d !important;
        }

        button[title='Trova e sottolinea']:hover {
          background-color: unset !important;
        }

        .tox .tox-tbtn--select {
          width: auto !important;
        }

        .tox .tox-tbtn--select:hover {
          background: unset !important;
          cursor: pointer;
        }
      `}</style>
    </>
  );
}
