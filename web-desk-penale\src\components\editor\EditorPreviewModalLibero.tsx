import { Box, Grid, Modal, Typography } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier,
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useRouter } from 'next/router';
import {useEffect, useState} from 'react';
import { useTranslation } from 'react-i18next';
import {
  confirmModalStyle,
  formatDate, innerModalStyle,
  modalStylePreview,
  oscuramentoTxt
} from '../shared/Utils';
import { useConfig } from '../shared/configuration.context';
import {EditorModalProps2, EditorAction} from "./editor.interfaces";
import EditorIntestazione from "./EditorIntestazione";
import {calcolaTesti, EditorEpigrafe} from "./editor.utils";
import {NsGenericModal} from "../../utils/NsGenericModal";

export default function EditorPreviewModalLibero(editorPreviewModalProps: Readonly<EditorModalProps2>) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { notify } = useNotifier();
  const { servizi } = useConfig();
  const router = useRouter();
  const [cominatedEditor, setCominatedEditor] = useState<string>('');
  const [disabledCodaDifirmaButton, setDisabledCodaDifirmaButton] = useState<boolean>(false); // Nuovo stato per il bottone di immissione in coda di firma
  const [disabledFirmaEDepositaButton, setDisabledFirmaEDepositaButton] = useState<boolean>(false); // Nuovo stato per il bottone "procediAllaFirmaDelDeposito"
  const [disabledTornaIndietroButton, setDisabledTornaIndietroButton] = useState<boolean>(false); // Nuovo stato per il bottone "procediAllaFirmaDelDeposito"
  const [modalProps, setModalProps] = useState({
    isOpen: false,
    title: '',
    body: <></>,
  });
  const buttonStyle = {
    fontSize: '1em',
    bgcolor: '#CCE2DF',
    color: '#308A7D',
    paddingRight: '1em',
    paddingLeft: '1em',
  };
  console.log('editorPreviewModalProps extern: ', editorPreviewModalProps)
  const { idProvvedimento, params, idUdienza } = router.query;
  const serviceUrl = `${servizi}`;
  useEffect(() => {
    let  calculateTextPreview: string =  '';
    console.log('editorPreviewModalProps effect: ', editorPreviewModalProps)
    if(editorPreviewModalProps.content?.oscurato && editorPreviewModalProps.enabledFinalStep){
      console.log('editorPreviewModalProps entro nell if: ', editorPreviewModalProps,editorPreviewModalProps.content?.oscurato,
        editorPreviewModalProps.enabledFinalStep);
      calculateTextPreview = editorPreviewModalProps?.content?.textOscurato ?? '';
    } else {
      console.log('editorPreviewModalProps entro nell else: ', editorPreviewModalProps, editorPreviewModalProps.content?.oscurato ,
        editorPreviewModalProps.enabledFinalStep);

      calculateTextPreview = calcolaTesti(editorPreviewModalProps?.content);
    }
    setCominatedEditor(calculateTextPreview || '')
  }, [editorPreviewModalProps])

  const getHandleClickActionSaveAndSendToCodaDiFirma = () => {
    return Promise.resolve(editorPreviewModalProps?.handleClickAction(EditorAction.CODA_FIRMA));
  };

  const saveAndSendToCodaDiFirma = async () => {
    try {
      setDisabledCodaDifirmaButton(true);
      setDisabledFirmaEDepositaButton(true); // Disabilita anche il bottone "procediAllaFirmaDelDeposito"
      setDisabledTornaIndietroButton(true); // Disabilita anche il bottone per tornare indietro
      await getHandleClickActionSaveAndSendToCodaDiFirma();
    } catch (error) {
      console.error('Error during handle click action:', error);
    } finally {
      setDisabledCodaDifirmaButton(false);
    }
  };

  const getHandleClickActionSaveAndFirmaEDeposita = () => {
    return Promise.resolve(editorPreviewModalProps?.handleClickAction(EditorAction.FIRMA_DEPOSITA));
  };

  const saveAndFirmaEDeposita = async () => {
    try {
      setDisabledFirmaEDepositaButton(true);
      setDisabledCodaDifirmaButton(true); // Disabilita anche il bottone per l'immissione in coda
      setDisabledTornaIndietroButton(true); // Disabilita anche il bottone per tornare indietro
      await getHandleClickActionSaveAndFirmaEDeposita();
    } catch (error) {
      console.error('Error during handle click action:', error);
    } finally {
      setDisabledFirmaEDepositaButton(false);
    }
  };

  const handleModal = (data: any) => {
    setModalProps({
      ...modalProps,
      body: (
        <Box sx={confirmModalStyle}>
          <Typography variant="h5" mt={1} mb={3}>
            {t('shared.editorModal.depositoFileWord')}
          </Typography>
          <Box display="flex" justifyContent="space-between">
            <NsButton
              id={'downloadClosed'}
              size="small"
              onClick={() => setModalProps({ ...modalProps, isOpen: false })}
              sx={buttonStyle}
            >
              {t('shared.editorModal.chiudi')}
            </NsButton>
            <NsButton  id={'downloadWord'} variant="contained" onClick={() => downloadWord()}>
              {t('shared.editorModal.download')}
            </NsButton>
          </Box>
        </Box>
      ),
      title: `Conferma presa in carico fascicolo ${data?.nrg}`,
      isOpen: true,
    });
  };

  const downloadWord = async () => {
    setModalProps({ ...modalProps, isOpen: false });
    setIsLoading(true);
    axios
      .get(
        `${serviceUrl}/provvedimento/downloadDocxForRedazione/${idProvvedimento}`,
        { responseType: 'blob' }
      )
      .then((response: any) => {
        const filename = response.headers['content-disposition'].split('=')[1];
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
      })
      .catch((_: any) => {
        notify({
          message: 'Errore nel download del word',
          type: 'error',
        });
      })
      .finally(() => {
        setIsLoading(false);
        router.push('/fascicolo/' + idUdienza + '?params=' + params).then();
      });
  };

  return (
    <>
      <NsGenericModal isOpen={modalProps?.isOpen} body={modalProps?.body} title={modalProps.title} style={innerModalStyle}></NsGenericModal>
      <Modal
        id={'previewEditorModal'}
        open={editorPreviewModalProps?.isOpenPreviewModal}
        onClose={editorPreviewModalProps?.closeModal}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={modalStylePreview}  id={'previewBodyEditorModal'}>
          {editorPreviewModalProps?.colleggioMagistrati && editorPreviewModalProps?.udienza &&
            <EditorIntestazione tipoProvvedimento={editorPreviewModalProps.tipoProvvedimento}
                                udienza={editorPreviewModalProps.udienza}
                                idSezionale={editorPreviewModalProps.idSezionale}
                                colleggioMagistrati={editorPreviewModalProps.colleggioMagistrati}
                                ricorsoDetails={editorPreviewModalProps.ricorsoDetails}
                                semplificata={editorPreviewModalProps.semplificata}
            />}
          <Grid mt={2} item xs={12}>
            <Box mb={2} dangerouslySetInnerHTML={{ __html: cominatedEditor }} />
            {editorPreviewModalProps.content?.oscurato && <Box mb={2}>{oscuramentoTxt}</Box>}
            {/*Così è deciso, {formatDateUTC(dataDecisione)}*/}
            Così è deciso, {formatDate(editorPreviewModalProps?.dataDecisione ?? '', 'DD/MM/YYYY')}
            <EditorEpigrafe epigrafeNames={editorPreviewModalProps?.epigrafeNames} />
          </Grid>
          <Grid item mt={4} xs={12} container justifyContent="space-between">
            <NsButton id={'closeModal'} variant="outlined" color="primary" onClick={editorPreviewModalProps?.closeModal} disabled={disabledTornaIndietroButton}>
              {t('shared.editorModal.tornaIndietro')}
            </NsButton>
            { !editorPreviewModalProps.enabledFinalStep  &&
              !editorPreviewModalProps?.isPresidente && (
                <NsButton
                  id={'downloadWord'}
                  variant="contained"
                  color="primary"
                  onClick={handleModal}
                >
                  {t('editor.editorLibero.downloadWord')}
                </NsButton>
              )}
            {editorPreviewModalProps?.enabledFinalStep ? (
              <Box>

                {!editorPreviewModalProps?.isPresidente && (
                  <>
                    {/* BOTTONE PER INSERIRE IN CODA DI FIRMA */}
                    <NsButton
                      disabled={disabledCodaDifirmaButton}
                      id={'CodaDeposito'}
                      sx={{ marginRight: '10px' }}
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        saveAndSendToCodaDiFirma()
                      }}
                    >
                      {t('shared.editorModal.mettiInCodaDiFirma')}
                    </NsButton>
                    {/* BOTTONE PER FIRMARE E DEPOSITARE */}
                    <NsButton
                      id={'procediAllaFirma'}
                      variant="contained"
                      color="primary"
                      disabled={disabledFirmaEDepositaButton} // Nuovo stato per disabilitare il bottone
                      onClick={() => {
                        saveAndFirmaEDeposita()
                      }}
                    >
                      {t('shared.editorModal.procediAllaFirmaDelDeposito')}
                    </NsButton>
                  </>
                )}
                {editorPreviewModalProps?.isPresidente
                  /* BOTTONE PER INVIARE LA MINUTA MODIFICATA */ && (
                  <NsButton
                    id={'sendMinutaModificata'}
                    variant="contained"
                    color="primary"
                    onClick={editorPreviewModalProps?.handleInviaMinutaModificata}
                  >
                    {t('shared.editorModal.inviaMinutaModificata')}
                  </NsButton>
                )}
              </Box> /* BOTTONE PER PROCEDERE CON SOTTOLINEATURA */
            ) : (
              /* BOTTONE PER PROCEDERE CON SOTTOLINEATURA */
              <NsButton
                id={'confirmOrNestButton'}
                variant="contained"
                color="primary"
                onClick={editorPreviewModalProps?.handleConfermaClick}
              >
                {t('shared.editorModal.procediConSottolineatura')}
              </NsButton>
            )}
          </Grid>
        </Box>
      </Modal>
      <NsFullPageSpinner isOpen={isLoading} value={100} />
    </>
  );
}
