import { Box, Typography } from '@mui/material';
import {  useState } from 'react';
import { BasicEditor } from './BasicEditor';
import { useTranslation } from 'react-i18next';
import {EditorProps} from "./editor.interfaces";
import {EditorValue} from "../../interfaces";


export default function EditorStrutturato({
  dirtyEvent,
  placeholders,
  isPresidente,
  clickEvent,
  editorsValueDefault,
  updateEditorValue,
  templates,
  isMinutaModificataDalPresidente,
}: Readonly<EditorProps>) {
 const { t } = useTranslation();
  const [dirty, setDirty] = useState<boolean>(false);
  const [editorsValueCurrent, setEditorsValueCurrent] = useState<EditorValue>(editorsValueDefault);


  const updateEditor = (state: string, value: string) => {
    setEditorsValueCurrent({ ...editorsValueCurrent, [state]: value })
    updateEditorValue?.({ [state]: value })
  };

  const setDirtyInput = (value: boolean) => {
    setDirty(value);
    dirtyEvent?.(value);
    console.log(`dirtyHook:${dirty}, dirtyValue:${value}`);
  };
  const setClickEvent = (value: boolean) => {
    // setCllickEventValue(value);
    clickEvent?.(value);
    console.log(`dirtyHook:${dirty}, dirtyValue:${value}`);
  };

  return (
    <>
      {placeholders && templates?.introduzione && (
        <>
          <Typography mt={2} mb={2}>
            <Box component="span" color={'red'}>
              *
            </Box>
            {t('editor.strutturaEditor.introduzione')}
          </Typography>
          <BasicEditor
            changed={(value: string) => updateEditor('introduzione', value)}
            dirty={(value: boolean) => setDirtyInput(value)}
            clickEvent={(value: boolean) => setClickEvent(value)}
            defaultValue={editorsValueDefault.introduzione}
            templates={templates?.introduzione}
            isPresidente={isPresidente}
            showTemplate=""
            templateValues={{
              ...placeholders,
            }}
            isMinutaModificataDalPresidente={isMinutaModificataDalPresidente}
          />
          <Typography mt={2} mb={2}>
            <Box component="span" color={'red'}>
              *
            </Box>
            {t('editor.strutturaEditor.ritenutoInFatto')}
          </Typography>
          <BasicEditor
            changed={(value: string) => updateEditor('motivoRicorso', value)}
            dirty={(value: boolean) => setDirtyInput(value)}
            clickEvent={(value: boolean) => setClickEvent(value)}
            defaultValue={editorsValueDefault.motivoRicorso}
            templates={templates.motivoRicorso}
            isPresidente={isPresidente}
            templateValues={{
              ...placeholders,
            }}
            isMinutaModificataDalPresidente={isMinutaModificataDalPresidente}
          />
          <Typography mt={2} mb={2}>
            <Box component="span" color={'red'}>
              *
            </Box>
            {t('editor.strutturaEditor.consideratoInDiritto')}
          </Typography>
          <BasicEditor
            changed={(value: string) => updateEditor('finaleDeposito', value)}
            dirty={(value: boolean) => setDirtyInput(value)}
            clickEvent={(value: boolean) => setClickEvent(value)}
            defaultValue={editorsValueDefault.finaleDeposito}
            templates={templates.finaleDeposito}
            isPresidente={isPresidente}
            templateValues={{
              ...placeholders,
            }}
            isMinutaModificataDalPresidente={isMinutaModificataDalPresidente}
          />
          <Typography mt={2} mb={2}>
            <Box component="span" color={'red'}>
              *
            </Box>
            {t('editor.strutturaEditor.PQM')}
          </Typography>
          <BasicEditor
            changed={(value: string) => updateEditor('pqm', value)}
            dirty={(value: boolean) => setDirtyInput(value)}
            clickEvent={(value: boolean) => setClickEvent(value)}
            defaultValue={editorsValueDefault.pqm}
            isPresidente={isPresidente}
            showTemplate=""
            isMinutaModificataDalPresidente={isMinutaModificataDalPresidente}
          />
        </>
      )}
    </>
  );
}
