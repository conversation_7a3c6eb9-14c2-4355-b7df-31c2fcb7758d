import {EditorTemplates, EditorValue, TemplateProps} from "../../interfaces";
import React from "react";
import {ParsedUrlQueryInput} from "node:querystring";
import {ProvvedimentiOrigineEnum, ProvvedimentiTipoEnum} from "@/generated/ProvvedimentoTableMutation.graphql";

export interface BasicEditorProps {
  defaultValue?: string;
  height?: number;
  disabled?: boolean;
  changed?: (value: string) => void;
  dirty?: (value: boolean) => void;
  clickEvent?: (value: boolean) => void;
  showSecondEditor?: any;
  templates?: TemplateProps[];
  templateValues?: any;
  showTemplate?: string;
  isPresidente: boolean;
  isMinutaModificataDalPresidente?: boolean;
}
export enum EditorAction {
  CODA_FIRMA='codaFirma',
  FIRMA_DEPOSITA='procediAllaFirma',
  REDIRECT_EDITOR ='redirectAction',
  MODIFICA_DIRETTA_PRESIDENTE = 'MODIFICA_DIRETTA_PRESIDENTE'
}
export interface SalvaBozzaProps{
  idProvvedimento: string | null | undefined,
  tipologiaProvvedimento: ProvvedimentiTipoEnum,
  nrg: number,
  origine: ProvvedimentiOrigineEnum,
  idUdienza: number,
  anRuolo: number | null | undefined,
  numRuolo: number | null | undefined,
  text: string | null | undefined,
  introduzione: string | null | undefined,
  motivoRicorso: string | null | undefined,
  finaleDeposito: string | null | undefined,
  textOscurato: string,
  generaOscurato: boolean,
  dataDecisione: string | null | undefined,
  params: number,
  pqm: string,
  edit?: boolean,
  IRP?: boolean
  strutturato: boolean;
  action?: EditorAction
}

export interface EditorModalProps {
  modal: boolean;
  closeModal: () => void;
  style: object;
  title?: string;
  body?: JSX.Element;
  closeButton?: boolean;
  previewModalOpen: any;
  handleIndietroClick: any;
  handleConfermaClick: any;
  combinedSecondContent: string;
  combinedContent: string;
  editorsValue: any;
  handleClick?: any;
  allValues?: any;
  epigrafeTxt: any;
  dataDecisione: any;
  isPresidente?: boolean;
  handleInviaMinutaModificata?: any;
}

export interface EditorModalProps2 {
  isOpenPreviewModal: boolean;
  closeModal: () => void;
  title?: string;
  body?: React.ReactElement;
  closeButton?: boolean;
  handleIndietroClick: () => void;
  handleConfermaClick: () => void;
  handleClickAction: (data: EditorAction) => void;
  content: EditorValue | null;
  epigrafeNames: any;
  dataDecisione: string | null;
  isPresidente?: boolean;
  handleInviaMinutaModificata?: () => void;
  colleggioMagistrati: ReadonlyArray<any> | null |  undefined;
  udienza: any;
  tipoProvvedimento: string | null |  undefined;
  idSezionale: string | undefined;
  ricorsoDetails: any;
  semplificata: boolean | null |  undefined;
  enabledFinalStep: boolean;
}
export interface EditorFooterProps {
  disabled?: boolean ;
  oscuramento: boolean;
  handleOscurato: () => void;
  dataDecisione: string | null;
  epigrafeNames: any;
}
export enum EditorTypology {
  STRUTTURATO = 'STRUTTURATO',
  LIBERO = 'LIBERO'
}
export interface OscuratoEditorProps {
  textOscurato: string;
  changeAllValues: (content: string) => void;
}
export interface EditorEpigrafesProps {
  estensore: string; presidente: string;
}export interface EditorRicorsiDetailsProps {
  anno: number | null;
  numero: number | null;
  nrgCompleto: string | null;
  dataDecisione: string | null;
  nrg: string | null;
}

export interface EditorProps {
  updateEditorValue: (value: EditorValue) => any;
  dirtyEvent: (value: boolean) => any;
  clickEvent: (value: boolean) => any;
  placeholders: any;
  isPresidente: boolean;
  editorsValueDefault: EditorValue,
  templates?: EditorTemplates | null;
  isMinutaModificataDalPresidente?: boolean;

}
export interface EditorQueryParams extends ParsedUrlQueryInput {
  idUdienza?: number;
  params: any;
  idProvvedimento?: any;
  tipoProvvedimento?: string;
  edit?: boolean;
  IRP?: boolean;
}

export interface CustomizedSteppersProps {
  activeStep: number;
}

export interface TemplateTipologie {
  idTemplate: string;
  tipologia: string;
  title: string;
  description: string;
  content: string;
  ordine: number;
}
