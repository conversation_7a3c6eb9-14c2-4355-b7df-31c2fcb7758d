import {formatCity, formatDate} from "../shared/Utils";

import {EditorTemplates, EditorValue, PlaceholdersProps, TemplateProps} from "../../interfaces";
import {TemplateTipologie} from "./editor.interfaces";

export function convertTemplatePlaceholders(data: any): PlaceholdersProps{
  const mappedData: PlaceholdersProps = {};
  if (data.context.length > 0) {
    data.context.map((placeholder: any) => {
      mappedData[placeholder.key] = placeholder.value;
      return mappedData;
    });
  }
  mappedData.AUTORITAAPPELLO = mappedData.AUTORITAAPPELLO.includes('CORTE APPELLO') ? `Corte d'Appello` : mappedData.AUTORITAAPPELLO;

  mappedData.AUTORITAPRONUNCIA = mappedData.AUTORITAPRONUNCIA.includes('CORTE APPELLO') ? `Corte d'Appello` : mappedData.AUTORITAPRONUNCIA;

  mappedData.CITTAAPPELLO = formatCity(mappedData.CITTAAPPELLO);
  mappedData.CITTAPRONUNCIA = formatCity(mappedData.CITTAPRONUNCIA);

  mappedData.DATAPRONUNCIA = formatDate(mappedData.DATAPRONUNCIA, 'DD/MM/YYYY');
  mappedData.DATASENTENZA = formatDate(mappedData.DATASENTENZA, 'DD/MM/YYYY');
  return mappedData;
};

export const tipologie = ['INTRODUZIONE', 'MOTIVO_DI_RICORSO', 'MOTIVAZIONE', 'PARTE_FINALE',];

export function convertTemplateTemplateProps(data: TemplateTipologie, placeholder: PlaceholdersProps ):TemplateProps{
  let content =  data.content;
  Object.keys(placeholder).map(key => {
    content = content.replaceAll(`{$${key}}`, placeholder[key] );
  })
  return new TemplateProps({id: data.idTemplate, title: data.title, description: data.description, content: content});
}

export function convertTemplateResult(data: Array<TemplateTipologie>,  placeholder: PlaceholdersProps){
  const templateTipologieEditor = new EditorTemplates();
  templateTipologieEditor.introduzione = data.filter(res => res.tipologia.toUpperCase() === 'INTRODUZIONE')
    .map(res => convertTemplateTemplateProps(res, placeholder));
  templateTipologieEditor.motivoRicorso = data.filter(res => res.tipologia.toUpperCase() === 'MOTIVO_DI_RICORSO')
    .map(res => convertTemplateTemplateProps(res, placeholder));
  templateTipologieEditor.finaleDeposito = data.filter(res => res.tipologia.toUpperCase() === 'MOTIVAZIONE' || res.tipologia.toUpperCase() === 'PARTE_FINALE')
    .map(res => convertTemplateTemplateProps(res, placeholder));
  return templateTipologieEditor;
}

export function getCallCorrectUrl(idProvv: string| undefined | null,   idUdienza: number, nrg: number): string{
  const baseUrl = `/provvedimento`;
  if(idProvv){
    return `${baseUrl}/getProvvedimentiContentDocx/${idProvv}`;
  }
  return `${baseUrl}/getTestiIniziali/${idUdienza}/${nrg}`;
}
export interface EditorValueResponse {
  idProvvedimentoEditor?: string;
  idProvvedimento?: string;
  textLibero?: string;
  textOscurato?: string;
  introduzione?: string;
  motivoRicorso?: string;
  finaleDeposito?: string;
  pqm?: string;
  createAt?: Date;
  strutturato?: boolean;
  oscurato: boolean;
  semplificata: boolean;
  tipoProvvedimento: string;
  idSezionale: string;
}
export function convertEditorValueResult(data: EditorValueResponse, sezione: string):EditorValue{

  const finaleDepositoAddSezioneUnite = ' La questione di diritto per la quale i ricorsi sono stati rimessi alle Sezioni unite è la seguente:""';
  const editorValue: EditorValue = {
    idProvvedimento: data.idProvvedimento ?? null,
    introduzione: data?.introduzione ?? '',
    pqm: data?.pqm ?? '',
    finaleDeposito:
      data?.finaleDeposito ?? '',
    motivoRicorso: data?.motivoRicorso ?? '',
    textOscurato: data?.textOscurato ?? '',
    strutturato: data?.strutturato,
    textLibero: data?.textLibero,
    oscurato: data?.oscurato,
    idSezionale:data?.idSezionale,
    tipoProvvedimento: data?.tipoProvvedimento,
    semplificata: data?.semplificata
  };
  if (sezione && sezione == 'SU') {
    editorValue.finaleDeposito += finaleDepositoAddSezioneUnite;
  }
  return editorValue;
}
