import dynamic from "next/dynamic";
import {EditorValue} from "../../interfaces";
import Box from "@mui/material/Box";

/**
 * Componente react che carica dinamicamente l'editor tinymce
 */
export const DynamicEditor = dynamic(() => import('./BundledEditor'), {
  ssr: false, loading: () => <p>Loading...</p>,
});

export function calcolaTesti(content: EditorValue | null) {
  if (content) {
    if (content?.strutturato) {
      return `${content?.introduzione ?? ''}<br>
             <div style="text-align: center;"><b>RITENUTO IN FATTO</b></div>${content?.motivoRicorso ?? ''}<br>
              <div style="text-align: center;"><b>CONSIDERATO IN DIRITTO</b></div>${content?.finaleDeposito ?? ''}<br>
              <div style="text-align: center;"><b>P.Q.M.</b></div>${content?.pqm ?? ''}`;

    }
    return `${content?.textLibero}${
      content?.pqm ? `<div style="text-align: center;"><b>P.Q.M.</b></div> ${content?.pqm}` : ''
    }`;
  }
  return '';
}

export function EditorEpigrafe({epigrafeNames}: Readonly<any>) {
  return (<Box>
    <Box style={{display: 'flex', justifyContent: 'center'}}>
      {epigrafeNames?.estensore && (
        <span style={{marginRight: '10%'}}>
                    <div>Il Consigliere estensore</div>
                    <div>{epigrafeNames.estensore}</div>
                  </span>
      )}

      <span style={{marginRight: '10%'}}>
                  <div>Il Presidente</div>
                  <div>{epigrafeNames.presidente}</div>
                </span>
    </Box>
  </Box>);
}

export function calculateTextOscurato(content: EditorValue | null) {
  if (content) {
    if(content.strutturato) {
      return `${content?.introduzione ?? ''}
                <br>
                <div style="text-align: center;"><b>RITENUTO IN FATTO</b></div>
                ${content?.motivoRicorso ?? ''}
                <br>
                <div style="text-align: center;"><b>CONSIDERATO IN DIRITTO</b></div>
                ${content?.finaleDeposito ?? ''}
                <br>
                <div style="text-align: center;"><b>P.Q.M.</b></div>
                ${content?.pqm ?? ''}`;
    }
    return `${content?.textLibero ?? ''}
             <div style="text-align: center;"><b>P.Q.M.</b></div> ${content?.pqm ?? ''}`;
  }
  return '';
}
