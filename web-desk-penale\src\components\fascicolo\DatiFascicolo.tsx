import { Box, Grid, Typography, useTheme } from '@mui/material';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { DatiFascicoloProps } from '../../model/fascicolo-models.model';
import DatiFascicoloDetails from './DatiFascicoloDetails';
import RiunitiFascicoliTabs from './RiunitiFascicoliTabs';

export default function DatiFascicolo({
  ricorsoUdienza,
  relatore,
  isRelatore,
  isEstensore,
  checkStatoOnSIC,
}: Readonly<DatiFascicoloProps>) {
  const { t } = useTranslation();
  const theme: any = useTheme();

  return (
    <>
      <Grid border={theme.custom.borders[0]} ml={2} mt={2} pl={2} pt={2} pb={2}>
        <Typography variant="h1">
          {t('fascicolo.datiFascicolo.titoloMascheraDatiFascicolo')}
        {/*Da eliminare questo codice quando rilasciamo riunito fase due */}
        { checkStatoOnSIC?.ricorsoRiunito &&
          <Typography  variant="h2">
            {`+(R) ${checkStatoOnSIC?.ricorsoRiunito?.numero}/    ${checkStatoOnSIC?.ricorsoRiunito?.anno}`}
          </Typography>
        }</Typography>
        {isEstensore !== isRelatore && relatore && (
          <Grid>
            <Box mt={1}>
              <Typography>{t('fascicolo.relazionatoDa')}:</Typography>
            </Box>
            <Box mt={1}>
              <Typography variant="h3">
                {relatore?.nome} {relatore?.cognome}
              </Typography>
            </Box>
          </Grid>
        )}
        <DatiFascicoloDetails ricorsoUdienza={ricorsoUdienza} />
      </Grid>{ricorsoUdienza && checkStatoOnSIC?.isPrincipalRicorsoRiunito && (
        <Grid border={theme.custom.borders[0]} ml={2} mt={2} pl={2} pt={2}>
          <Typography variant="h1">
            {t('fascicolo.datiFascicolo.datiRiuniti')}
          </Typography>
          <RiunitiFascicoliTabs
            checkStatoOnSIC={checkStatoOnSIC}
            ricorsoUdienza={ricorsoUdienza}
          ></RiunitiFascicoliTabs>
        </Grid>
      )}
    </>
  );
}
