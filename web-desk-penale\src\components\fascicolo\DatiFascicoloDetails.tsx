import { Box, Grid, useTheme } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DetailParti from '../calendar/DetailParti';
import DetailProvvedimenti from '../calendar/DetailProvvedimenti';
import DetailReati from '../calendar/DetailReati';
import { DatiFascicoloDetailsProps } from '../../model/fascicolo-models.model';
import DialogModal from '../shared/DialogModal';

const labesTitle = { fontSize: '17px', fontWeight: 600 };

export default function DatiFascicoloDetails({
  ricorsoUdienza,
}: Readonly<DatiFascicoloDetailsProps>) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [allReati, setAllReati] = useState<string[] | undefined>([]);
  const [anno, setAnno] = useState<any>();
  const [numero, setNumero] = useState<any>();
  const [tipoRicorsoDescrizione, setTipoRicorsoDescrizione] = useState<any>();
  const [dataIscrizione, setDataIscrizione] = useState<any>();
  const [provvedimentoImpugnato, setProvvedimentoImpugnato] = useState<any>();
  const [allProvvedimenti, setAllProvvedimenti] = useState<any>();

  const [reatoPrincipale, setReatoPrincipale] = useState<any>();

  const [partePrincipale, setPartePrincipale] = useState<any>();

  const [nrg, setNrg] = useState<any>();

  useEffect(() => {
    const TempNrg = ricorsoUdienza?.ricorso?.nrg;
    setNrg(TempNrg);

    const reato = ricorsoUdienza?.ricorso?.reatiRicorso?.find(
      (reato: any) => reato?.principale
    )?.reato?.displayReati;

    const anno = ricorsoUdienza?.ricorso?.anno;
    setAnno(anno);

    const numero = ricorsoUdienza?.ricorso?.numero;
    setNumero(numero);

    const tipoRicorso = ricorsoUdienza?.ricorso?.tipoRicorso;
    setTipoRicorsoDescrizione(tipoRicorso?.descrizione);

    const dataIscrizione = ricorsoUdienza?.ricorso?.dataIscrizione;
    setDataIscrizione(new Date(dataIscrizione).toLocaleDateString());

    const provvedimentoImpugnato =
      ricorsoUdienza?.ricorso?.provvedimentoImpugnato?.[0] ?? null;
    setProvvedimentoImpugnato(provvedimentoImpugnato);

    const allProvvedimenti = ricorsoUdienza?.ricorso?.provvedimentoImpugnato;
    setAllProvvedimenti(allProvvedimenti);

    const allReati = ricorsoUdienza?.ricorso?.reatiRicorso?.map(
      (reato: any) => {
        return (
          reato?.reato?.displayReati + (reato?.principale ? ' Principale' : '')
        );
      }
    );

    const partePrincipale = ricorsoUdienza?.ricorso?.detParti;
    setPartePrincipale(partePrincipale);
    setReatoPrincipale(reato);
    setAllReati(allReati);
  }, [ricorsoUdienza]);

  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <></>,
  });

  const handleModal = (param: string) => {
    if (param == 'parti') {
      const title = `Parti fascicolo ${numero || ''}/${anno || ''}`;
      setModalProps({
        ...modalProps,
        content: <DetailParti parti={nrg} />,
        isOpen: true,
        title,
      });
    } else if (param == 'reati' && allReati?.length && allReati.length > 1) {
      const title = '';
      setModalProps({
        ...modalProps,
        content: <DetailReati reati={allReati} />,
        isOpen: true,
        title,
      });
    } else if (param == 'provvedimenti') {
      const title = `Provvedimenti fascicolo ${numero || ''}/${anno || ''}`;
      setModalProps({
        ...modalProps,
        content: <DetailProvvedimenti provvedimenti={allProvvedimenti} />,
        isOpen: true,
        title,
      });
    }
  };

  return (
    <>
      <DialogModal {...modalProps} />
      <Grid container display={'inline-block'}>
        <Grid item>
          <Box mt={1}>{t('common.valorePonderale')}:</Box>
          <Box>
            <strong>{ricorsoUdienza?.ricorso?.spoglio?.valPond}</strong>
          </Box>
        </Grid>
        <Grid item>
          <Box mt={1}>{t('common.oscuramentoSic')}:</Box>
          <Box>
            <strong>{ricorsoUdienza?.oscuratoSicSingle ? 'SI' : 'NO'}</strong>
          </Box>
        </Grid>
      </Grid>
      <Grid container display={'flex'}>
        <Grid item>
          <Box mt={1}>{t('common.parti')}:</Box>
          <Box>
            <strong>{partePrincipale}</strong>
            <NsButton
              sx={theme.custom.secondaryButton}
              onClick={() => handleModal('parti')}
            >
              {t('common.vediTutte')}
            </NsButton>
          </Box>
        </Grid>
      </Grid>
      <Grid container display={'flex'}>
        <Grid item>
          <Box mt={1}>{t('common.reato')}:</Box>
          <Box sx={labesTitle}>
            <Box>
              {reatoPrincipale}
              {allReati?.length && allReati.length > 1 && (
                <NsButton
                  sx={theme.custom.secondaryButton}
                  onClick={() => handleModal('reati')}
                >
                  {t('common.vediTutti')}
                </NsButton>
              )}
            </Box>
          </Box>
        </Grid>
      </Grid>
      <Grid container display={'flex'}>
        <Grid item>
          <Box mt={2}>{t('fascicolo.tipoRicorso')}:</Box>
          <Box>
            <strong style={{ textTransform: 'capitalize' }}>
              {tipoRicorsoDescrizione}
            </strong>
          </Box>
          <Box mt={2}>{t('fascicolo.dataIscrizione')}:</Box>
          <Box>
            <strong>{dataIscrizione}</strong>
          </Box>
          <Box mt={2}>{t('fascicolo.provvedimentoImpugnato')}:</Box>
          <Box>
            <strong>{provvedimentoImpugnato}</strong>
            {allProvvedimenti?.length && allProvvedimenti.length > 1 && (
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() => handleModal('provvedimenti')}
              >
                {t('common.vediTutti')}
              </NsButton>
            )}
          </Box>
        </Grid>
      </Grid>
    </>
  );
}
