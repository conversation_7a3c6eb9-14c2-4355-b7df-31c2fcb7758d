import { Box, Grid, Typography, useTheme } from '@mui/material';
import { NsFullPageSpinner, NsButton } from '@netservice/astrea-react-ds';
import { useConfig } from '../shared/configuration.context';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  getActionDatiFascicolo,
  useDownloadUtils,
  EstensoreActionName,
} from '../shared/Utils';
import router from 'next/router';

export default function DatiProvvedimento({
  provvedimento,
  ricorsoUdienza,
  checkStatoOnSIC,
  nomeFile,
  numRaccGen,
}: any) {
  const { t } = useTranslation();
  const theme: any = useTheme();

  const [isLoadingDownload, setIsLoadingDownload] = useState(false);
  const [isOscurato, setIsOscurato] = useState(false);

  const [isPubblicato, setIsPubblicato] = useState(false);

  const { id, params } = router.query;

  const [tipologia, setTipologia] = useState<any>();

  const { servizi } = useConfig();

  const icons = getActionDatiFascicolo(t);

  useEffect(() => {
    const  oscurato = provvedimento?.isOscurato || (provvedimento?.listaFile?.length > 0
      && provvedimento?.listaFile.some((f: any) => f.oscurato));
    setIsOscurato(oscurato || false);
    if (provvedimento?.tipo) {
      setTipologia(provvedimento.tipo.replace('MINUTA_', ''));
    }
    const isPubblicato = checkStatoOnSIC?.statoProvvedimento == 'PUBBLICATA';

    setIsPubblicato(isPubblicato);
  }, [provvedimento, checkStatoOnSIC]);

  const serviceUrl = `${servizi}`;
  const { handleDownloadAtto, handlePreviewAtto } =
    useDownloadUtils(serviceUrl);

  const handleDownload = (isOscurato: boolean) => {
    setIsLoadingDownload(true);
    handleDownloadAtto(
      id?.toString() ?? '',
      params?.toString() ?? '',
      tipologia ?? '',
      nomeFile ?? '',
      isOscurato
    ).finally(() => setIsLoadingDownload(false));
  };

  const handlePreview = (isOscurato: boolean) => {
    setIsLoadingDownload(true);
    handlePreviewAtto(
      id?.toString() ?? '',
      params?.toString() ?? '',
      isOscurato
    ).finally(() => setIsLoadingDownload(false));
  };

  const IconButtons = () => {
    const allowedActions: Array<EstensoreActionName> = [];

    allowedActions.push(
      EstensoreActionName.DOWNLOAD_EPURATO,
      EstensoreActionName.PREVIEW_EPURATO
    );
    if (isOscurato) {
      allowedActions.push(
        EstensoreActionName.DOWNLOAD_EPURATO_OSCURATO,
        EstensoreActionName.PREVIEW_EPURATO_OSCURATO
      );
    }

    return (
      <Grid
        container
        justifyContent="flex-start"
        alignItems="left"
        pl={0}
        ml={0}
        pr={0}
        mr={0}
      >
        {icons
          .filter((icon) => allowedActions.includes(icon.actionName))
          .map((icon) => (
            <NsButton
              key={icon.id}
              sx={theme.custom.secondaryButton}
              onClick={() => {
                switch (icon.actionName) {
                  case EstensoreActionName.DOWNLOAD_EPURATO:
                  case EstensoreActionName.DOWNLOAD_EPURATO_OSCURATO:
                    handleDownload(
                      icon.actionName ===
                        EstensoreActionName.DOWNLOAD_EPURATO_OSCURATO
                    );
                    break;
                  case EstensoreActionName.PREVIEW_EPURATO:
                  case EstensoreActionName.PREVIEW_EPURATO_OSCURATO:
                    handlePreview(
                      icon.actionName ===
                        EstensoreActionName.PREVIEW_EPURATO_OSCURATO
                    );
                    break;
                }
              }}
            >
              {icon.icon}
            </NsButton>
          ))}
      </Grid>
    );
  };

  return (
    <Grid item xs={12} md={12} lg={12}>
      <Grid
        border={theme.custom.borders[0]}
        mt={2}
        ml={2}
        style={{ padding: '16px' }}
      >
        <Typography variant="h1">
          {t('fascicolo.datiProvvedimento.mainTitle')}
        </Typography>
        <Box mt={1}> {t('fascicolo.datiProvvedimento.oscuratoSic')}</Box>
        <Box>
          <strong style={{ textTransform: 'capitalize' }}>
            {ricorsoUdienza.oscuratoSicComplessivo ? 'SI' : 'NO'}
          </strong>
        </Box>
        <Box mt={1}> {t('fascicolo.datiProvvedimento.oscuratoDeskCSP')}</Box>
        <Box>
          <strong style={{ textTransform: 'capitalize' }}>
            {ricorsoUdienza.oscuramentoDeskCsp ? 'SI' : 'NO'}
          </strong>
        </Box>
        {isPubblicato && <Box mt={2}>
          <Typography
            variant="h2"
          > {tipologia} {numRaccGen}
            </Typography>
            <IconButtons />
        </Box>}

      </Grid>
      <NsFullPageSpinner isOpen={isLoadingDownload} value={1} />
    </Grid>
  );
}
