import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {DetailFascicoloProps} from "../../interfaces";
import {NsBreadcrumbs, NsFullPageSpinner} from "@netservice/astrea-react-ds";
import DialogModal from '../shared/DialogModal';
import {Box, Grid, Typography, useTheme} from "@mui/material";
import {formatDate, RuoloEnum} from "../shared/Utils";
import {StatoProvvedimentiEnum} from "../../types/types";
import {FascicoloDetailsSchema} from "../relay/relayQueriesFascicoloDetails";
import {NETWORK_ONLY, useQuery} from "relay-hooks";
import {useRouter} from "next/router";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {
  ProvvedimentiStatoEnum,
  relayQueriesFascicoloDetails_FascicoloDetailsQuery,
} from '@/generated/relayQueriesFascicoloDetails_FascicoloDetailsQuery.graphql';
import {FascicoloPresidenteDetailsSchema} from "../relay/relayQueriesFascicoloPresidenteDetails";
import {
  relayQueriesFascicoloPresidenteDetails_FascicoloPresidenteDetailsQuery
} from "@/generated/relayQueriesFascicoloPresidenteDetails_FascicoloPresidenteDetailsQuery.graphql";
import UdienzaDatiHeader from "../scrivania/UdienzaDatiHeader";
import { EsitoParzialeSchema } from "../relay/relayQueries";
import { relayQueries_EsitoParzialeQuery } from "@/generated/relayQueries_EsitoParzialeQuery.graphql";
import ButtonMenu from "../shared/ButtonMenu";
import RedazioneOnline from "./RedazioneOnline";
import UploadProvvedimento from "../calendar/UploadProvvedimento";
import { DetailUdienza } from '../calendar/DetailUdienza';
import ProvvedimentiTable from './ProvvedimentiTable';
import ProvvedimentiTablePresidente from './ProvvedimentiTablePresidente';
import DatiFascicolo from './DatiFascicolo';
import DatiProvvedimento from './DatiProvvedimento';
import CodaButton from '../shared/CodaButton';

const buttonCreaNuovoProps = {
  name: 'Crea nuovo',
  menuNames: [
    {title: 'Redazione online', value: 'redazione'},
    {
      title: 'Importa documento',
      value: 'importaDocumento',
    },
  ],
  endIcon: <ExpandMoreIcon/>,
};

const mainLinks = {
  margin: 0,
  color: '#308A7D',
  textDecoration: 'underline',
  textDecorationColor: 'rgba(48, 138, 125, 0.4)',
  cursor: 'pointer',
};

const styledStatoSic = {
  background: '#E9D7D7',
  color: '#8E3030',
  fontSize: '15px',
  borderRadius: '5px',
  height: '20px',
  alignItems: 'center',
  padding: '0 7.1px 0 7.1px',
  marginTop: '5px',
  width: '95px',
};

const styledStato = {
  background: '#D7E9E6',
  color: '#308A8E',
  fontSize: '15px',
  borderRadius: '5px',
  height: '20px',
  alignItems: 'center',
  padding: '0 10px 0 10px',
  marginTop: '5px',
  width: '95px',
};

type CheckStatoSicType = {
  dataMinuta: any | null;
  dataPubblicazione: any | null;
  idUdienza: number | null;
  nrg: number | null;
  numRaccoltaGenerale: number | null;
  numRaccoltaGeneraleString: string | null;
  statoProvvedimento: ProvvedimentiStatoEnum | null;
};

export default function DetailsFascicolo({ruolo} : DetailFascicoloProps) {

  const router = useRouter();
  const theme: any = useTheme();
  const [ricorso, setRicorso] = useState<any>();
  const [udienza, setUdienza] = useState<any>();
  const [stato, setStato] = useState<any>();
  const [nrg, setNrg] = useState<string>();
  const [numRaccGen, setNumRaccGen] = useState<string>();
  const [checkStatoSic, setCheckStatoSic] = useState<CheckStatoSicType>();
  const [creaNuovo, setCreaNuovo] = useState<any>(false);
  const [ricorsoUdienza, setRicorsoUdienza] = useState<any>();
  const [idUdienza, setIdUdienza] = useState<string>();
  const [valPonderale, setValPonderale] = useState<number>(0);
  const [valPondComplessivo, setValPondComplessivo] = useState<number>(0);
  const [termineDeposito, setTermineDeposito] = useState<any>();
  const [isEstensore, setIsEstensore] = useState<boolean>(false);
  const [relatore, setRelatore] = useState<any>();
  const [isRelatore, setIsRelatore] = useState<boolean>(false);
  const [provvedimento, setProvvedimento] = useState<any>();
  const [esitoParzialeProv, setEsitoParzialeProv] = useState<any>();
  const [dataUdienza, setDataUdienza] = useState<string>();
  const [refreshCoda, setRefreshCoda] = useState<boolean>(false);

  const { t } = useTranslation();

  const {id, params} = router.query;

  const datiProvvedimentoProps = {
    provvedimento: provvedimento,
    ricorsoUdienza: ricorsoUdienza,
    checkStatoOnSIC: udienza?.checkStatoOnSIC,
    nomeFile: nrg ?? '',
    numRaccGen: numRaccGen ?? '',
  };

  const { data: dataPresidente, isLoading: isLoadingPresidente, retry: retryPresidente } =
    useQuery<relayQueriesFascicoloPresidenteDetails_FascicoloPresidenteDetailsQuery>(
      FascicoloPresidenteDetailsSchema,
      {
        idUdin: Number(id),
        nrg: Number(params),
      },
      {
        fetchPolicy: NETWORK_ONLY,
        skip: ruolo !== RuoloEnum.PRESIDENTE
      }
    );

  const { data: dataEstensore, isLoading: isLoadingEstensore, retry: retryEstensore } =
    useQuery<relayQueriesFascicoloDetails_FascicoloDetailsQuery>(
      FascicoloDetailsSchema,
      {
        idUdin: Number(id),
        nrg: Number(params),
      },
      {
        fetchPolicy: NETWORK_ONLY,
        skip: ruolo !== RuoloEnum.ESTENSORE
      }
    );

  const {data: dataEsitoParzialeEstensore} =
    useQuery<relayQueries_EsitoParzialeQuery>(
      EsitoParzialeSchema,
      {
        ricorsoUdienza: Number(
          dataEstensore?.udienzeWithProvvedimentoDet?.ricorsiUdienza?.find(
            (data: any) => data?.ricorso?.nrg == params
          )?.idRicudien
        ),
      },
      {
        fetchPolicy: NETWORK_ONLY,
        skip: ruolo !== RuoloEnum.ESTENSORE ||
              !dataEstensore?.udienzeWithProvvedimentoDet?.ricorsiUdienza?.find(
                (data: any) => data?.ricorso?.nrg == params
        )?.esitoParziale?.esitoParziale,
      }
    );

  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const [modalProps, setModalProps] = useState<any>({
    isOpen: false,
    title: '',
    content: <></>,
    onClose: closeModal,
    showCancelButton: 'Chiudi',
  });

  const creaNuovoModal = (param: string) => {
    let content, title, showCancelButton;
    if (param == 'redazione') {
      showCancelButton = false;
      content = (
        <RedazioneOnline
          data={{ idUdienza: idUdienza, nrg: params }}
          closeModal={closeModal}
        />
      );
      title = t('fascicolo.detailFascicolo.redazioneNuovoProvvedimento');
    } else if (param == 'importaDocumento') {
      title = t('fascicolo.detailFascicolo.importaDocumento');
      showCancelButton = false;
      content = (
        <UploadProvvedimento
          data={{ ricorso: ricorso, id: idUdienza, nrg: params }}
          closeModal={closeModal}
        />
      );
    } else {
      title = t('fascicolo.detailFascicolo.collegioUdienza') + dataUdienza;
      content = <DetailUdienza idUdienza={idUdienza} nrg={ricorso?.nrg} />;
    }

    setModalProps({
      ...modalProps,
      content,
      isOpen: true,
      title,
      showCancelButton,
    });
  };

  const setCreaNuovoEvent = (data: boolean) => {
    if (
      udienza.checkStatoOnSIC?.statoProvvedimento == StatoProvvedimentiEnum.RIUNITO ||
      udienza.checkStatoOnSIC?.statoProvvedimento == StatoProvvedimentiEnum.RIUNITO_CARTACEO
    ) {
      setCreaNuovo(false);
    } else {
      setCreaNuovo(data);
    }
  };

  function getUdienzaNameLink() {
    if (ruolo === RuoloEnum.ESTENSORE){
      return `${t('fascicolo.detailFascicolo.udienza')} 
              ${udienza?.sezione?.descrizione} - 
              ${formatDate(udienza?.dataUdienza, 'DD MMMM YYYY')} - 
              ${udienza?.tipoUdienza?.sigla} - 
              Collegio: ${udienza?.aula?.sigla}`;
    }else{
      return `${t('fascicolo.detailFascicoloPresidente.udienza')}: 
              ${udienza?.sezione?.sigla} - 
              ${formatDate(udienza?.dataUdienza, 'DD MMMM YYYY')} - 
              ${udienza?.tipoUdienza?.sigla} - 
              ${t('fascicolo.detailFascicoloPresidente.collegio')}: 
              ${udienza?.aula?.sigla}`
    }
  }


  const renderingBreadcrumb = () => {
    const udienzaNameLink = getUdienzaNameLink();
    return (<Typography variant="h2">
      {ruolo == RuoloEnum.ESTENSORE ? (
        <NsBreadcrumbs
          linkItems={[
            {
              href: '/calendario',
              name: t('fascicolo.detailFascicolo.calendarioUdienze')
            },
            {
              href: `/calendario?id=${id}&params=${ricorso?.nrg}`,
              name: udienzaNameLink
            }
          ]}
          linkUnderline="always"
          title=''
        />
      ) : (
        <NsBreadcrumbs
          linkItems={[
            {
              href: '/scrivania',
              name: t('scrivania.minuteDaVerificare')
            },
            {
              href:`/scrivania/${idUdienza}`,
              name: udienzaNameLink
            }
          ]}
          linkUnderline="always"
          title=''
        />
      )}
      {t('fascicolo.detailFascicolo.fascicolo')} {nrg}{' '}
      { renderingStatoProvvedimento() }
    </Typography>)
  };

  const renderingStatoProvvedimento = () => {
    switch (stato) {
      case StatoProvvedimentiEnum.PUBBLICATA:
       return <Box component={'span'} sx={styledStato}>
          {t('fascicolo.detailFascicolo.pubblicato')}
        </Box>;
      case StatoProvvedimentiEnum.PUBBLICATO_SIC:
        return (
          <>
            {'|'}{' '}
            <Box component={'span'} sx={styledStatoSic}>
              {t('fascicolo.detailFascicolo.pubblicatoSic')}
            </Box>{' '}
            <Box component={'span'} sx={{ fontSize: '0.7em' }}>
              N. Racc. Gen. : {numRaccGen}
              {', '}
              {formatDate(checkStatoSic?.dataPubblicazione, 'DD/MM/YYYY')}
            </Box>
          </>
        );
      case StatoProvvedimentiEnum.PROVV_DEPOSITATO_SIC:
        return (<>
          {'|'}{' '}
          <Box component={'span'} sx={styledStatoSic}>
            {t('fascicolo.detailFascicolo.provvDepositatoSic')}
          </Box>{' '}
          <Box component={'span'} sx={{ fontSize: '0.7em' }}>
            {formatDate(checkStatoSic?.dataPubblicazione, 'DD/MM/YYYY')}
          </Box>
        </>
        );
      case StatoProvvedimentiEnum.MINUTA_DEPOSITATA_SIC:
        return (
          <>
            {'|'}{' '}
            <Box component={'span'} sx={styledStatoSic}>
              {t('fascicolo.detailFascicolo.depositatoSic')}
            </Box>{' '}
            <Box component={'span'} sx={{ fontSize: '0.7em' }}>
              {formatDate(checkStatoSic?.dataMinuta, 'DD/MM/YYYY')}
            </Box>{' '}
          </>
        );
    }    
  }

  const rederingProvvEstensore = () => {
    if (ricorsoUdienza?.esitoParziale?.esitoParziale) {
      return (
        <Box mt={3} p={2} border={theme.custom.borders[0]}>
          <Typography variant="h1" mt={1}>
            {t('fascicolo.detailFascicolo.provvedimenti')}
          </Typography>
          <Typography variant="h3" mt={2} style={{fontWeight: 500}}>
            {t('fascicolo.detailFascicolo.provvedimentoParziale')} {nrg}
          </Typography>
          <Typography variant="h1" mt={2}>
            {t('fascicolo.detailFascicolo.motivo')}
          </Typography>
          <Typography
            variant="h3"
            mt={2}
            style={{fontWeight: 500, textTransform: 'capitalize'}}
          >
            {esitoParzialeProv?.motivo?.toLowerCase() || ''}
          </Typography>
          {esitoParzialeProv?.descrizione ? (
            <>
              <Typography variant="h1" mt={2}>
                {t('fascicolo.detailFascicolo.descrizione')}
              </Typography>
              <Typography variant="h3" mt={1} style={{fontWeight: 500}}>
                {esitoParzialeProv.descrizione.toLowerCase()}
              </Typography>
            </>
          ) : (
            ''
          )}
        </Box>
      );
    }
    if (isEstensore) {
      return (
        <Grid
          border={theme.custom.borders[0]}
          mt={2}
          item
          container
          justifyContent="space-between"
          p={2}
          sx={{background: '#FFFFFF'}}
        >
          <Typography variant="h1">
            {t('fascicolo.detailFascicolo.provvedimenti')}
          </Typography>
          {creaNuovo && (
            <ButtonMenu
              handleButton={(data) => creaNuovoModal(data)}
              {...buttonCreaNuovoProps}
            />
          )}
          <Grid item xs={12} mt={3}>
            <ProvvedimentiTable
              creaNuovo={(data: boolean) => setCreaNuovoEvent(data)}
              data={dataEstensore}
              refreshCoda={() => setRefreshCoda((prevState) => !prevState)}
              refreshPage={() => retryEstensore()}
            />
          </Grid>
        </Grid>
      );
    }
    return (
      <Box mt={3} p={2} border={theme.custom.borders[0]}>
        <Typography variant="h1" mt={1}>
          {t('fascicolo.detailFascicolo.provvedimenti')}
        </Typography>
        <Typography variant="h2" mt={3}>
          {t('fascicolo.detailFascicolo.provvedimentoRedattoEstensore')}
        </Typography>
      </Box>
    );
  };

  const rederingProvvPresidente = () => {
    return (
      <Grid
        border={theme.custom.borders[0]}
        mt={2}
        item
        container
        justifyContent="space-between"
        p={2}
        sx={{background: '#FFFFFF'}}
      >
        <Typography variant="h1">
          {t('fascicolo.detailFascicoloPresidente.provvedimenti')}
        </Typography>
        <Grid item xs={12} mt={3}>
          <ProvvedimentiTablePresidente
            refreshPage={() => retryPresidente()}
            data={dataPresidente}
          />
        </Grid>
      </Grid>
    )
  }

  const renderingDatiUdienzaAndProvv = () => {
    return(
      <>
        <UdienzaDatiHeader
          valPondale={valPonderale}
          valPondComplessivo={valPondComplessivo}
          termineDeposito={termineDeposito}
          showValore={true}
          idUdienza={Number(idUdienza)}
          nrg={ricorso?.nrg}
        />
        { ruolo === RuoloEnum.ESTENSORE ? (rederingProvvEstensore() ) : ( rederingProvvPresidente()) }
      </>
    );
  }

  const codaDepositoButton = () => {
    if(ruolo === RuoloEnum.ESTENSORE){
      return(
        <CodaButton
          refreshCoda={refreshCoda}
          refreshPage={() => retryEstensore()}
          ruolo={'ESTENSORE'}
        />
      )
    }
    return(
      <CodaButton
        ruolo={'PRESIDENTE'}
        refreshPage={() => retryPresidente()}
      />
    )
  }

  /* funzione che valorizza gli hook comuni al ruolo: ESTENSORE - PRESIDENTE */
  function setDettagliRicorsoUdienza(ricUdien: any, udien: any) {

    setRicorsoUdienza(ricUdien);

    const ricorso = ricUdien?.ricorso;
    setRicorso(ricorso);

    const number = ricorso?.numero;
    const year = ricorso?.anno;
    setNrg(`${number}/${year}`);

    const checkStatoOnSic = udien?.checkStatoOnSIC;

    setStato(checkStatoOnSic?.statoProvvedimento);

    setIdUdienza(udien?.idUdien);

    setNumRaccGen(checkStatoOnSic?.numRaccoltaGeneraleString ?? undefined);

    setCheckStatoSic(checkStatoOnSic || undefined);

    setValPonderale(ricUdien?.ricorso?.spoglio?.valPond || 0)

    setValPondComplessivo(ricUdien?.valPondComplessivo || 0);

    setTermineDeposito(formatDate(udien?.termineDeposito, 'DD MM YYYY'));

    setIsEstensore(ricUdien?.isEstensore || false);

    if (isEstensore) {
      const relatore = ricUdien?.relatore;
      setRelatore(relatore?.anagraficaMagistrato);
    }
    setIsRelatore(ricUdien?.isRelatore || false);
  }

  useEffect(() => {
    if(ruolo === RuoloEnum.ESTENSORE) {
      const udienza = dataEstensore?.udienzeWithProvvedimentoDet;
      setUdienza(udienza);

      if (dataEsitoParzialeEstensore?.esitoParziale) {
        setEsitoParzialeProv(dataEsitoParzialeEstensore.esitoParziale);
      }

      setDataUdienza(formatDate(udienza?.dataUdienza, 'DD MMMM YYYY'));

      const provvedimentoCurrent = udienza?.provvedimentiByNrg?.find(
        (prov: any) => prov.nrg == params
      );
      setProvvedimento(provvedimentoCurrent);

      // Serve per la gestione del tasto 'Crea Nuovo' in caso di riuniti o riuniti cartacei
      setCreaNuovo(true);

      const ricorsiUdienza = udienza?.ricorsiUdienza ?? [];

      const ricUdien = ricorsiUdienza.find(
        (data: any) => data?.ricorso?.nrg == params
      );

      setDettagliRicorsoUdienza(ricUdien, udienza);
    }
    else {

      const udienza = dataPresidente?.udienzeWithProvvedimentoDet;
      setUdienza(udienza);

      const provvedimentoCurrent = udienza?.provvedimentoByNrgPerPresidente?.find(
        (prov: any) => prov.nrg == params
      );

      setProvvedimento(provvedimentoCurrent);

      const ricUdien = dataPresidente?.udienzeWithProvvedimentoDet.ricorsiUdienza?.find(
        (data: any) => data?.ricorso?.nrg == params
      );

      setDettagliRicorsoUdienza(ricUdien, udienza);}

  }, [dataPresidente, dataEstensore, dataEsitoParzialeEstensore]);

  function renderingDatiFascicoloAndDatiProvv() {
    return <>
      {udienza && (
        <DatiFascicolo
          ricorsoUdienza={ricorsoUdienza}
          checkStatoOnSIC={udienza?.checkStatoOnSIC}
          relatore={relatore}
          isRelatore={isRelatore}
          isEstensore={isEstensore}
          provvedimento={provvedimento}
        />
      )}
      {udienza && ricorsoUdienza && (
        <DatiProvvedimento {...datiProvvedimentoProps}
        />
      )}
    </>;
  }

  const renderingEstensore = () => {
    return (
      <Grid container p={2} justifyContent="space-between">
        <Grid item xs={12} md={12} lg={12}>
          { renderingBreadcrumb() }
        </Grid>
        <Grid item xs={12} md={12} lg={6}>
          {renderingDatiUdienzaAndProvv()}
        </Grid>
        <Grid item xs={12} md={12} lg={6}>
          { renderingDatiFascicoloAndDatiProvv() }
        </Grid>
        {codaDepositoButton()}
        <DialogModal {...modalProps} />
      </Grid>
    );
  };

  function renderingPresidente() {
    return (
      <Grid container p={3} justifyContent="space-between">
        <Grid item>
          { renderingBreadcrumb() }
        </Grid>
        <Grid container item>
          <Grid item xs={8}>
            { renderingDatiUdienzaAndProvv() }
          </Grid>
          <Grid item xs={4}>
            { renderingDatiFascicoloAndDatiProvv() }
          </Grid>
        </Grid>
        { codaDepositoButton() }
        <DialogModal {...modalProps} />
      </Grid>
    );
  }

  const dettaglioFascicolo = () => {
    if(ruolo === RuoloEnum.ESTENSORE)
      return renderingEstensore();
    else
      return renderingPresidente();
  };

  return isLoadingEstensore || isLoadingPresidente ? (
    <NsFullPageSpinner isOpen={true} value={1}/>
  ) : (
    <>
      { dettaglioFascicolo() }
    </>
  );
}
