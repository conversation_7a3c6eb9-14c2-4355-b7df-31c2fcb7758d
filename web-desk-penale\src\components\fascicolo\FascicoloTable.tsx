import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EditIcon from '@mui/icons-material/Edit';
import InfoIcon from '@mui/icons-material/Info';
import { Box, Checkbox } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Column } from 'src/interfaces';
import RelayTable from '../shared/RelayTable';
import { getStateNames } from '../shared/Utils';

interface Data {
  tipologia: string;
  autore: string;
  stato: string;
  ultima: string;
  oscuramento: string;
}

function createData(
  tipologia: string,
  autore: string,
  stato: string,
  ultima: string,
  oscuramento: string
): Data {
  return { tipologia, autore, stato, ultima, oscuramento };
}

const renderTipologia = (cell: any, row: any) => {
  const style = {
    border: '1px solid',
    borderRadius: '5px',
    background: '#C8E3F7',
    color: '#005694',
    textAlign: 'center',
  };
  return (
    <TableCell key={cell.id} align={cell.align}>
      <Box sx={style}>{row.tipologia}</Box>
    </TableCell>
  );
};

const renderStato = (cell: any, row: any) => {
  return (
    <TableCell key={cell.id} align={cell.align}>
      <Box display="flex">
        <InfoIcon sx={{ color: '#C4C4C4' }} />{' '}
        <Box ml={1} component="span">
          {' '}
          {getStateNames('RELATORE', row.stato)}
        </Box>
      </Box>
    </TableCell>
  );
};

const renderAzioni = (cell: any, row: any) => {
  return (
    <TableCell key={cell.id} align={cell.align}>
      <Box display="flex">
        <EditIcon sx={{ color: '#000000' }} />{' '}
        <DeleteOutlineIcon sx={{ color: '#000000' }} />
      </Box>
    </TableCell>
  );
};

const renderHeadStato = (name: string) => {
  return (
    <Box display="flex">
      <InfoIcon sx={{ color: '#FFFFF' }} /> <Box ml={1}>{name}</Box>
    </Box>
  );
};

export default function FascicoloTable() {
  const { t } = useTranslation();
  const [selected, setSelected] = useState<any[]>([]);

  const handleSelectAllClick = (
    event: React.ChangeEvent<HTMLInputElement>,
    rows: any
  ) => {
    if (event.target.checked) {
      const newSelected = rows.map((n: any, i: number) => i);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleClick = (_: React.MouseEvent<unknown>, index: number) => {
    const array = selected.includes(index)
      ? selected.filter((i) => i !== index)
      : [...selected, index];
    setSelected([...array]);
  };

  const renderCheckbox = (cell: any, row: any, index: any) => {
    const isSelected = selected.includes(index);
    return (
      <TableCell>
        <Checkbox
          color="primary"
          checked={isSelected}
          onClick={(event) => handleClick(event, index)}
        />
      </TableCell>
    );
  };

  const renderHeadCheckbox = (name: string, rows: any) => {
    return (
      <Checkbox
        color="primary"
        checked={selected.length > 0 ? true : false}
        onChange={(event) => handleSelectAllClick(event, rows)}
        inputProps={{
          'aria-label': 'select all',
        }}
      />
    );
  };

  const columns: Column[] = [
    {
      id: 'checked',
      minWidth: 170,
      label: '',
      render: renderCheckbox,
      renderHeadCell: renderHeadCheckbox,
    },
    {
      id: 'tipologia',
      minWidth: 170,
      label: t('common.tipologia') as string,
      render: renderTipologia,
    },
    {
      id: 'autore',
      align: 'left',
      label: t('common.autore') as string,
      minWidth: 170,
    },
    {
      id: 'stato',
      minWidth: 170,
      label: t('common.statoProvvedimento') as string,
      align: 'left',
      render: renderStato,
      renderHeadCell: renderHeadStato,
    },
    {
      id: 'ultima',
      minWidth: 170,
      label: t('common.ultimaModifica') as string,
      align: 'left',
    },
    {
      id: 'oscuramento',
      minWidth: 170,
      label: t('common.oscuramento') as string,
      align: 'left',
    },
    {
      id: 'azioni',
      minWidth: 170,
      label: t('common.azioni') as string,
      align: 'left',
      render: renderAzioni,
    },
  ];

  const rows = [
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
    createData('SENTENZA', 'Magistrato', 'In bozza', '27.02.2023', 'Si'),
  ];

  return <RelayTable rows={rows} columns={columns} />;
}
