import DownloadIcon from '@mui/icons-material/Download';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import { Box, Grid, Typography, useTheme } from '@mui/material';
import { useNotifier } from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import MainModal from '../shared/MainModal';
import { useConfig } from '../shared/configuration.context';


export default function FileBusta({ provvedimentiDaFirmare }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();

  const { notify } = useNotifier();

  const { servizi } = useConfig();

  const serviceUrl = `${servizi}`;

  const closeModal = () => {
    setModalProps({ ...modalProps, modal: false });
  };

  const [modalProps, setModalProps] = useState({
    modal: false,
    style: {
      options: theme.custom.options
    },
    closeModal,
    title: '',
    body: <></>,
  });

  const previewPdfByIdCat = (provv: any) => {
    const idCat = provv.idCategoria;
    axios
      .get(serviceUrl + '/provvedimento/downloadByIdCat/' + idCat, {
        responseType: 'blob',
      })
      .then((response: any) => {
        const tipoFile = provv.tipoFile;
        const pdfBlob = new Blob([response.data], {
          type: 'application/' + tipoFile,
        });
        const pdfUrl = window.URL.createObjectURL(response.data);
        window.open(pdfUrl, '_blank');
      })
      .catch((error: any) => {
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      });
  };
  const downloadPdfByIdCat = (provv: any) => {
    const idCat = provv.idCategoria;
    axios
      .get(serviceUrl + '/provvedimento/downloadByIdCat/' + idCat, {
        responseType: 'blob',
      })
      .then((response: any) => {
        const tipoFile = provv.tipoFile;
        const nomeFile = provv.nomeFile;
        const pdfBlob = new Blob([response.data], {
          type: 'application/' + tipoFile,
        });
        const downloadUrl = window.URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.download = nomeFile;
        link.href = downloadUrl;
        link.click();
      })
      .then(() => {
        notify({
          message:
            t('fascicolo.fileBusta.downloadDelFile') +
            ' ' +
            provv.tipoFile +
            ' ' +
            t('fascicolo.fileBusta.avvenutoConSuccesso'),
          type: 'success',
        });
      })
      .catch((error: any) => {
        notify({
          message: 'Errore nel download del file' + provv.tipoFile,
          type: 'error',
        });
      });
  };

  function compareFiles(a: any, b: any) {
    const fileExtensionA = a.nomeFile.split('.').pop();
    const fileExtensionB = b.nomeFile.split('.').pop();
    if (!a.oscurato && fileExtensionA === 'pdf') {
      return -1;
    }
    if (!b.oscurato && fileExtensionB === 'pdf') {
      return 1;
    }
    if (a.oscurato && fileExtensionA === 'pdf') {
      return -1;
    }
    if (b.oscurato && fileExtensionB === 'pdf') {
      return 1;
    }
    if (fileExtensionA === 'xml' || fileExtensionB === 'xml') {
      return 1;
    }
    return 0;
  }

  return (
    <Grid item xs={12} md={12}>
      {provvedimentiDaFirmare
        ?.sort(compareFiles)
        .map((provv: any, i: number) => {
          return (
            <Grid
              container
              mt={3}
              justifyContent="space-between"
              alignItems="center"
              border={theme.custom.borders[0]}
              p={2}
              key={i}
            >
              <Box display="flex" alignItems="center">
                <InsertDriveFileIcon color="primary" />
                <Typography ml={2} mr={2} variant="h5">
                  {provv.nomeFile}
                </Typography>
                {provv.tipoFile === 'pdf' && provv.oscurato && (
                  <Typography sx={{...theme.custom.atto, ...theme.custom.atto.secondary}}>
                    {t('fascicolo.fileBusta.oscurato')}
                  </Typography>
                )}
                {provv.tipoFile === 'pdf' && !provv.oscurato && (
                  <Typography sx={{...theme.custom.atto, ...theme.custom.atto.secondary}}>
                    {t('fascicolo.fileBusta.nonOscurato')}
                  </Typography>
                )}
              </Box>
              <Box display="flex">
                {provv.tipoFile === 'pdf' && (
                  <Box
                    mr={1}
                    ml={2}
                    sx={theme.custom.options}
                    display={{ xs: 'none', md: 'flex' }}
                    onClick={() => previewPdfByIdCat(provv)}
                    style={{ cursor: 'pointer' }}
                  >
                    <RemoveRedEyeIcon fontSize="small" />
                  </Box>
                )}
                <Box
                  sx={theme.custom.options}
                  onClick={() => downloadPdfByIdCat(provv)}
                  style={{ cursor: 'pointer' }}
                >
                  <DownloadIcon fontSize="small" />
                </Box>
              </Box>
            </Grid>
          );
        })}
      <MainModal {...modalProps} />
    </Grid>
  );
}
