import { ProvvedimentiTipoEnum } from '../../types/types';
import { relayQueries_SettingsQuery } from '@/generated/relayQueries_SettingsQuery.graphql';
import { Button, Grid, InputAdornment, useTheme } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTextInput,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useForm } from 'relay-forms';
import {
  NETWORK_ONLY,
  STORE_OR_NETWORK,
  useMutation,
  useQuery,
} from 'relay-hooks';
import { graphql } from 'relay-runtime';
import { FirmaDepositaProps } from 'src/interfaces';
import { GetAuthUser } from '../shared/Utils';
import { useConfig } from '../shared/configuration.context';
import { SettingsQuery } from '../relay/relayQueries';
import { MinimalRicorsoDetailSchema } from '../relay/relayQueriesRicorsoDetails';
import { relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery } from '@/generated/relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery.graphql';

export const ROOT_QUERY = MinimalRicorsoDetailSchema;
export const ROOT_SETTINGS_QUERY = SettingsQuery;

const mutationFirmaDeposita = graphql`
  mutation FirmaDepositaMutation($input: FirmaProvvLavorazioneInput!) {
    firmaEDeposita(firmaProvvLavorazioneInput: $input) {
      idAutore
      idProvvedimento
      dataDeposito
      idUdienza
    }
  }
`;

const hideHover = {
  ':hover': {
    backgroundColor: 'white',
  },
};

export default function FirmaDeposita({
  idUdienza,
  tipologiaProvvedimento,
  type,
  closeModal,
  daDepositare,
  provvedimentiResult,
}: FirmaDepositaProps) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [mutateChange, { loading }] = useMutation(mutationFirmaDeposita);
  const router = useRouter();
  const { data: user, status, token } = GetAuthUser();
  const { servizi, enableFirmaOtp } = useConfig();
  const [firmaOtp, setIsFirmaOtp] = useState<any>(false);
  const { id, params, idProvvedimento, calendar } = router.query;
  const [usernameFirma, setUsernameFirma] = useState<string>('');
  const [passwordFirma, setPasswordFirma] = useState<string>('');

  const { data: data1, isLoading: isLoading1 } =
    useQuery<relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery>(
      ROOT_QUERY,
      {
        idUdien: Number(idUdienza),
        nrg: Number(params),
      },
      {
        fetchPolicy: STORE_OR_NETWORK,
      }
    );

  const { data: data2, isLoading: isLoading2 } =
    useQuery<relayQueries_SettingsQuery>(
      ROOT_SETTINGS_QUERY,
      {},
      { fetchPolicy: NETWORK_ONLY }
    );

  const number = data1?.ricorsoByIdUdienAndNrg?.numero;

  const year = data1?.ricorsoByIdUdienAndNrg.anno;

  const nrgCompleto = data1?.ricorsoByIdUdienAndNrg?.nrg;

  const nrgCompletoNumber = parseInt(nrgCompleto!);

  const [tipologiaProvvedimento2, setTipologiaProvvedimento2] = useState<
    string | null
  >('');

  const fixTipologiaProvvedimento = (tipologiaProvvedimento: string) => {
    /*    if (tipologiaProvvedimento == 'ORDINANZA') {
      setTipologiaProvvedimento2('MINUTA_ORDINANZA');
    } else if (tipologiaProvvedimento == 'SENTENZA') {
      setTipologiaProvvedimento2('MINUTA_SENTENZA');
    }*/
    setTipologiaProvvedimento2(tipologiaProvvedimento || '');
  };

  useEffect(() => {
    setIsFirmaOtp(enableFirmaOtp);
    if (data2 && data2?.impostazioniByCf?.usernameFirma != null) {
      setUsernameFirma(data2?.impostazioniByCf?.usernameFirma);
    } else {
      setUsernameFirma('');
    }
    if (data2 && data2?.impostazioniByCf?.passwordFirma != null) {
      setPasswordFirma(data2?.impostazioniByCf?.passwordFirma);
    } else {
      setPasswordFirma('');
    }

    fixTipologiaProvvedimento(tipologiaProvvedimento as string);
  }, [data2]);

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    p: 2,
  };

  const { notify } = useNotifier();

  const [showPassword, setShowPassword] = useState(false);
  const [showOTP, setShowOTP] = useState(false);
  const [Loading, setLoading] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleOTPVisibility = () => {
    setShowOTP(!showOTP);
  };

  const { submit, reset } = useForm<any>({
    onSubmit: async (values: any) => {
      setLoading(true);
      const isDecoded = await isPasswordDecoded(values);
      const usrFirma = values.Username ? values.Username : usernameFirma;
      const pswFirma = values.Password ? values.Password : passwordFirma;

      if (!usrFirma || !pswFirma) {
        notify({
          type: 'error',
          message: 'Dati obbligatori non inseriti!',
        });
        setLoading(false);
      } else {
        if (type === 'depositoMassivo') {
          if (firmaOtp) {
            if (!values.OTP) {
              notify({
                type: 'error',
                message: 'OTP non inserito!',
              });
              setLoading(false);
            } else {
              firmaDepositaMassivo(
                usrFirma.trim(),
                pswFirma.trim(),
                values.OTP,
                isDecoded
              );
            }
          } else if (token != null) {
            firmaDepositaMassivo(
              usrFirma.trim(),
              pswFirma.trim(),
              token,
              isDecoded
            );
          } else {
            setLoading(false);
          }
        } else {
          if (firmaOtp) {
            if (!values.OTP) {
              notify({
                type: 'error',
                message: t('fascicolo._firmaDeposita.otpNonInserito'),
              });
              setLoading(false);
            } else {
              callFirmaEDeposita(
                nrgCompletoNumber!,
                true,
                tipologiaProvvedimento2 as ProvvedimentiTipoEnum,
                {
                  usernameFirma: usrFirma.trim(),
                  passwordFirma: pswFirma.trim(),
                  pinFirma: values.OTP.trim(),
                },
                {
                  codiceFiscaleMittente: user?.cf || '',
                  codiceUfficioDestinatario: '80417740588',
                  ruoloMittente: 'MAG',
                  idMsg: 'depmag:80417740588:' + user?.cf + ':15228.enc',
                },
                {
                  tipoProvvedimento: tipologiaProvvedimento2 as string,
                  annoFascicolo: year!,
                  numeroFascicolo: number!,
                }
              );
            }
          } else if (token != null) {
            callFirmaEDeposita(
              nrgCompletoNumber!,
              true,
              tipologiaProvvedimento2 as ProvvedimentiTipoEnum,
              {
                usernameFirma: usrFirma.trim(),
                passwordFirma: pswFirma.trim(),
                pinFirma: token,
              },
              {
                codiceFiscaleMittente: user?.cf || '',
                codiceUfficioDestinatario: '80417740588',
                ruoloMittente: 'MAG',
                idMsg: 'depmag:80417740588:' + user?.cf + ':15228.enc',
              },
              {
                tipoProvvedimento: tipologiaProvvedimento2 as string,
                annoFascicolo: year!,
                numeroFascicolo: number!,
              }
            );
          }
        }
      } // FINE CONTROLLO PRESENZA INSERIMENTO USERNAME E PASSWORD DI FIRMA
    },
  });

  const isPasswordDecoded = async (values: any) => {
    if (firmaOtp) {
      return false;
    } else if (
      values.Password == null &&
      data2?.impostazioniByCf?.passwordFirma != null &&
      data2?.impostazioniByCf?.passwordFirma != ''
    ) {
      return true;
    } else if (
      (data2?.impostazioniByCf?.passwordFirma != '' &&
        values.Password != null &&
        data2?.impostazioniByCf?.passwordFirma != values.Password) ||
      data2?.impostazioniByCf?.passwordFirma == null ||
      data2?.impostazioniByCf?.passwordFirma == ''
    ) {
      return false;
    } else {
      return true;
    }
  };

  const firmaDepositaMassivo = async (
    usernameFirma: string,
    passwordFirma: string,
    pinFirma: string,
    isDecoded: Boolean
  ) => {
    await axios
      .post(`${servizi}/presidente/firmaEDeposita`, {
        credenziali: {
          usernameFirma,
          passwordFirma,
          pinFirma,
        },
        isDecoded,
        daDepositare,
      })
      .then((response) => {
        provvedimentiResult!(response.data);
        // Close the modal after successful signing and depositing
        if (closeModal) {
          closeModal();
        }
      })
      .catch(function (error) {
        if (
          error?.response?.data?.message &&
          error?.response?.data?.message == 'DEPOSITO_INTERNAL_ERROR'
        ) {
          notify({
            message: t('server.errorCode.DEPOSITO_INTERNAL_ERROR'),
            type: 'error',
          });
          return;
        }
        if (
          error?.response?.data?.message &&
          error?.response?.data?.message === 'ID_DEPOSITO_NULL'
        ) {
          notify({
            message: t('server.errorCode.ID_DEPOSITO_NULL'),
            type: 'error',
          });
          return;
        }
        if (
          error?.response?.data?.message &&
          error?.response?.data?.message != 'SERVIZI_DEPOSITO_ERROR'
        ) {
          notify({
            message: error?.response?.data?.message,
            type: 'error',
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const callFirmaEDeposita = (
    nrg: number,
    firmato: true,
    tipologiaProvvedimento: ProvvedimentiTipoEnum,
    credenzialiFirmaRemota: {
      usernameFirma: string;
      passwordFirma: string;
      pinFirma: string;
    },
    bustaMakerData: {
      codiceFiscaleMittente: string;
      codiceUfficioDestinatario: string;
      ruoloMittente: string;
      idMsg: string;
    },
    generazioneDatiAtto: {
      tipoProvvedimento: string;
      annoFascicolo: number;
      numeroFascicolo: number;
    }
  ) => {
    return mutateChange({
      variables: {
        input: {
          nrg: nrg,
          idProvvedimento: idProvvedimento,
          firmato: firmato,
          tipologiaProvvedimento: tipologiaProvvedimento2,
          credenzialiFirmaRemota: credenzialiFirmaRemota,
          bustaMakerData: bustaMakerData,
          generazioneDatiAtto: generazioneDatiAtto,
        },
      },
    })
      .then((_) => {
        notify({
          message: 'Firma e deposito avvenuti con successo',
          type: 'success',
        });
        if (closeModal) {
          closeModal();
        }
        if (calendar != 'true') {
          router.push({
            pathname: '/provvedimentodepositato',
            query: {
              idUdienza: idUdienza,
              params: nrg,
              tipologiaProvvedimento: tipologiaProvvedimento2,
            },
          });
        }
      })
      .catch(function (error) {
        if (
          error?.source?.errors[0]?.message &&
          error?.source?.errors[0]?.message === 'DEPOSITO_INTERNAL_ERROR'
        ) {
          notify({
            message: t('server.errorCode.DEPOSITO_INTERNAL_ERROR'),
            type: 'error',
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <form onSubmit={submit}>
        <Grid container spacing={2}>
          {usernameFirma &&
          passwordFirma &&
          usernameFirma != '' &&
          passwordFirma != '' ? (
            <Grid item xs={12}>
              <NsTextInput
                label={t('fascicolo._firmaDeposita.username')}
                name="Username"
                type="text"
                onChange={(e) => setUsernameFirma(e.target.value)}
                placeholder=""
                defaultValue={usernameFirma}
              />
              <NsTextInput
                label="Password"
                name="Password"
                type={showPassword ? 'text' : 'password'}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <NsButton
                        sx={hideHover}
                        onClick={togglePasswordVisibility}
                        variant="text"
                      >
                        {showPassword
                          ? t('form.buttons.nascondi')
                          : t('form.buttons.mostra')}
                      </NsButton>
                    </InputAdornment>
                  ),
                }}
                onChange={(e) => {
                  setPasswordFirma(e.target.value);
                }}
                placeholder=""
                defaultValue={passwordFirma}
              />
              {firmaOtp && (
                <NsTextInput
                  label={t('fascicolo._firmaDeposita.otp')}
                  name="OTP"
                  type={showOTP ? 'text' : 'password'}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <NsButton onClick={toggleOTPVisibility}>
                          {showOTP ? 'Hide' : 'Show'}
                        </NsButton>
                      </InputAdornment>
                    ),
                  }}
                  defaultValue=""
                />
              )}
            </Grid>
          ) : (
            <Grid item xs={12}>
              <NsTextInput
                label="Username"
                name="Username"
                type="text"
                onChange={(e) => setUsernameFirma(e.target.value)}
                placeholder=""
                defaultValue=""
                autoComplete="false"
              />
              <NsTextInput
                label={t('fascicolo._firmaDeposita.password')}
                name="Password"
                type={showPassword ? 'text' : 'password'}
                onChange={(e) => {
                  setPasswordFirma(e.target.value);
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <NsButton onClick={togglePasswordVisibility}>
                        {showPassword ? 'Hide' : 'Show'}
                      </NsButton>
                    </InputAdornment>
                  ),
                }}
                placeholder=""
                autoComplete="false"
                defaultValue=""
              />
              {firmaOtp && (
                <NsTextInput
                  label={t('fascicolo._firmaDeposita.otp')}
                  name="OTP"
                  type={showOTP ? 'text' : 'password'}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Button onClick={toggleOTPVisibility}>
                          {showOTP ? 'Hide' : 'Show'}
                        </Button>
                      </InputAdornment>
                    ),
                  }}
                  defaultValue=""
                />
              )}
            </Grid>
          )}
          <Grid item xs={6}>
            <NsButton
              variant="outlined"
              color="primary"
              onClick={closeModal}
              type="button"
            >
              {t('fascicolo._firmaDeposita.annulla')}
            </NsButton>
          </Grid>
          <Grid item xs={6}>
            <NsButton variant="contained" color="primary" type="submit">
              {t('fascicolo._firmaDeposita.firmaEDeposita')}
            </NsButton>
          </Grid>
        </Grid>
      </form>
      {Loading && <NsFullPageSpinner isOpen value={1} />}
    </>
  );
}
