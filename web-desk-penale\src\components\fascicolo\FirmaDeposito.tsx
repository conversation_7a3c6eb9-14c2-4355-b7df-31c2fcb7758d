import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import MainModal from '../shared/MainModal';
import { useConfig } from '../shared/configuration.context';
import FileBusta from './FileBusta';
import FirmaDeposita from './FirmaDeposita';
import { MinimalRicorsoDetailSchema } from '../relay/relayQueriesRicorsoDetails';
import { relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery } from '@/generated/relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery.graphql';

import { tipoProvvedimentoToString } from '../shared/Utils';
import { ProvvedimentiTipoEnum } from '../../types/types';

export const ROOT_QUERY = MinimalRicorsoDetailSchema;

export default function FirmaDeposito() {
  const [loading, setLoading] = useState<boolean>(false);
  const { notify } = useNotifier();
  const { t } = useTranslation();
  const theme: any = useTheme();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { idUdienza, params, idProvvedimento, uploadedFile } = router.query;
  const { data } =
    useQuery<relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery>(
      ROOT_QUERY,
      {
        idUdien: Number(idUdienza),
        nrg: Number(params),
      },
      {
        fetchPolicy: STORE_OR_NETWORK,
      }
    );

  const number = data?.ricorsoByIdUdienAndNrg?.numero;
  const year = data?.ricorsoByIdUdienAndNrg?.anno;
  const nrg = `${number}/${year}`;

  /* Hook created to catch the state of provvedimento.
   *I've added a new properties (stato) in query relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery
   */
  const [statoProvvedimento, setStatoProvvedimento] = useState<
    string | null | undefined
  >(null);

  const [checked, setChecked] = useState(false);
  const [provvedimentiDaFirmare, setProvvedimentiDaFirmare] = useState([]);

  const [tipoProvvedimento, setTipoProvvedimento] =
    useState<ProvvedimentiTipoEnum | null>(null);

  const style = {
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    p: 2,
  };

  const closeModal = () => {
    setModalProps({ ...modalProps, modal: false });
  };

  const { servizi } = useConfig();

  const serviceUrl = `${servizi}`;

  const serviceUrl2 = `${servizi}/provvedimento/getTipoProvvedimentoAndSemplificata/`;

  const getTestiIniziali = async () => {
    try {
      const response = await axios.get(serviceUrl2 + idUdienza + '/' + params);
      if (response.data) {
        setTipoProvvedimento(response.data.tipoProvvedimento);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const [modalProps, setModalProps] = useState({
    modal: false,
    style: style,
    closeModal,
    title: '',
    body: <></>,
  });

  const handleModal = () => {
    let body, title;
    body = (
      <FirmaDeposita
        idUdienza={idUdienza}
        tipologiaProvvedimento={tipoProvvedimento}
        closeModal={closeModal}
      />
    );
    title = t('fascicolo.firmaDeposito.firmaEDepositaIlProvvedimento');

    setModalProps({ ...modalProps, modal: true, title, body });
  };

  const getProvvedimentiDaFirmare = () => {
    if (idProvvedimento && typeof idProvvedimento === 'string')
      axios
        .get(
          serviceUrl +
            '/provvedimento/getProvvedimentiDaFirmare/' +
            idProvvedimento
        )
        .then((response) => {
          const provvedimenti = response.data;
          setProvvedimentiDaFirmare(provvedimenti);
        })
        .catch((error) => {
          console.error('Error getting provvedimenti:', error);
        })
        .finally(() => {
          setIsLoading(false);
        });
  };

  useEffect(() => {
    if (data && data.ricorsoByIdUdienAndNrg) {
      getProvvedimentiDaFirmare();
    }
    getTestiIniziali();
    setStatoProvvedimento(data?.ricorsoByIdUdienAndNrg?.provvedimento?.stato);
  }, [data]);

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };

  const addCodaDiFirma = () => {
    axios
      .get(serviceUrl + '/provvedimento/addCodaDeposito/' + idProvvedimento)
      .then((res) => {
        notify({
          message: t('fascicolo.provvedimentiTable.aggiuntoAllaCodaDiDeposito'),
          type: 'success',
        });
        router.push({
          pathname: '/calendario',
        });
      })
      .catch((error) => {
        notify({
          message: "Errore durante l'inserimento in coda di deposito",
          type: 'error',
        });
      })
      .finally(() => setLoading(false));
  };

  return (
    <>
      <Box p={2}>
        <Grid container>
          <Grid xs={3} md={3} sm={0} lg={3} mb={4} />
          <Typography variant="h1">
            {t('fascicolo.firmaDeposito.firmaEDepositoMinuta')}{' '}
            {tipoProvvedimentoToString(tipoProvvedimento)}{' '}
            {t('fascicolo.firmaDeposito.fascicolo')} {nrg}
          </Typography>
        </Grid>
        <Box mt={2}>
          <Grid container>
            <Grid xs={0} xl={3} md={3} sm={0} lg={3} />
            <Grid
              item
              xs={12}
              xl={6}
              md={6}
              sm={12}
              lg={6}
              border="1px solid #ccc"
              p={4}
              borderRadius="5px"
            >
              <FileBusta provvedimentiDaFirmare={provvedimentiDaFirmare} />
              <Box mt={2}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={checked}
                      onChange={handleCheckboxChange}
                    />
                  }
                  label={t('fascicolo.firmaDeposito.conformitaVerificata')}
                />
                <Box
                  mt={2}
                  display={'flex'}
                  width={'100%'}
                  justifyContent={'space-between'}
                >
                  <Box mt={1}>
                    <NsButton
                      sx={{ marginLeft: '10px' }}
                      variant="outlined"
                      color="primary"
                      onClick={() => router.back()}
                    >
                      {t('fascicolo.firmaDeposito.annulla')}
                    </NsButton>
                  </Box>
                  {/*                  {!isDebug ? (
                    <NsButton
                      sx={{ mt: 1 }}
                      variant="contained"
                      color="primary"
                      disabled={!checked}
                      onClick={() => firmaDepositaAzure()}
                    >
                      Firma e deposita
                    </NsButton>
                  ) : (
                    <NsButton
                      sx={{ mt: 1 }}
                      variant="contained"
                      color="primary"
                      disabled={!checked}
                      onClick={() => handleModal()}
                    >
                      Firma e deposita
                    </NsButton>
                  )}*/}
                  <Box mt={1}>
                    {statoProvvedimento !== 'IN_CODE_FIRMA_REL' && (
                      <NsButton
                        sx={{ marginRight: '10px' }}
                        variant="contained"
                        color="primary"
                        onClick={() => addCodaDiFirma()}
                      >
                        {t('editor.editorLibero.mettiInCodaDiFirma')}
                      </NsButton>
                    )}
                    <NsButton
                      sx={{ marginRight: '10px' }}
                      variant="contained"
                      color="primary"
                      disabled={!checked}
                      onClick={() => handleModal()}
                    >
                      {t('fascicolo.firmaDeposito.firmaEDeposita')}
                    </NsButton>
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>
      <MainModal {...modalProps} />
      {loading && <NsFullPageSpinner isOpen={isLoading} value={10} />}
    </>
  );
}
