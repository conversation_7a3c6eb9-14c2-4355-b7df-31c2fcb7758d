import { useEffect, useState } from 'react';

export default function PdfViewer({ blob }: any) {
  const [pdfString, setPdfString] = useState('');

  useEffect(() => {
    if (blob) {
      setPdfString(URL.createObjectURL(blob));
    }
  }, [blob]);

  return (
    <div style={{ width: '100%', height: '90vh' }}>
      {pdfString && (
        <a href={pdfString} target="_blank" rel="noopener noreferrer">
          Open PDF in new tab
        </a>
      )}
    </div>
  );
}