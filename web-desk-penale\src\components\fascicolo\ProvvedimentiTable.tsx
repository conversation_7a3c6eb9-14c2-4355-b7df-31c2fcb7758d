import InfoIcon from '@mui/icons-material/Info';
import { Box, Typography, useTheme } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Column } from 'src/interfaces';
import DetailNote from '../calendar/DetailNote';
import DetailStato from '../calendar/DetailStato';
import UploadProvvedimento from '../calendar/UploadProvvedimento';
import RelayTable from '../shared/RelayTable';
import {
  checkStatoSic,
  EstensoreActionName,
  formatDate,
  getActionEstensore,
  getStateNames,
  RuoloEnum,
  userRole,
} from '../shared/Utils';
import { StatoProvvedimentiEnum } from '../../types/types';
import { useGetRuolo } from '../shared/GetRuolo';
import { useConfig } from '../shared/configuration.context';
import { relayQueriesFascicoloDetails_FascicoloDetailsQuery$data } from '@/generated/relayQueriesFascicoloDetails_FascicoloDetailsQuery.graphql';
import TrackingStato from '../calendar/TrackingStato';
import { CacheConfig } from 'relay-runtime';
import { Options } from 'relay-hooks/lib/RelayHooksTypes';
import DialogModal from '../shared/DialogModal';

type ProvvedimentiTable = {
  data:
    | relayQueriesFascicoloDetails_FascicoloDetailsQuery$data
    | null
    | undefined;
  creaNuovo: (data: boolean) => void;
  refreshCoda: () => void;
  refreshPage: (_cacheConfigOverride?: CacheConfig, options?: Options) => void;
};

export default function ProvvedimentiTable({
  data,
  creaNuovo,
  refreshCoda,
  refreshPage,
}: ProvvedimentiTable) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const getRuolo = useGetRuolo();
  const [ruolo] = useState<any>(userRole() ?? getRuolo());
  const borderTable = { border: theme.custom.borders[0] };

  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const { params } = useRouter().query;

  const [loading, setLoading] = useState<boolean>(false);
  const [rows, setRows] = useState<any[]>([]);
  const [showInfo] = useState<boolean>(
    !!data?.udienzeWithProvvedimentoDet?.provvedimentiByNrg?.[0]?.tipo
  );

  const tipologia =
    data?.udienzeWithProvvedimentoDet?.provvedimentiByNrg?.[0]?.tipo;
  const checkSicBe = data?.udienzeWithProvvedimentoDet?.checkStatoOnSIC;

  const icons = getActionEstensore(t);

  const iconBoxStyles = {
    background: '#e0eeec',
    width: '30px',
    height: '30px',
    marginRight: '10px',
    cursor: 'pointer',
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  };

  const { notify } = useNotifier();

  const router = useRouter();

  const { servizi } = useConfig();

  const serviceUrl = `${servizi}`;

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <DetailNote id={''} role="RELATORE"/>,
  });

  useEffect(() => {
    creaNuovo!(rows.length === 0 && !checkStatoSic(checkSicBe));
  }, [rows]);

  const udienza = data?.udienzeWithProvvedimentoDet;

  const isRiunito = udienza?.checkStatoOnSIC?.statoProvvedimento === StatoProvvedimentiEnum.RIUNITO;

  const ricorsiUdienza = udienza?.ricorsiUdienza ?? [];
  const ricorso =
    ricorsiUdienza?.find((data: any) => data?.ricorso?.nrg == params)
      ?.ricorso ?? null;

  const idProvvedimento =
    data?.udienzeWithProvvedimentoDet?.ricorsiUdienza?.find(
      (data: any) => data?.ricorso?.nrg == params
    )?.ricorso?.provvedimento?.idProvvedimento;
  const duplicateProvvLocale = (
    idProvv: string,
    tipoProvvedimento: string,
    origine: string
  ) => {
    axios
      .get(serviceUrl + '/provvedimento/duplicate/' + idProvv)
      .then((response) => {
        setLoading(false);
        const idProvvDuplicato = response?.data;
        if (idProvvDuplicato && origine === 'LOCALE') {
          importaProvvedimento(idProvvDuplicato);
          localStorage.setItem('DUPLICATE_OPEN_MODAL', idProvvDuplicato);
          refreshPage();
        } else if (idProvvDuplicato && origine === 'SYSTEM') {
          pushRedazioneLibero(tipoProvvedimento, idProvvDuplicato);
        } else {
          notify({
            message:
              'Errore nella duplicazione del provvedimento, origine provvedimento non determinato.',
            type: 'error',
          });
        }
      })
      .catch((error) => {
        setLoading(false);
        notify({
          message: 'Errore nella duplicazione del provvedimento',
          type: 'error',
        });
      });
  };

  const goToFirmaDeposita = (idProvvedimento: string) => {
    router.push({
      pathname: '/firmadeposita',
      query: {
        idUdienza: data?.udienzeWithProvvedimentoDet.idUdien,
        params: router.query.params,
        idProvvedimento: idProvvedimento,
      },
    });
  };

  // service that allow you to add in Coda di firma
  const addCodaDiFirma = async (idProvv: string, source: string) => {
    let result = true;
    if (source === 'SYSTEM') {
      result = await generaPdfToDocx(idProvv, true);
    }

    if (result) {
      axios
        .get(serviceUrl + '/provvedimento/addCodaDeposito/' + idProvv)
        .then((res) => {
          notify({
            message: t(
              'fascicolo.provvedimentiTable.aggiuntoAllaCodaDiDeposito'
            ),
            type: 'success',
          });
          localStorage.setItem('SHOW_MESSAGE_CODA_FIRMA', 'true');
          window.location.reload();
        })
        .catch((error) => {
          notify({
            message: "Errore durante l'inserimento in coda di deposito",
            type: 'error',
          });
        })
        .finally(() => setLoading(false));
    }
  };

  const pushRedazioneLibero = (
    tipoProvvedimento: string | undefined,
    idProvv: string
  ) => {
    router
      .push({
        pathname: '/editor',
        query: {
          idUdienza: data?.udienzeWithProvvedimentoDet.idUdien,
          params: router.query.params,
          tipoProvvedimento: tipoProvvedimento,
          idProvvedimento: idProvv,
          edit: true,
        },
      })
      .then(() => {});
  };
  const duplicateProvvedimento = (
    idProvv: string,
    tipoProvvedimento: string
  ) => {
    axios
      .get(serviceUrl + '/provvedimento/duplicate/' + idProvv)
      .then((response) => {
        setLoading(false);
        pushRedazioneLibero(tipoProvvedimento, response.data);
      })
      .catch((error) => {
        setLoading(false);
        notify({
          message: 'Errore nella duplicazione del provvedimento',
          type: 'error',
        });
      });
  };

  const importaProvvedimento = (idProvv: string) => {
    setModalProps({
      ...modalProps,
      content: (
        <UploadProvvedimento
          data={{
            ricorso: ricorso,
            id: data?.udienzeWithProvvedimentoDet?.idUdien,
            nrg: params,
            duplicato: true,
            idProvvedimento: idProvv,
          }}
          closeModal={closeModal}
        />
      ),
      isOpen: true,
      title: t('fascicolo.provvedimentiTable.importaProvvedimento'),
    });
    setLoading(false);
    return true;
  };

  const deleteProvvedimento = (idProvv: string) => {
    axios
      .delete(serviceUrl + '/provvedimento/deleteProvvedimento/' + idProvv)
      .then((response: any) => {
        notify({
          message: t('fascicolo.provvedimentiTable.provvedimentoEliminato'),
          type: 'success',
        });
        setRows((rows) => rows.filter((row) => row.id !== idProvv));
        refreshCoda();
        refreshPage();
      })
      .finally(() => setLoading(false));
  };

  const downloadPdfOscuratoByIdProvv = (idProvv: string, file: any) => {
    axios
      .get(
        serviceUrl + '/provvedimento/downloadPdfOscuratoByIdProvv/' + idProvv,
        { responseType: 'blob' }
      )
      .then((response) => {
        const pdfBlob = new Blob([response.data], { type: 'application/pdf' });
        const downloadUrl = window.URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.href = downloadUrl;
        const fileName = `Provvedimento_${tipologia}_${file?.nomeFile.replace(
          'Provvedimento_',
          ''
        )}`;
        const filNameToString = fileName.toString();
        link.download = filNameToString;
        link.click();
      })
      .then(() => {
        setLoading(false);
        notify({
          message: t('fascicolo.provvedimentiTable.downloadOK'),
          type: 'success',
        });
      })
      .catch((error) => {
        setLoading(false);
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      });
  };

  const downloadPdfByIdProvv = (idProvv: string, file: any) => {
    axios
      .get(serviceUrl + '/provvedimento/downloadPdfByIdProvv/' + idProvv, {
        responseType: 'blob',
      })
      .then((response) => {
        const pdfBlob = new Blob([response.data], { type: 'application/pdf' });
        const downloadUrl = window.URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.href = downloadUrl;
        const fileName = `Provvedimento_${tipologia}_${file?.nomeFile.replace(
          'Provvedimento_',
          ''
        )}`;
        document.body.appendChild(link);
        link.download = fileName.toString();
        link.click();
      })
      .then(() => {
        setLoading(false);
        notify({
          message: t('fascicolo.provvedimentiTable.downloadOK'),
          type: 'success',
        });
      })
      .catch((error) => {
        setLoading(false);
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      });
  };

  const handleModal = (param: EstensoreActionName, id: string, row: any) => {
    const file = calculateThirdParam(param, row);
    const tipoProvvedimento =
      data?.udienzeWithProvvedimentoDet?.provvedimentiByNrg?.[0]?.tipo;
    if (!tipoProvvedimento) {
      notify({
        message: 'Errore durante il calcolo del tipo di provvedimento',
        type: 'error',
      });
      return;
    }
    switch (param) {
      case EstensoreActionName.STATO:
        if (id) {
          setModalProps({
            ...modalProps,
            content: <DetailStato id={id} roles="RELATORE" />,
            isOpen: true,
            title: t('fascicolo.provvedimentiTable.statoBusta'),
          });
        }
        break;
      case EstensoreActionName.NOTE:
        const title = t('fascicolo.provvedimentiTable.noteProvvedimento');
        setModalProps({
          ...modalProps,
          content: <DetailNote id={id} stato={row.stato} role="RELATORE"/>,
          isOpen: true,
          title: title,
        });
        break;
      case EstensoreActionName.FIRMA_DEPOSITA_ACTION:
        if (row.origine === 'LOCALE') {
          goToFirmaDeposita(id);
        } else {
          generaPdfToDocx(id).then(() =>
            console.log('finish operation generaPdf and redirect')
          );
        }
        break;
      case EstensoreActionName.DOWNLOAD_PDF:
        setLoading(true);
        downloadPdfByIdProvv(id, file);
        break;
      case EstensoreActionName.DOWNLOAD_PDF_OSCURATO:
        setLoading(true);
        downloadPdfOscuratoByIdProvv(id, file);
        break;
      case EstensoreActionName.DELETE:
        setLoading(true);
        deleteProvvedimento(id);
        break;
      case EstensoreActionName.REDAZIONE:
        pushRedazioneLibero(tipoProvvedimento, id);
        break;
      case EstensoreActionName.DUPLICA_PROVV_AND_GOTO_REDAZIONE:
        setLoading(true);
        duplicateProvvedimento(id, tipoProvvedimento);
        break;
      case EstensoreActionName.IMPORT_DOCUMENT:
        setLoading(true);
        importaProvvedimento(id);
        break;
      case EstensoreActionName.DUPLICATE_DOCUMENT_LOCAL:
        setLoading(true);
        duplicateProvvLocale(id, tipoProvvedimento, row.origine);
        break;
      case EstensoreActionName.TRACKING:
        if (idProvvedimento && idProvvedimento !== '') {
          const isEstensorePresidente = Boolean(
            data?.udienzeWithProvvedimentoDet?.ricorsiUdienza?.find(
              (ricUdi) => ricUdi?.ricorso?.nrg === params
            )?.isEstensore &&
              data?.udienzeWithProvvedimentoDet?.ricorsiUdienza.find(
                (ricUdi) => ricUdi?.ricorso?.nrg === params
              )?.isPresidente
          );
          setModalProps({
            ...modalProps,
            content: (
              <TrackingStato
                id={idProvvedimento}
                sicCheckStato={
                  data?.udienzeWithProvvedimentoDet?.checkStatoOnSIC
                }
                roles="RELATORE"
                isEstensorePresidente={isEstensorePresidente}
              />
            ),
            isOpen: true,
            title: 'Stato provvedimento fascicolo',
          });
        }
        break;
      case EstensoreActionName.PUSH_INTO_DEPOSIT_QUEUE:
        setLoading(true);
        addCodaDiFirma(id, row.origine).then(() =>
          console.log('finish operation generated')
        );
        break;
      default:
        alert('action not supporterd');
    }
  };

  const generaPdfToDocx = async (
    idProvvedimento: string,
    disabledRedirect?: boolean
  ) => {
    setLoading(true);
    return await axios
      .get(serviceUrl + '/provvedimento/generaPdfToDocx/' + idProvvedimento)
      .then(() => {
        if (!disabledRedirect) {
          goToFirmaDeposita(idProvvedimento);
        }
        return true;
      })
      .catch(() => {
        setLoading(false);
        return false;
      })
      .finally(() =>
        !disabledRedirect ? setLoading(false) : console.log('nothing')
      );
  };
  const renderStato = (cell: any, row: any) => {
    const handleClick = () => {
      handleModal(EstensoreActionName.STATO, row.id, null);
    };

    const clickableStyle = {
      cursor: 'pointer',
      textDecoration: 'underline',
    };

    const renderingStato = () => {
      if (row.stato === StatoProvvedimentiEnum.BOZZA && row.isRevisione) {
        return (
          <Box
            display="flex"
            alignItems="center"
            onClick={handleClick}
            sx={clickableStyle}
          >
            <Typography variant="body2" color="text.primary">
              {getStateNames('RELATORE', StatoProvvedimentiEnum.MINUTA_IN_REVISIONE)}
            </Typography>
          </Box>
        );
      }
      if (row.stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA) {
        return (
          <Box
            display="flex"
            alignItems="center"
            onClick={handleClick}
            sx={clickableStyle}
          >
            <Box
              component="img"
              src="/images/icon-scadenze.png"
              alt="scadenze"
              sx={{ marginRight: 1 }}
            />
            <Typography variant="body2" color="error">
              {getStateNames('RELATORE', row.stato)}
            </Typography>
          </Box>
        );
      } else if (row.stato === StatoProvvedimentiEnum.MINUTA_MODIFICATA_PRESIDENTE) {
        return (
          <Box
            display="flex"
            alignItems="center"
            onClick={handleClick}
            sx={clickableStyle}
          >
            <Typography variant="body2" color="text.primary">
              {getStateNames('RELATORE', row.stato)}
            </Typography>
          </Box>
        );
      } else if (row.stato === StatoProvvedimentiEnum.BOZZA_PRESIDENTE) {
        return (
          <Box
            display="flex"
            alignItems="center"
            onClick={handleClick}
            sx={clickableStyle}
          >
            <Typography variant="body2" color="text.primary">
              {getStateNames('RELATORE', row.stato)}
            </Typography>
          </Box>
        );
      } else if (row.stato === StatoProvvedimentiEnum.DA_REDIGERE) {
        return (
          <Box display="flex" alignItems="center" onClick={handleClick}>
            <Typography variant="body2" color="text.primary">
              {getStateNames('RELATORE', row.stato)}
            </Typography>
          </Box>
        );
      }
      return (
        <Box
          display="flex"
          alignItems="center"
          onClick={handleClick}
          sx={clickableStyle}
        >
          <Typography variant="body2" color="text.primary">
            {getStateNames('RELATORE', row.stato)}
          </Typography>
        </Box>
      );
    };

    if (
      row.stato.trim() === StatoProvvedimentiEnum.CODA_DI_FIRMA &&
      ruolo.includes(RuoloEnum.PRESIDENTE)
    ) {
      row.stato = StatoProvvedimentiEnum.CODA_DI_FIRMA;
    }
    if (
      row.stato.trim() === StatoProvvedimentiEnum.CODA_DI_FIRMA &&
      ruolo.includes(RuoloEnum.PRESIDENTE_RELATORE)
    ) {
      row.stato = 'MINUTA ACCETTATA E INVIATA AL PRESIDENTE';
    }

    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box display="flex">{renderingStato()}</Box>
      </TableCell>
    );
  };

  const renderTipologia = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        <Box display="flex">
          <Box ml={1} component="span">
            {' '}
            {row.tipo.replace('_', ' ')}
          </Box>
        </Box>
      </TableCell>
    );
  };

  const renderAzioni = (cell: any, row: any) => {
    const source = row.origine;
    const stato =  row.stato;
    const disabled = row.disabledModificaOrDuplica || checkStatoSic(checkSicBe);
    const disabledButton = row.disabledButton;
    const isListaFileEmpty = row.listaFile.length === 0;
    const isDuplicato = row?.isDuplicato;
    let allowedIcons: Array<EstensoreActionName> =
      new Array<EstensoreActionName>();

    if (isRiunito) {
      if (row.listaFile.length > 0) {
        allowedIcons.push(EstensoreActionName.DOWNLOAD_PDF);
        if (source === 'SYSTEM' && row.listaFile.length > 1) {
          allowedIcons.push(EstensoreActionName.DOWNLOAD_PDF_OSCURATO);
        }
      }
      if ((stato === StatoProvvedimentiEnum.BOZZA || stato === StatoProvvedimentiEnum.IN_CODE_FIRMA_REL) && source) {
        allowedIcons.push(EstensoreActionName.DELETE);
      }
    } else {
      if (row.listaFile.length > 0) {
        allowedIcons.push(EstensoreActionName.DOWNLOAD_PDF);
      }
      if (source) {
        if (!isListaFileEmpty && stato === StatoProvvedimentiEnum.BOZZA) {
          allowedIcons.push(EstensoreActionName.PUSH_INTO_DEPOSIT_QUEUE);
          allowedIcons.push(EstensoreActionName.FIRMA_DEPOSITA_ACTION);
        }
        if (stato === StatoProvvedimentiEnum.BOZZA || stato === StatoProvvedimentiEnum.IN_CODE_FIRMA_REL) {
          allowedIcons.push(EstensoreActionName.DELETE);
        }
      }
      if (source === 'LOCALE') {
        if (stato === StatoProvvedimentiEnum.BOZZA) {
          allowedIcons.push(EstensoreActionName.IMPORT_DOCUMENT);
        }
        if (
          (stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA || 
           stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE || 
           stato === StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE) &&
          !disabled
        ) {
          allowedIcons.push(EstensoreActionName.DUPLICATE_DOCUMENT_LOCAL);
        }
      } else if (source === 'SYSTEM') {
        if (row.listaFile.length > 1) {
          allowedIcons.push(EstensoreActionName.DOWNLOAD_PDF_OSCURATO);
        }
        if (!disabled && stato === StatoProvvedimentiEnum.BOZZA) {
          allowedIcons.push(EstensoreActionName.REDAZIONE);
        }
        if (
          (stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA ||
            stato === StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE ||
            stato === StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE ||
            stato === StatoProvvedimentiEnum.MINUTA_MODIFICATA_PRESIDENTE) &&
          !disabled &&
          !disabledButton
        ) {
          allowedIcons.push(EstensoreActionName.DUPLICA_PROVV_AND_GOTO_REDAZIONE);
        }
      }
    }

    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        <Box display="flex" flexDirection="row">
          {icons
            .filter((icon) => allowedIcons.includes(icon.actionName))
            .map((icon) => (
              <Box
                key={icon.id}
                sx={{
                  ...iconBoxStyles,
                  backgroundColor: icon.backgroundColor
                    ? icon.backgroundColor
                    : 'bold',
                }}
                onClick={() => {
                  handleModal(icon.actionName, row.id, row);
                }}
              >
                {icon.icon}
              </Box>
            ))}
        </Box>
      </TableCell>
    );
  };

  const calculateThirdParam = (
    iconActionName: EstensoreActionName,
    row: any
  ) => {
    if (iconActionName && row) {
      switch (iconActionName) {
        case EstensoreActionName.DOWNLOAD_PDF:
          return row.listaFile.find((file: any) => file.oscurato === false);
        case EstensoreActionName.DOWNLOAD_PDF_OSCURATO:
          return row.listaFile.find((file: any) => file.oscurato === true);
        default:
          return null;
      }
    }
    return null;
  };
  const renderNote = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        {(row.stato.trim() === StatoProvvedimentiEnum.BUSTA_RIFIUTATA ||
          row.stato.trim() === StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE ||
          row.stato.trim() === StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE ||
          row.stato.trim() ===
            StatoProvvedimentiEnum.MINUTA_MODIFICATA_PRESIDENTE) && (
          <NsButton
            sx={theme.custom.secondaryButton}
            onClick={() => handleModal(EstensoreActionName.NOTE, row.id, row)}
          >
            {t('common.vedi')}
          </NsButton>
        )}
      </TableCell>
    );
  };

  const renderModifica = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        {formatDate(row.dataUltimaModifica, 'DD/MM/YYYY HH:mm')}
      </TableCell>
    );
  };

  const renderAutore = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        {`${row?.autore?.nome}  ${row?.autore?.cognome}`}
      </TableCell>
    );
  };

  const columns: Column[] = [
    {
      id: 'tipologia',
      minWidth: 170,
      label: t('common.tipologia') as string,
      render: renderTipologia,
    },
    {
      id: 'autore',
      align: 'left',
      label: t('common.estensore') as string,
      minWidth: 170,
      render: renderAutore,
    },
    {
      id: 'stato',
      minWidth: 170,
      label: t('common.statoBusta') as string,
      align: 'left',
      render: renderStato,
      renderHeadCell: (cell: any, rows: any) => {
        return (
          <Box display="flex" alignItems="center">
            <div>{t('common.statoBusta')}</div>
            <InfoIcon
              sx={{
                color: '#308A7D',
                marginLeft: '10px',
                display: showInfo ? 'block' : 'none',
              }}
              onClick={() =>
                handleModal(EstensoreActionName.TRACKING, '', null)
              }
            />
          </Box>
        );
      },
    },
    {
      id: 'ultima',
      minWidth: 170,
      label: t('common.ultimaModifica') as string,
      align: 'left',
      render: renderModifica,
    },
    {
      id: 'note',
      minWidth: 170,
      label: t('common.note') as string,
      align: 'left',
      render: renderNote,
    },
    {
      id: 'azioni',
      minWidth: 170,
      label: t('common.azioni') as string,
      align: 'left',
      render: renderAzioni,
    },
  ];

  useEffect(() => {
    if (data?.udienzeWithProvvedimentoDet?.provvedimentiByNrg) {
      const rows = data.udienzeWithProvvedimentoDet.provvedimentiByNrg.map(
        (provvedimento: any) => {
          return {
            id: provvedimento.idProvvedimento,
            disabledModificaOrDuplica: provvedimento.disabledModificaOrDuplica,
            disabledButton: provvedimento.disabledButton,
            tipo: provvedimento.tipo,
            stato: provvedimento.stato,
            dataUltimaModifica: provvedimento.dataUltimaModifica,
            autore: provvedimento.autore,
            note: provvedimento.note,
            origine: provvedimento.origine,
            listaFile: provvedimento.listaFile ? provvedimento.listaFile : [],
            isDuplicato: provvedimento?.isDuplicato,
            hasDuplicato: provvedimento?.hasDuplicato,
            isRevisione: provvedimento?.isRevisione,
          };
        }
      );
      setRows(rows);
    }
    const duplicateOpenModal = localStorage.getItem('DUPLICATE_OPEN_MODAL');
    if (duplicateOpenModal) {
      importaProvvedimento(duplicateOpenModal);
      localStorage.removeItem('DUPLICATE_OPEN_MODAL');
    }

    const showMessage = localStorage.getItem('SHOW_MESSAGE_CODA_FIRMA');
    if (showMessage === 'true') {
      notify({
        message: t('fascicolo.provvedimentiTable.aggiuntoAllaCodaDiDeposito'),
        type: 'success',
      });
      localStorage.removeItem('SHOW_MESSAGE_CODA_FIRMA');
    }
  }, [data]);

  useEffect(() => {
    if (!rows || rows.length < 1) return;
    setRows(
      rows.sort(
        (p1, p2) =>
          new Date(p2.dataUltimaModifica).getTime() -
          new Date(p1.dataUltimaModifica).getTime()
      )
    );
  }, [rows]);

  return (
    <>
      {loading && <NsFullPageSpinner isOpen={true} value={1} />}
      {isRiunito ? (
        <Box>
          <Typography variant="body1">
            {t('fascicolo.detailFascicolo.provvedimentoSubordinato')}
          </Typography>
        </Box>
      ) : (
        <RelayTable rows={rows} columns={columns} />
      )}
      <DialogModal {...modalProps} />
    </>
  );
}
