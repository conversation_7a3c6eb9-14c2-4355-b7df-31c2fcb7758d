import ArticleIcon from '@mui/icons-material/Article';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EditIcon from '@mui/icons-material/Edit';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import FileDownloadOffIcon from '@mui/icons-material/FileDownloadOff';
import InfoIcon from '@mui/icons-material/Info';
import RateReviewIcon from '@mui/icons-material/RateReview';
import UploadIcon from '@mui/icons-material/Upload';
import { Box, Typography, useTheme } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios, { all } from 'axios';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Column } from 'src/interfaces';
import DetailNote from '../calendar/DetailNote';
import DetailStato from '../calendar/DetailStato';
import TrackingStato from '../calendar/TrackingStato';
import PdfPreview from '../scrivania/PdfPreview';
import RelayTable from '../shared/RelayTable';
import {
  formatDate,
  getStateNames,
  convertTipoProvvedimentoForRedazioneOnlinePresidente,
} from '../shared/Utils';
import { StatoProvvedimentiEnum } from '../../types/types';
import { useConfig } from '../shared/configuration.context';
import { relayQueriesFascicoloPresidenteDetails_FascicoloPresidenteDetailsQuery$data } from '@/generated/relayQueriesFascicoloPresidenteDetails_FascicoloPresidenteDetailsQuery.graphql';
import DialogModal from '../shared/DialogModal';
import { maxWidth } from '@mui/system';
type ProvvedimentiTablePresidenteType = {
  data:
    | relayQueriesFascicoloPresidenteDetails_FascicoloPresidenteDetailsQuery$data
    | null
    | undefined;
  refreshPage: () => void;
};
export default function ProvvedimentiTablePresidente({
  data,
  refreshPage,
}: Readonly<ProvvedimentiTablePresidenteType>) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const borderTable = { border: theme.custom.borders[0] };

  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
    setPdfPreviewModalProps({ ...pdfPreviewmodalProps, isOpen: false });
  };

  const { params } = useRouter().query;

  const isRiunito = data?.udienzeWithProvvedimentoDet?.checkStatoOnSIC?.statoProvvedimento === StatoProvvedimentiEnum.RIUNITO;

  const icons = [
    {
      id: 1,
      name: 'EditIcon',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.modificaProvvedimento')}
          icon={<EditIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
          placement='top'
        />
      ),
    },
    {
      id: 2,
      name: 'RateReviewIcon',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.depositaMinuta')}
          icon={<RateReviewIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
          placement='top'
        />
      ),
    },
    {
      id: 3,
      name: 'FileDownloadIcon',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.scarica')}
          icon={<FileDownloadIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
          placement='top'
        />
      ),
    },
    {
      id: 4,
      name: 'FileDownloadIconOscurato',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.scaricaProvvedimentoOscurato')}
          icon={<FileDownloadOffIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
          placement='top'
        />
      ),
    },
    {
      id: 5,
      name: 'DeleteOutlineIcon',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.eliminaProvvedimento')}
          icon={<DeleteOutlineIcon fontSize="small" sx={{ color: '#ff0000' }} />}
          placement='top'
        />
      ),
    },
    {
      id: 6,
      name: 'UploadIcon',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.caricaProvvedimento')}
          icon={<UploadIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
          placement='top'
        />
      ),
    },
    {
      id: 7,
      name: 'EditIcon2',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.modificaProvvedimento')}
          icon={<EditIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
          placement='top'
        />
      ),
    },
    {
      id: 8,
      name: 'ArticleIcon',
      icon: (
        <NsTooltip
          title={t('fascicolo.provvedimentiTablePresidente.visualizza')}
          icon={<ArticleIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
          placement='top'
        />
      ),
    },
  ];


  const iconBoxStyles = {
    background: '#e0eeec',
    width: '30px',
    height: '30px',
    marginRight: '10px',
    cursor: 'pointer',
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  };

  const { notify } = useNotifier();

  const router = useRouter();

  const { servizi } = useConfig();

  const serviceUrl = `${servizi}`;

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <DetailNote id={''} role="PRESIDENTE"/>,
    openFromParent: true,
    fullScreen: false
  });

  const [pdfPreviewmodalProps, setPdfPreviewModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: t(
      'fascicolo.provvedimentiTablePresidente.visualizzaProvvedimento'
    ),
    content: <></>,
    openFromParent: true,
    fullScreen: true,
    maxWidth: 'xl',
  });

  const udienza = data?.udienzeWithProvvedimentoDet;

  const ricorsiUdienza = udienza?.ricorsiUdienza ?? [];

  const ricorso =
    ricorsiUdienza?.find((data: any) => data?.ricorso?.nrg == params)
      ?.ricorso ?? null;

  const idProvvedimento =
    data?.udienzeWithProvvedimentoDet?.ricorsiUdienza?.find(
      (data: any) => data?.ricorso?.nrg == params
    )?.ricorso?.provvedimento?.idProvvedimento;

  const duplicateProvvedimento = (idProvv: any, tipoProvvedimento: any) => {
    axios
      .get(serviceUrl + '/provvedimento/duplicate/' + idProvv)
      .then((response) => {
        const tipoProvvedimentoCl =
          convertTipoProvvedimentoForRedazioneOnlinePresidente(
            tipoProvvedimento
          );
        router.push({
          pathname: '/editor',
          query: {
            idUdienza: data?.udienzeWithProvvedimentoDet.idUdien,
            params: router.query.params,
            tipoProvvedimento: tipoProvvedimentoCl,
            idProvvedimento: response.data,
            edit: true,
            IRP: true,
          },
        });
      })
      .catch((error) => {
        notify({
          message: 'Errore nella duplicazione del provvedimento',
          type: 'error',
        });
      });
  };

  const deleteProvvedimento = (idProvv: any) => {
    axios
      .delete(serviceUrl + '/provvedimento/deleteProvvedimento/' + idProvv)
      .then((response) => {
        notify({
          message: t(
            'fascicolo.provvedimentiTablePresidente.provvedimentoEliminato'
          ),
          type: 'success',
        });
        setRows((rows) => rows.filter((row) => row.id !== idProvv));
        refreshPage();
        window.location.reload();
      })
      .catch((error) => {
        notify({
          message: 'Errore nella cancellazione del provvedimento',
          type: 'error',
        });
      });
  };

  const downloadPdfOscuratoByIdProvv = (idProvv: any, file: any) => {
    axios
      .get(
        serviceUrl + '/provvedimento/downloadPdfOscuratoByIdProvv/' + idProvv,
        { responseType: 'blob' }
      )
      .then((response) => {
        const pdfBlob = new Blob([response.data], { type: 'application/pdf' });
        const downloadUrl = window.URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.href = downloadUrl;
        const fileName = file?.nomeFile ? file.nomeFile : 'provvedimento.pdf';
        const filNameToString = fileName.toString();
        link.download = filNameToString;
        link.click();
      })
      .then(() => {
        notify({
          message: t('fascicolo.provvedimentiTablePresidente.downloadOK'),
          type: 'success',
        });
      })
      .catch((error) => {
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      });
  };

  const generaPdfToDocx = (idProvvedimento: string) => {
    setLoading(true);
    axios
      .get(serviceUrl + '/provvedimento/generaPdfToDocx/' + idProvvedimento)
      .then(() => {
        router.push({
          pathname: '/firmadeposita',
          query: {
            idUdienza: data?.udienzeWithProvvedimentoDet.idUdien,
            params: router.query.params,
            idProvvedimento: idProvvedimento,
          },
        });
      })
      .finally(() => setLoading(false));
  };

  const downloadPdfByIdProvv = (idProvv: any, file: any) => {
    axios
      .get(serviceUrl + '/provvedimento/downloadPdfByIdProvv/' + idProvv, {
        responseType: 'blob',
      })
      .then((response) => {
        const pdfBlob = new Blob([response.data], { type: 'application/pdf' });
        const downloadUrl = window.URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.href = downloadUrl;
        const fileName = file?.nomeFile ? file.nomeFile : 'provvedimento.pdf';
        const filNameToString = fileName.toString();
        link.download = filNameToString;
        link.click();
      })
      .then(() => {
        notify({
          message: t('fascicolo.provvedimentiTablePresidente.downloadOK'),
          type: 'success',
        });
      })
      .catch((error) => {
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      });
  };

  const handleModal = (param: string, id: string, file: any) => {
    const tipoProvvedimento =
      data?.udienzeWithProvvedimentoDet?.provvedimentoByNrgPerPresidente?.[0]
        ?.tipo || '';

    if (param == 'stato') {
      if (id !== undefined && id !== null) {
        setModalProps({
          ...modalProps,
          content: <DetailStato id={id} roles="PRESIDENTE" />,
          isOpen: true,
          title: t(
            'fascicolo.provvedimentiTablePresidente.statoProvvedimentoFascicolo'
          ),
        });
      }
    } else if (param === 'note') {
      const title = t(
        'fascicolo.provvedimentiTablePresidente.noteProvvedimento'
      );
      setModalProps({
        ...modalProps,
        content: <DetailNote id={id} stato={file} role="PRESIDENTE" />,
        isOpen: true,
        title: title,
      });
    } else if (param === 'rateReview') {
      generaPdfToDocx(id);
    } else if (param === 'downloadPdf') {
      downloadPdfByIdProvv(id, file);
    } else if (param === 'downloadPdfOscurato') {
      downloadPdfOscuratoByIdProvv(id, file);
    } else if (param === 'delete') {
      deleteProvvedimento(id);
    } else if (param === 'redazione') {
      const titologia =
        convertTipoProvvedimentoForRedazioneOnlinePresidente(tipoProvvedimento);
      router.push({
        pathname: '/editor',
        query: {
          idUdienza: data?.udienzeWithProvvedimentoDet.idUdien,
          params: router.query.params,
          tipoProvvedimento: titologia,
          idProvvedimento: id,
          edit: true,
          IRP: true,
        },
      });
    }
    // } else if (param === 'importaDocumento') {
    //     setModalProps({ ...modalProps, content: <UploadProvvedimento data={{ id:data, idUdienza: router.query.idUdienza, numOrdine: router.query.ordine }} closeModal={closeModal} />, modal: true, title: "Importa documento", style: { ...style, width: 600 } });
    //
    else if (param === 'view') {
      const provvedimento =
        data?.udienzeWithProvvedimentoDet.provvedimentoByNrgPerPresidente?.find(
          (provvedimento) => provvedimento.idProvvedimento === id
        );
      setPdfPreviewModalProps({
        ...pdfPreviewmodalProps,
        content: (
          <Box
            sx={{
              width: '100%',
              height: '100%', // Take full height of the modal
              overflow: 'hidden', // Prevent double scrollbars
            }}
          >
            <PdfPreview
              idUdienza={data?.udienzeWithProvvedimentoDet?.idUdien}
              provv={id}
              refreshPage={handleRefresh}

              fascicolo={provvedimento}
            />
          </Box>
        ),
        isOpen: true,
      });
    } else if (param === 'redazione2') {
      duplicateProvvedimento(id, tipoProvvedimento);
    } else if (param === 'tracking') {
      if (
        idProvvedimento !== undefined &&
        idProvvedimento !== null &&
        idProvvedimento !== ''
      ) {
        const isEstensorePresidente = Boolean(
          data?.udienzeWithProvvedimentoDet?.ricorsiUdienza?.find(
            (ricUdi) => ricUdi?.ricorso?.nrg === params
          )?.isEstensore &&
            data?.udienzeWithProvvedimentoDet?.ricorsiUdienza.find(
              (ricUdi) => ricUdi?.ricorso?.nrg === params
            )?.isPresidente
        );
        setModalProps({
          ...modalProps,
          content: (
            <TrackingStato
              id={idProvvedimento}
              sicCheckStato={null}
              roles="PRESIDENTE"
              isEstensorePresidente={isEstensorePresidente}
            />
          ),
          isOpen: true,
          title: t(
            'fascicolo.provvedimentiTablePresidente.statoProvvedimentoFascicolo'
          ),
        });
      }
    }
  };

  const renderStato = (cell: any, row: any) => {
    const handleClick = () => {
      handleModal('stato', row.id, null);
    };

    const clickableStyle = {
      cursor: 'pointer',
      textDecoration: 'underline',
    };

    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box display="flex">
          {row.stato === 'BUSTA RIFIUTATA' ? (
            <Box
              display="flex"
              alignItems="center"
              onClick={handleClick}
              sx={clickableStyle}
            >
              <Box
                component="img"
                src="/images/icon-scadenze.png"
                alt="scadenze"
                sx={{ marginRight: 1 }}
              />
              <Typography variant="body2" color="error">
                {getStateNames('PRESIDENTE', row.stato)}
              </Typography>
            </Box>
          ) : row.stato === 'REDAZIONE_ESTENSORE' ||
            row.stato === 'DA_REDIGERE' ? (
            <Box display="flex" alignItems="center">
              <Typography variant="body2" color="text.primary">
                {getStateNames('PRESIDENTE', row.stato)}
              </Typography>
            </Box>
          ) : (
            <Box
              display="flex"
              alignItems="center"
              onClick={handleClick}
              sx={clickableStyle}
            >
              <Typography variant="body2" color="text.primary">
                {getStateNames('PRESIDENTE', row.stato)}
              </Typography>
            </Box>
          )}
        </Box>
      </TableCell>
    );
  };

  const handleRefresh = () => {
    closeModal();
    refreshPage();
  };

  const renderTipologia = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        <Box display="flex">
          <Box ml={1} component="span">
            {' '}
            {row.tipo.replace('_', ' ')}
          </Box>
        </Box>
      </TableCell>
    );
  };

  const renderAzioni = (cell: any, row: any) => {
    const source = row.origine;
    const stato = row.stato;
console.log('row', row);    const enabledRichiestaDiModificaEVerificato = row.enabledRichiestaDiModificaEVerificato;
    let allowedIcons: string[] = [];

    if (row.listaFile.find((file: any) => file.oscurato === false)) {
      allowedIcons.push('FileDownloadIcon');
    }
    if (row.listaFile.find((file: any) => file.oscurato === true)) {
      allowedIcons.push('FileDownloadIconOscurato');
    }
    allowedIcons.push('ArticleIcon');

    if (stato === 'BOZZA_PRESIDENTE' && !enabledRichiestaDiModificaEVerificato) {
      if (!isRiunito) {
        allowedIcons.push('EditIcon');
        allowedIcons.push('DeleteOutlineIcon');
      } else {
        allowedIcons.push('DeleteOutlineIcon');
      }
    }

    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        <Box display="flex" flexDirection="row">
          {icons
            .filter((icon) => allowedIcons.includes(icon.name))
            .map((icon) => (
              <Box
                key={icon.id}
                sx={{
                  ...iconBoxStyles,
                  backgroundColor: icon.name === 'DeleteOutlineIcon' ? '#f9e1dd' : 'bold',
                }}
                onClick={() => {
                  if (icon.name === 'FileDownloadIcon') {
                    handleModal(
                      'downloadPdf',
                      row.id,
                      row.listaFile.find((file: any) => file.oscurato === false)
                    );
                  }
                  if (icon.name === 'FileDownloadIconOscurato') {
                    handleModal(
                      'downloadPdfOscurato',
                      row.id,
                      row.listaFile.find((file: any) => file.oscurato === true)
                    );
                  }
                  if (icon.name === 'DeleteOutlineIcon') {
                    handleModal('delete', row.id, null);
                  }
                  if (icon.name === 'EditIcon') {
                    handleModal('redazione', row.id, null);
                  }
                  if (icon.name === 'ArticleIcon') {
                    handleModal(
                      'view',
                      row.id,
                      enabledRichiestaDiModificaEVerificato
                    );
                  }
                }}
              >
                {icon.icon}
              </Box>
            ))}
        </Box>
      </TableCell>
    );
  };

  const renderNote = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align}>
        {(row.stato.trim() === StatoProvvedimentiEnum.BUSTA_RIFIUTATA ||
          row.stato.trim() === StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE ||
          row.stato.trim() === StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE || 
          row.hasNote && row.stato.trim() === StatoProvvedimentiEnum.MINUTA_ACCETTATA) && (
          <NsButton
            sx={theme.custom.secondaryButton}
            onClick={() => handleModal('note', row.id, row.stato)}
          >
            {t('common.vedi')}
          </NsButton>
        )}
      </TableCell>
    );
  };

  const renderModifica = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        {formatDate(row.dataUltimaModifica, 'DD/MM/YYYY HH:mm')}
      </TableCell>
    );
  };

  const renderAutore = (cell: any, row: any) => {
    return (
      <TableCell key={cell.id} align={cell.align} sx={borderTable}>
        {`${row?.autore?.nome}  ${row?.autore?.cognome}`}
      </TableCell>
    );
  };

  const columns: Column[] = [
    {
      id: 'tipologia',
      minWidth: 170,
      label: t('common.tipologia') as string,
      render: renderTipologia,
    },
    {
      id: 'autore',
      align: 'left',
      label: t('common.autore') as string,
      minWidth: 170,
      render: renderAutore,
    },
    {
      id: 'stato',
      minWidth: 170,
      label: t('common.statoProvvedimento') as string,
      align: 'left',
      render: renderStato,
      renderHeadCell: (cell: any, rows: any) => {
        return (
          <Box display="flex" alignItems="center">
            <Box>{t('common.statoBusta')}</Box>
            <InfoIcon
              sx={{ color: '#308A7D', marginLeft: '10px' }}
              onClick={() => handleModal('tracking', '', null)}
            />
          </Box>
        );
      },
    },
    {
      id: 'ultima',
      minWidth: 170,
      label: t('common.ultimaModifica') as string,
      align: 'left',
      render: renderModifica,
    },
    {
      id: 'note',
      minWidth: 170,
      label: t('common.note') as string,
      align: 'left',
      render: renderNote,
    },
    {
      id: 'azioni',
      minWidth: 170,
      label: t('common.azioni') as string,
      align: 'left',
      render: renderAzioni,
    },
  ];

  const [rows, setRows] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  //sort rows by data
  const sortedRows = rows.sort((a, b) => {
    return (
      new Date(b.dataUltimaModifica).getTime() -
      new Date(a.dataUltimaModifica).getTime()
    );
  });

  useEffect(() => {
    if (data?.udienzeWithProvvedimentoDet?.provvedimentoByNrgPerPresidente) {
      const rows =
        data?.udienzeWithProvvedimentoDet?.provvedimentoByNrgPerPresidente.map(
          (provvedimento: any) => {
            return {
              id: provvedimento.idProvvedimento,
              tipo: provvedimento.tipo,
              stato: provvedimento.stato,
              dataUltimaModifica: provvedimento.dataUltimaModifica,
              autore: provvedimento.autore,
              enabledRichiestaDiModificaEVerificato:
                provvedimento.enabledRichiestaDiModificaEVerificato,
              hasNote: provvedimento.hasNote,
              origine: provvedimento.origine,
              listaFile: provvedimento.listaFile ? provvedimento.listaFile : [],
            };
          }
        );
      setRows(rows);
    }
  }, [data]);

  return (
    <>
      {loading && <NsFullPageSpinner isOpen={true} value={1} />}
      {isRiunito ? (
        <Box >
          <Typography variant="body1" >
            {t('fascicolo.detailFascicolo.provvedimentoSubordinato')}
          </Typography>
        </Box>
      ) : (
        <RelayTable rows={rows} columns={columns} />
      )}
      <DialogModal {...modalProps} />
      <DialogModal {...pdfPreviewmodalProps} />
    </>
  );
};
