
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { Grid, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { useTranslation } from 'next-i18next';
import {MinimalRicorsoDetailSchema} from "../relay/relayQueriesRicorsoDetails";
import {
  relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery
} from "@/generated/relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery.graphql";

export default function ProvvedimentoDepositato() {
  const { t } = useTranslation();
  const ROOT_QUERY = MinimalRicorsoDetailSchema;

  const router = useRouter();

  const { idUdienza, params, tipologiaProvvedimento } = router.query;

  const { data, isLoading } = useQuery<relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery>(
    ROOT_QUERY,
    {
      idUdien: Number(idUdienza),
      nrg: Number(params),
    },
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  const idUdien = Number(idUdienza);

  const number =
    data?.ricorsoByIdUdienAndNrg?.numero;
  const year =
    data?.ricorsoByIdUdienAndNrg?.anno;
  const nrg = `${number}/${year}`;

  const [tipologiaProvvedimento2, setTipologiaProvvedimento2] =
    useState<string>('');

  const fixTipologiaProvvedimento = (tipologiaProvvedimento: string) => {
    if (tipologiaProvvedimento == 'ORDINANZA') {
      setTipologiaProvvedimento2('MINUTA ORDINANZA');
    } else if (tipologiaProvvedimento == 'SENTENZA') {
      setTipologiaProvvedimento2('MINUTA SENTENZA');
    } else setTipologiaProvvedimento2(tipologiaProvvedimento);
  };

  useEffect(() => {
    fixTipologiaProvvedimento(tipologiaProvvedimento as string);
  }, []);

  const text =
    'La ' +
    tipologiaProvvedimento2.replace('_', ' ') +
    ' del fascicolo ' +
    nrg +
    ' è stata depositata con successo';

  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <CheckCircleIcon sx={{ fontSize: 60, color: 'green', margin: 2 }} />
        <Typography variant="h1" sx={{ margin: 2 }}>
          {t('fascicolo.provvedimentoDepositato.provvedimentoDepositato')}
        </Typography>
        <Typography variant="h6" sx={{ margin: 2 }}>
          {text}
        </Typography>
      </Grid>
      <Grid item xs={12} sx={{ marginBottom: 4, margin: 2 }}>
        <NsButton
          variant="outlined"
          color="primary"
          type="button"
          onClick={() => router.push('/calendario')}
        >
          {t('fascicolo.provvedimentoDepositato.tornaAlCalendarioUdienze')}
        </NsButton>
        <NsButton
          sx={{ marginLeft: '10px' }}
          variant="contained"
          color="primary"
          type="button"
          onClick={() =>
            router.push('/fascicolo/' + idUdien + '?params=' + params)
          }
        >
          {t('fascicolo.provvedimentoDepositato.visualizzaDettaglioFascicolo')}
        </NsButton>
      </Grid>
    </Grid>
  );
}
