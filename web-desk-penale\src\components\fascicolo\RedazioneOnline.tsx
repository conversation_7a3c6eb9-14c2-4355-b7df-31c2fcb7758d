import { Box, Grid, MenuItem, Typography } from '@mui/material';
import { NsButton, NsTextInput } from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RedazioneOnlineProps } from 'src/interfaces';
import { useConfig } from '../shared/configuration.context';
import { tipoProvvedimentoToString } from '../shared/Utils';
import { ProvvedimentiTipoEnum } from '../../types/types';

export default function RedazioneOnline({
  data,
  closeModal,
}: RedazioneOnlineProps) {
  const router = useRouter();
  const { t } = useTranslation();
  const [tipoProvvedimento, setTipoProvvedimento] =
    useState<ProvvedimentiTipoEnum | null>(null);
  const [isDataReady, setIsDataReady] = useState<boolean>(false);
  const [semplificata, setSemplificata] = useState<boolean>(false);

  const handleConfirm = () => {
    if (tipoProvvedimento !== null) {
      router.push({
        pathname: '/editor',
        query: {
          idUdienza: data.idUdienza,
          params: data.nrg,
          tipoProvvedimento,
        },
      });
    }
  };

  const { servizi } = useConfig();

  const serviceUrl = `${servizi}/provvedimento/getTipoProvvedimentoAndSemplificata/`;

  const getTestiIniziali = async () => {
    try {
      const response = await axios.get(
        serviceUrl + data.idUdienza + '/' + data.nrg
      );

      if (response.data) {
        setTipoProvvedimento(response.data?.tipoProvvedimento);
        setIsDataReady(true);
        setSemplificata(response.data.semplificata);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    getTestiIniziali();
  }, []);

  return (
    <>
      <Typography variant="h5">
        {t('fascicolo.redazioneOnline.tipologiaProvvedimento')}:{' '}
      </Typography>
      <Grid item xs={12}>
        <NsTextInput
          size="small"
          disabled
          defaultValue={
            tipoProvvedimento
              ? tipoProvvedimentoToString(tipoProvvedimento)
              : ''
          }
        >
          {tipoProvvedimento && (
            <MenuItem value={2}>
              {tipoProvvedimentoToString(tipoProvvedimento)}
            </MenuItem>
          )}
        </NsTextInput>
        {semplificata && (
          <Typography mt={2} variant="h3">
            {t('fascicolo.redazioneOnline.motivazioneSemplificata')}
          </Typography>
        )}
        <Box mt={2} display={'flex'} justifyContent={'space-between'}>
          <NsButton variant="outlined" color="primary" onClick={closeModal}>
            {t('fascicolo.redazioneOnline.chiudi')}
          </NsButton>
          <NsButton
            variant="contained"
            color="primary"
            onClick={handleConfirm}
            disabled={!isDataReady}
          >
            {t('fascicolo.redazioneOnline.conferma')}
          </NsButton>
        </Box>
      </Grid>
    </>
  );
}
