import { Box, Grid, Tab, Tabs, Typography } from '@mui/material';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import {
  relayQueriesRiunitDetails_GetRiunitiDetailsQuery,
  relayQueriesRiunitDetails_GetRiunitiDetailsQuery$data,
} from '@/generated/relayQueriesRiunitDetails_GetRiunitiDetailsQuery.graphql';
import {
  GetRiunitiDetailsSchema,
  GetRiunitiRicorsoUdienzaSchema,
} from '../relay/relayQueriesRiunitDetails';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import { relayQueriesRiunitDetails_GetRiunitiRicorsoUdienzaQuery } from '@/generated/relayQueriesRiunitDetails_GetRiunitiRicorsoUdienzaQuery.graphql';
import {
  DatiFascicoloDetailsProps,
  TabPanelRiunitiProps,
} from '../../model/fascicolo-models.model';
import DatiFascicoloDetails from './DatiFascicoloDetails';
import { display } from '@mui/system';

function GetSinglePanelRiuniti({
  idsRicudienRiunito,
  riuniti,
  value,
}: TabPanelRiunitiProps) {
  const ROOT_QUERY2 = GetRiunitiRicorsoUdienzaSchema;
  console.log('idsRicudienRiunito:', idsRicudienRiunito);
  const [ricorsoUdienzaRiunito, setRicorsoUdienzaRiunito] = useState<any[]>([]);
  const { data: ricorsoUdienzaRiunitiQuery, isLoading } =
    useQuery<relayQueriesRiunitDetails_GetRiunitiRicorsoUdienzaQuery>(
      ROOT_QUERY2,
      {
        idRicUdienList: idsRicudienRiunito,
      },
      {
        fetchPolicy: STORE_OR_NETWORK,
        skip: !idsRicudienRiunito || idsRicudienRiunito.length == 0,
      }
    );
  useEffect(() => {
    console.log(
      'GetRiunitiRicorsoUdienzaSchema:',
      value,
      ricorsoUdienzaRiunitiQuery
    );
    setRicorsoUdienzaRiunito(
      (ricorsoUdienzaRiunitiQuery?.ricorsoUdienzaInRicUdien as any[]) ?? []
    );
  }, [ricorsoUdienzaRiunitiQuery]);
  const drawTabRiun = (i: number, riun: any) => {
    console.log('drawTabRiunStart:', i, riun);
    console.log(
      'boolean:',
      riuniti?.getRiuntiByIdRicUdien,
      riuniti?.getRiuntiByIdRicUdien?.some(
        (ricUdien) => Number(ricUdien.idRicorsoUdienza) === riun.idRicudien
      )
    );
    if (
      value === i &&
      ricorsoUdienzaRiunito?.length > 0 &&
      riun &&
      riuniti?.getRiuntiByIdRicUdien?.some(
        (ricUdien) =>
          Number(ricUdien.idRicorsoUdienza) === Number(riun.idRicudien)
      )
    ) {
      console.log('drawTabRiun:', i, riun);
      return (
        <Grid item p={2} pt={0} xs={7}>
          <DatiFascicoloDetails ricorsoUdienza={riun} />
        </Grid>
      );
    }
    return <></>;
  };
  return isLoading ? (
    <Box sx={{ maxWidth: 400, height: 250, overflowY: 'auto' }}>
      <NsFullPageSpinner value={10} isOpen={isLoading} />
    </Box>
  ) : (
    <>
      {ricorsoUdienzaRiunito?.map((riun: any, i: number) => {
        console.log(`value:${value},i=:${i}, riun:`, riun);
        return (
          <div
            role="tabpanel"
            hidden={value !== i}
            id={`riun-${i}`}
            key={`simple-tab-riun-${i}`}
            aria-labelledby={`simple-tab-riun-${i}`}
          >
            {drawTabRiun(i, riun)}
          </div>
        );
      })}{' '}
    </>
  );
}

export default function RiunitiFascicoliTabs({
  ricorsoUdienza,
  checkStatoOnSIC,
}: DatiFascicoloDetailsProps) {
  console.log('ricorsoUdienza:', ricorsoUdienza);
  const ROOT_QUERY2 = GetRiunitiDetailsSchema;
  const [value, setValue] = useState<number>(0);
  const [riuniti, setRiuniti] =
    useState<relayQueriesRiunitDetails_GetRiunitiDetailsQuery$data | null>(
      null
    );
  const [idsRicudienRiunito, setIdsRicudienRiunito] = useState<Array<number>>(
    []
  );
  const { data: data1, isLoading: isLoading1 } =
    useQuery<relayQueriesRiunitDetails_GetRiunitiDetailsQuery>(
      ROOT_QUERY2,
      {
        idRicUdin: Number(ricorsoUdienza?.idRicudien),
      },
      {
        fetchPolicy: STORE_OR_NETWORK,
        skip: !ricorsoUdienza?.idRicudien,
      }
    );

  useEffect(() => {
    setRiuniti(data1 || null);
    const riunitiList = data1?.getRiuntiByIdRicUdien;
    if (idsRicudienRiunito.length === 0) {
      const map = riunitiList?.map((riun: any) => riun.idRicorsoUdienza);
      setIdsRicudienRiunito(map ?? []);
    }
  }, [data1]);

  const handleChange = (rEvent: React.SyntheticEvent, indexTab: number) => {
    setValue(indexTab);
  };
  const a11yProps = (index: string) => {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  };

  const getTabDetials = () => {
    if (ricorsoUdienza) {
      return (
        <Box sx={{ width: '100%', display: 'inline-flex' }}>
          <Grid item xs={3} md={3} lg={3}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={value}
                orientation={'vertical'}
                onChange={handleChange}
                aria-label="basic tabs example"
              >
                {riuniti?.getRiuntiByIdRicUdien?.map((riun: any, i: number) => {
                  return (
                    <Tab
                      key={'riun-' + i}
                      style={{ paddingLeft: '0rem', paddingRight: '0.25rem' }}
                      label={
                        <Box alignItems="left" display="flex">
                          <Typography variant="h3">
                            {'+(R) ' + riun?.numero + '/' + riun?.anno}
                          </Typography>
                        </Box>
                      }
                      {...a11yProps('riun-' + i)}
                    />
                  );
                })}
              </Tabs>
            </Box>
          </Grid>
          <Grid item xs={9} md={9} lg={9}>
            {riuniti && idsRicudienRiunito?.length > 0 && (
              <GetSinglePanelRiuniti
                idsRicudienRiunito={idsRicudienRiunito}
                riuniti={riuniti}
                value={value}
              />
            )}
          </Grid>
        </Box>
      );
    }
    return <></>;
  };

  return checkStatoOnSIC?.isPrincipalRicorsoRiunito ? (
    isLoading1 ? (
      <Box sx={{ maxWidth: 400, height: 250, overflowY: 'auto' }}>
        <NsFullPageSpinner value={10} isOpen={isLoading1} />
      </Box>
    ) : (
      getTabDetials()
    )
  ) : (
    <></>
  );
}
