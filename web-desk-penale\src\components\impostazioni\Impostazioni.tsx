import { relayQueries_CreaSettingsMutation } from '@/generated/relayQueries_CreaSettingsMutation.graphql';
import { relayQueries_DeleteSettingMutation } from '@/generated/relayQueries_DeleteSettingMutation.graphql';
import { relayQueries_SettingsQuery } from '@/generated/relayQueries_SettingsQuery.graphql';
import { relayQueries_UpdateSettingsMutation } from '@/generated/relayQueries_UpdateSettingsMutation.graphql';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import {
  Box,
  Button,
  Divider,
  Grid,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { NETWORK_ONLY, useMutation, useQuery } from 'relay-hooks';
import {
  CreateSettingsMutation,
  DeleteSettingMutation,
  SettingsQuery,
  UpdateSettingsMutation,
} from '../relay/relayQueries';
import { useConfig } from '../shared/configuration.context';
import GppBadIcon from '@mui/icons-material/GppBad';
import GppGoodIcon from '@mui/icons-material/GppGood';
import {userRole } from '../shared/Utils';
import { useGetRuolo } from '../shared/GetRuolo';
import { useSession } from 'next-auth/react';

export default function Intestazioni(ruolo: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();

  const { data: sessionToken } = useSession();

  const [usernameFirma, setUsernameFirma] = useState<string>('');
  const [passwordFirma, setPasswordFirma] = useState<string>('');
  const [cfUtente, setCfUtente] = useState<string>('');

  const { notify } = useNotifier();
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => setShowPassword(!showPassword);
  const handleMouseDownPassword = () => setShowPassword(!showPassword);
  const [isLoading2, setIsloading2] = useState<boolean>(false);

  const { servizi } = useConfig();
  const emailServiceUrl = `${servizi}/auth/emailuser`;
  const firmaRemotaUserUrl = `${servizi}/auth/checkUser`;

  const [firmaRemota, setFirmaRemota] = useState<boolean>(false);

  const [nomeCognome, setNomeCognome] = useState<string>();
  const [email, setEmail] = useState<string>();

  const ROOT_QUERY = SettingsQuery;
  const ROOT_CREATE = CreateSettingsMutation;
  const ROOT_UPDATE = UpdateSettingsMutation;
  const ROOT_DELETE = DeleteSettingMutation;

  const getCheckFirmaRemota = async () => {
    try {
      setIsloading2(true);
      const response = await axios.get(firmaRemotaUserUrl);
      setFirmaRemota(response.data);
      setIsloading2(false);
    } catch (error) {
      console.error('error:', error);
    }
  };

  const getFullUserName = () => {
    const token = sessionToken;
    if (token) {
      const user = token?.user as any;
      const fullName = `${user?.fName} ${user?.lName}`;
      setNomeCognome(fullName);
    }
  };

  const getEmail = async () => {
    try {
      const response = await axios.get(emailServiceUrl);
      setEmail(response.data);
    } catch (error) {
      console.log('error', error);
    }
  };

  const elimina = async () => {
    try {
      setIsloading2(true);
      await deleteSettings({ variables: {} })
        .then((res) => {
          notify({
            type: 'success',
            message: t('impostazioni.impostazioniEliminate'),
          });
          setUsernameFirma('');
          setPasswordFirma('');
          setCfUtente('');
          setIsloading2(false);
        })
        .catch((err) => {
          notify({
            type: 'error',
            message: "Errore nell'eliminazione dei dati",
          });
          setIsloading2(false);
        });
    } catch (error) {
      console.log('error', error);
      notify({
        type: 'error',
        message: error as string,
      });
      setIsloading2(false);
    }
  };

  const conferma = async () => {
    try {
      setIsloading2(true);
      if (usernameFirma && passwordFirma) {
        if (cfUtente == null || cfUtente == '') {
          await createSettings({
            variables: {
              settingsInput: {
                cfUtente: '',
                usernameFirma: usernameFirma.trim(),
                passwordFirma: passwordFirma.trim(),
              },
            },
          })
            .then((res) => {
              console.log();
              notify({
                type: 'success',
                message: t('impostazioni.impostazioniInserite'),
              });
              setCfUtente(
                res.creaSettings.cfUtente ? res.creaSettings.cfUtente : ''
              );
              setIsloading2(false);
            })
            .catch((err) => {
              notify({
                type: 'error',
                message: "Errore nell'inserimento dei nuovi dati",
              });
              setIsloading2(false);
            });
        } else {
          await updateSettings({
            variables: {
              settingsInput: {
                cfUtente: '',
                usernameFirma: usernameFirma.trim(),
                passwordFirma: passwordFirma.trim(),
              },
            },
          })
            .then((res) => {
              notify({
                type: 'success',
                message: t('impostazioni.modificaImpostazioni'),
              });
              setIsloading2(false);
            })
            .catch((err) => {
              notify({
                type: 'error',
                message: 'Errore nella modifica dei nuovi dati',
              });
              setIsloading2(false);
            });
        }
      } else {
        notify({
          type: 'error',
          message: 'Dati obbligatori non inseriti!',
        });
        setIsloading2(false);
      }
    } catch (error) {
      console.log('error', error);
      setIsloading2(false);
    }
  };

  const { data } = useQuery<relayQueries_SettingsQuery>(
    ROOT_QUERY,
    {},
    { fetchPolicy: NETWORK_ONLY }
  );

  const [createSettings] =
    useMutation<relayQueries_CreaSettingsMutation>(ROOT_CREATE);

  const [updateSettings] =
    useMutation<relayQueries_UpdateSettingsMutation>(ROOT_UPDATE);

  const [deleteSettings] =
    useMutation<relayQueries_DeleteSettingMutation>(ROOT_DELETE);

  useEffect(() => {
    getFullUserName();
    getEmail();
    getCheckFirmaRemota();

    if (data && data?.impostazioniByCf?.usernameFirma != null) {
      setUsernameFirma(data?.impostazioniByCf?.usernameFirma?.trim());
    } else {
      setUsernameFirma('');
    }
    if (data && data?.impostazioniByCf?.passwordFirma != null) {
      setPasswordFirma(data?.impostazioniByCf?.passwordFirma?.trim());
      setCfUtente(
        data?.impostazioniByCf?.cfUtente ? data?.impostazioniByCf?.cfUtente : ''
      );
    } else {
      setPasswordFirma('');
    }
  }, [data]);

  const getRuolo = useGetRuolo();
  const ruoloUtente = userRole() ?? getRuolo;
  const autenticatoSIC = Boolean(ruoloUtente?.length > 0);

  const styledInfo = {
    color: '#333',
    fontWeight: 'normal',
    fontFamily: 'Titillium Web, sans-serif',
  };

  return (
    <Grid
      container
      alignSelf="center"
      alignItems="center"
      justifyItems={'center'}
      justifySelf="center"
    >
      <Grid container item xl={3} md={2} sm={0} xs={0}></Grid>
      <Grid
        alignSelf="center"
        item
        xl={6}
        md={8}
        sm={12}
        xs={12}
        alignItems="center"
        border={theme.custom.borders[0]}
        sx={{ borderRadius: '30px', mt: 2 /* height: 'fit-content' */ }}
      >
        <Grid p={2} display="flex" justifyContent={'space-between'}>
          <Box display="flex">
            <Typography variant="h2" sx={{ textTransform: 'uppercase' }}>
              {t('impostazioni.impostazioniUtenteCollegato')}
            </Typography>

            {autenticatoSIC ? (
              <NsTooltip
                title="Utente presente in anagrafica SIC"
                icon={<GppGoodIcon sx={{ color: 'green' }} />}
              />
            ) : (
              <NsTooltip
                title="Utente non presente in anagrafica SIC"
                icon={<GppBadIcon sx={{ color: 'red' }} />}
              />
            )}
          </Box>
        </Grid>
        <Grid
          container
          item
          md={8}
          xs={8}
          justifyContent={'space-between'}
          sx={{ p: 2, m: 2 }}
        >
          <Box>
            <Typography mt={2} variant="h3">
              {`${t('impostazioni.email')} `}
              <span style={styledInfo}>{email}</span>{' '}
              {`${t('impostazioni.nomeUtente')} `}
              <span style={styledInfo}>{nomeCognome}</span>
              {/* <Typography mt={2} variant="h3">
              {t('impostazioni.ruolo')} {ruolo?.ruolo}
            </Typography> */}
            </Typography>
          </Box>
        </Grid>
        <Divider light sx={{ m: 4 }} />
        <Grid container item>
          <Box sx={{ p: 2 }}>
            <Typography mt={2} variant="h2" sx={{ textTransform: 'uppercase' }}>
              {t('impostazioni.credenzialiFirmaRemota')}
            </Typography>
          </Box>
          {/*<Box sx={{ p:2 }}>*/}
          {/*  {firmaRemota ? (*/}
          {/*    <Tooltip title="Utente abilitato alla firma remota">*/}
          {/*      <GppGoodIcon sx={{ color: 'green' }} />*/}
          {/*    </Tooltip>*/}
          {/*  ) : (*/}
          {/*    <Tooltip title="Utente non abilitato alla firma remota">*/}
          {/*      <GppBadIcon sx={{ color: 'red' }} />*/}
          {/*    </Tooltip>*/}
          {/*  )}*/}
          {/*</Box>*/}
        </Grid>
        {isLoading2 ? (
          <NsFullPageSpinner isOpen={true} value={1} />
        ) : (
          <Grid container item md={12} xs={12} sx={{ p: 4 }}>
            <Box>
              <TextField
                id="usernameFirma"
                label={t('impostazioni.nomeUtente')}
                defaultValue=""
                placeholder=""
                type="text"
                inputProps={{
                  autoComplete: 'new-password',
                  form: {
                    autoComplete: 'off',
                  },
                }}
                value={usernameFirma}
                onChange={(e) => setUsernameFirma(e.target.value)}
              />
            </Box>
            <Box ml="2rem" className="box-media-ns">
              <TextField
                id="passwordFirma"
                label={t('impostazioni.password')}
                type={showPassword ? 'text' : 'password'}
                defaultValue=""
                placeholder=""
                autoComplete="off"
                value={passwordFirma ? passwordFirma : ''}
                onChange={(e) => setPasswordFirma(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        onMouseDown={handleMouseDownPassword}
                      >
                        {showPassword ? <Visibility /> : <VisibilityOff />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Grid>
        )}
        {isLoading2 ? (
          <Box />
        ) : (
          <Grid
            container
            item
            display="flex"
            alignContent="flex-end"
            alignItems="right"
            className="grid-imp-ns"
            ml="2rem"
            sx={{ pr: 5, pb: 5 }}
          >
            <NsButton
              sx={{ mt: 3, width: '300px' }}
              size="small"
              variant="contained"
              onClick={() => elimina()}
            >
              {t('impostazioni.elimina').toUpperCase()}
            </NsButton>
            <NsButton
              sx={{ mt: 3, width: '300px', marginLeft: '1rem' }}
              size="small"
              variant="contained"
              className="grid-imp-ns"
              onClick={() => conferma()}
            >
              {t('impostazioni.conferma').toUpperCase()}
            </NsButton>
          </Grid>
        )}
      </Grid>
    </Grid>
  );
}
