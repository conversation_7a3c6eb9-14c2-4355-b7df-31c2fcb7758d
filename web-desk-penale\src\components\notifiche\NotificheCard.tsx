import FolderIcon from '@mui/icons-material/Folder';
import { Box, Grid, Typography } from '@mui/material';
import { NsButton, NsTooltip } from '@netservice/astrea-react-ds';
import { useEffect, useState } from 'react';
import { formatDate } from '../shared/Utils';
import { useTranslation } from 'next-i18next';

const styleCard = {
  boxShadow: '1px 0px 15px rgba(173, 173, 173, 0.34901960784313724)',
  '&:hover': {
    backgroundColor: '#D0E6E3',
  },
};

const selectedCard = {
  boxShadow: '1px 0px 15px rgba(173, 173, 173, 0.34901960784313724)',
  backgroundColor: '#D0E6E3',
  borderLeft: '4px solid #308a7d',
};

interface NotificheCardProps {
  notifica: any;
  selected: boolean;
  setReadIds: (callback: (ids: string[]) => string[]) => void;
  removeNotification: (ids: string[], url: string, nrg: number) => void;
}

export default function NotificheCard({
  notifica,
  selected,
  setReadIds,
  removeNotification,
}: Readonly<NotificheCardProps>) {
  const { t } = useTranslation();
  const read = notifica?.read;
  const [select, setSelect] = useState<boolean>(selected);

  useEffect(() => {
    setSelect(selected);
  }, [selected]);

  const handleSelect = (id: string) => {
    setSelect((preview) => !preview);
    setReadIds((prevIds: string[]) => {
      const idIndex = prevIds?.indexOf(id);
      if (idIndex !== -1) {
        return prevIds?.filter((existingId) => existingId !== id);
      } else {
        return [...prevIds, id];
      }
    });
  };

  const handleClick = (
    nrg: number,
    idUdienza: number,
    isEstensore: boolean,
    idNodifica?: string
  ) => {
    let url = isEstensore
      ? `/fascicolo/${idUdienza}`
      : `/scrivania/fascicolo/${idUdienza}`;
    if (idNodifica) {
      removeNotification([idNodifica], url, nrg);
    }
  };

  return (
    <Grid xs={12} mt={1} item sx={select ? selectedCard : styleCard} p={2}>
      <Box display="flex" justifyContent="space-between">
        <Box display="flex" alignItems="center">
          <Box
            sx={{
              width: '17px',
              height: '17px',
              borderRadius: '50%',
              marginRight: '5px',
              background: select ? '#307973' : '',
              border: select ? '1px solid #307973' : '1px solid black',
            }}
            onClick={() => handleSelect(notifica?.idNotifica)}
          ></Box>
          <Typography width={'100%'} variant={read ? 'h5' : 'h2'}>
            {notifica?.typeBusta} -{' '}
            {notifica?.tipoProvvedimento?.toLowerCase().replace('_', ' ')} -{' '}
            {notifica?.numeroFascicolo}/{notifica?.annoFascicolo}{' '}
            {notifica?.isPrincipale ? ' + (R)' : ''}:{notifica?.descType}
            <span style={{ fontSize: '14px', marginLeft: '20px' }}>
              {t('notifiche.notificheCard.ore')}{' '}
              {formatDate(notifica?.dataCreazione, 'HH:mm')}
            </span>
          </Typography>
        </Box>
        <Typography variant="h5">
          {' '}
          {formatDate(notifica.dataCreazione, 'MMM D').toUpperCase()}
        </Typography>
      </Box>
      <Typography
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mt={1}
        variant="h5"
      >
        {t('notifiche.notificheCard.udienza')}{' '}
        {formatDate(notifica.dataUdienza, 'DD-MM-YYYY')},{' '}
        {notifica.sezione?.descrizione}, {notifica.tipoUdienza?.descrizione},{' '}
        {notifica.coleggio?.descrizione}
        <NsButton
          onClick={() =>
            handleClick(
              notifica?.nrg,
              notifica?.idUdienza,
              notifica?.isEstensore,
              notifica?.idNotifica
            )
          }
          size="small"
          variant="contained"
        >
          <NsTooltip
            title={t('notifiche.notificheCard.visualizzaFascicolo')}
            icon={<FolderIcon fontSize="small" />}
          />
        </NsButton>
      </Typography>
    </Grid>
  );
}
