import { relayQueries_NotificheQuery } from '@/generated/relayQueries_NotificheQuery.graphql';
import { relayQueries_ReadNotificaMutation } from '@/generated/relayQueries_ReadNotificaMutation.graphql';
import SearchIcon from '@mui/icons-material/Search';
import { Box, Grid, TextField, Typography, useTheme } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier
} from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation, useQuery } from 'relay-hooks';
import { TableUpdateInfo } from 'src/interfaces';
import { NotificheSchema, ReadNotify } from '../relay/relayQueries';
import MainPagination from '../shared/MainPagination';
import { useConfig } from '../shared/configuration.context';
import NotificheCard from './NotificheCard';
import { useTranslation } from 'next-i18next';

const ROOT_QUERY = NotificheSchema;

const searchCss = {
  border: '1px solid',
  height: '104%',
  width: '45px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  background: '#B0B0B0',
  marginLeft: '-1px',
  cursor: 'pointer',
};

const ROOT_UPDATE = ReadNotify;

export default function Notifiche() {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const [readNotifyByIds] =
    useMutation<relayQueries_ReadNotificaMutation>(ROOT_UPDATE);
  const { notify } = useNotifier();

  const [tableInfo, setTableInfo] = useState<TableUpdateInfo>({
    pageSize: 10,
    after: '0',
    term: null,
  });
  const [notifiche, setNotifiche] = useState<any>([]);
  const [aggregate, setAggregate] = useState<{
    count: number;
    total: number;
    unread?: number;
  }>({ count: tableInfo.pageSize, total: 0 });
  const [readIds, setReadIds] = useState<string[]>([]);
  const [redirectUrl, setRedirecturl] = useState<{
    url?: string;
    nrg?: number;
  }>({});

  const [searchValue, setSearchValue] = useState<string>('');
  const [showSpinner, setShowSpinner] = useState<boolean>(true);

  const { setNotifichationsCount } = useConfig();
  const router = useRouter();

  const { data, isLoading, retry } = useQuery<relayQueries_NotificheQuery>(
    ROOT_QUERY,
    {
      first: tableInfo.pageSize,
      after: tableInfo.after,
      term: tableInfo.term,
    },
    {
      fetchPolicy: 'network-only',
    }
  );

  const mappedNotifiche = useMemo(() => {
    if (!data?.notificheByCurrentUser?.edges) 
      return [];
    const { notificheByCurrentUser } = data;
    return notificheByCurrentUser.edges.map((notifiche) => {
      const data = notifiche.node.descrizione?.split(':') ?? ['', ''];
      return { ...notifiche.node, typeBusta: data[0], descType: data[1] };
    });
  },[data]);

  useEffect(() => {
    if (data) {
      setNotifiche(mappedNotifiche);
      const totalNotifications =
        data?.notificheByCurrentUser?.aggregate?.total || 0;
      const unreadNotifications =
        data?.notificheByCurrentUser?.aggregate?.unread || 0;
      setAggregate(prev => ({
        ...prev,
        total: totalNotifications,
        unread: unreadNotifications,
      }));
      setNotifichationsCount!({
        unread: unreadNotifications,
        read: totalNotifications - unreadNotifications,
      }); 
      if (!isLoading) 
        setShowSpinner(false);
    }
  }, [data, isLoading, mappedNotifiche, setNotifichationsCount]);

  useEffect(() => {
    if (redirectUrl) {
      router.push({
        pathname: redirectUrl.url,
        query: {
          params: redirectUrl.nrg,
        },
      });
    }
  }, [redirectUrl, router]);

  const pageNumber = useCallback((index: number) => {
    setShowSpinner(true);
    setTableInfo(prev => ({ ...prev, after: (prev.pageSize * index).toString() }));
  },[]);

  const rowsPerPage = useCallback((pageSizeNew: number) => {
    setShowSpinner(true);
    setTableInfo(prev => ({ ...prev, pageSize: pageSizeNew, after: '0' }));
  },[]);

  const handleGoBack = () => {
    router.push('/calendario');
  };

  const selectAll = useCallback(() => {
    if (readIds.length === notifiche.length) {
      const newNotifiche = notifiche.map((n: any) => {
        return { ...n, selected: false };
      });
      setNotifiche(newNotifiche);
      setReadIds([]);
    } else {
      const newNotifiche = notifiche.map((n: any) => {
        return { ...n, selected: true };
      });
      setNotifiche(newNotifiche);
      setReadIds(notifiche.map((n: any) => n.idNotifica));
    }
  },[notifiche, readIds.length]);

  const getIds = useCallback((ids: string[], url?: string, nrg?: number) => {
    if (ids.length === 0) {
      return;
    }
    setRedirecturl({ url, nrg });
    readNotifyByIds({
      variables: {
        segnaLette: {
          ids,
        },
      },
    })
      .then((res) => {
        retry();
        setReadIds([]);
        if (ids.length > 1) {
          notify({
            message: 'Notifiche segnate come lette',
            type: 'success',
          });
        }
      })
      .catch((err) => {
        notify({
          message: 'Error',
          type: 'error',
        });
      });
  },[notify, retry, setRedirecturl]);

  const handleKeyDown = (event: any) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSearch = () => {
    setShowSpinner(true);
    setTableInfo({ ...tableInfo, term: searchValue });
  };

  const handleChange = (event: any) => {
    setSearchValue(event.target.value);
  };

  return (
    <>
      <Typography p={2} mt={2} mb={2} variant="h1">
        {t('notifiche.segnalazioni')}
      </Typography>
      <Typography></Typography>
      <Grid container justifyContent="center">
        <Grid
          item
          container
          justifyContent="center"
          p={2}
          xs={12}
          border={theme.custom.borders[0]}
        >
          <Grid item xs={9}>
            <Box
              display="flex"
              className="segnelazioniSearch"
              mb={3}
              mt={3}
              justifyContent="space-between"
            >
              <Box display="flex" justifyContent="space-between">
                <NsButton
                  size="small"
                  onClick={selectAll}
                  variant="contained"
                  sx={{ marginRight: 2 }}
                >
                  {readIds.length === notifiche.length
                    ? t('notifiche.deselezionaTutte')
                    : t('notifiche.selezionaTutte')}
                </NsButton>
                <NsButton
                  size="small"
                  onClick={() => getIds(readIds)}
                  variant="contained"
                >
                  {t('notifiche.segnaComeLette')}
                </NsButton>
              </Box>
              <Box display="flex" alignItems="center">
                <TextField
                  size="small"
                  onKeyDown={handleKeyDown}
                  onChange={handleChange}
                  value={searchValue}
                  label={t('notifiche.cerca')}
                  variant="outlined"
                />
                <Box onClick={handleSearch} sx={searchCss}>
                  <SearchIcon />
                </Box>
              </Box>
            </Box>
            {notifiche.length > 0 ? (
              notifiche?.map((notifica: any) => {
                return (
                  <NotificheCard
                    setReadIds={setReadIds}
                    selected={readIds.includes(notifica.idNotifica)}
                    key={notifica.idNotifica}
                    notifica={notifica}
                    removeNotification={(
                      ids: string[],
                      url: string,
                      nrg: number
                    ) => getIds(ids, url, nrg)}
                  />
                );
              })
            ) : (
              <Typography variant="h2">
                {t('notifiche.nessunaNotifica')}
              </Typography>
            )}
            {notifiche.length > 0 && (
              <Box sx={{ float: 'right' }}>
                <MainPagination
                  aggregate={aggregate}
                  pageNumber={(page: number) => pageNumber(page)}
                  rowsPerPageFunction={(rowsPerPageNew: number) =>
                    rowsPerPage(rowsPerPageNew)
                  }
                />
              </Box>
            )}
          </Grid>
        </Grid>
      </Grid>
      <NsFullPageSpinner isOpen={showSpinner} value={1} />
    </>
  );
}
