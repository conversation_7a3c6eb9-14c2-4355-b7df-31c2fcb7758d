import { Box, Button, Grid, Typography } from '@mui/material';
import UdienzaDatiCalendarHeader from '../scrivania/UdienzaDatiCalendarHeader';
import ProvvedimentoTable from './ProvvedimentoTable';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import VisualTable from './VisualTable';
import { useRouter } from 'next/router';
import axios from 'axios';
import { useConfig } from '../shared/configuration.context';
import {graphql, useMutation, useQuery} from 'relay-hooks';
import { ImportaProvvedimentiMutation } from '@/generated/ImportaProvvedimentiMutation.graphql';
import { Conferma, Prosegui } from '../shared/Conferma';
import MainModal from '../shared/MainModal';
import {formatDate, modalStyle} from '../shared/Utils';
import DialogModal from '../shared/DialogModal';
import {relayQueries_DatiGeneraliUdienzaQuery} from "@/generated/relayQueries_DatiGeneraliUdienzaQuery.graphql";
import {DatiGeneraliUdienzaSchema} from "../relay/relayQueries";
import {STORE_THEN_NETWORK} from "relay-hooks/lib/RelayHooksTypes";

const mainLinks = {
  margin: 0,
  color: '#308A7D',
  textDecoration: 'underline',
  textDecorationColor: 'rgba(48, 138, 125, 0.4)',
  cursor: 'pointer',
};

const mutationImport = graphql`
  mutation ImportaProvvedimentiMutation($input: CreateProvvLavorazioneInput!) {
    GenerazioneProvvedimentoCreateMutation(
      createProvvLavorazioneInput: $input
    ) {
      idProvvedimento
    }
  }
`;

export default function ImportaProvvedimenti() {
  const { t } = useTranslation();
  const router = useRouter();
  const { servizi } = useConfig();

  const serviceUrl = `${servizi}/provvedimento/saveFileLavorazione/`;

  const [mutateChange, { loading }] =
    useMutation<ImportaProvvedimentiMutation>(mutationImport);

  const [importedData, setImportedData] = useState<any[] | null>(null);
  const [eventSelect, setEventSelect] = useState<any | null>(null);
  const [firmaDeposita, setFirmaDeposita] = useState<boolean>(false);
  const [termineDeposito, setTermineDeposito] = useState<any>();
  const [disabledFirmaDeposita, setDisabledFirmaDeposita] =
    useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { idUdienza } = router.query;
  const { data:datiGeneraliUdienza, isLoading:datiGeneraliUdienzaLoading, retry } =
    useQuery<relayQueries_DatiGeneraliUdienzaQuery>(
      DatiGeneraliUdienzaSchema,
      {
        id: Number(idUdienza),
      },
      {
        fetchPolicy: STORE_THEN_NETWORK,
      }
    );
  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const closeConfirmModal = (provvedimenti?: any[]) => {
    setConfermaModal({ ...confermaModal, modal: false });
    setModalProps({
      ...modalProps,
      isOpen: true,
      content: <VisualTable provvedimenti={provvedimenti} />,
      onConfirm: () => reConfirm(provvedimenti ?? []) as any,
    });
  };
  const [modalProps, setModalProps] = useState({
    isOpen: false,
    title: '',
    content: <></>,
    onClose: closeModal,
    showCancelButton: true,
    maxWidth: 'sm',
    onConfirm: () => {
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('depositiSelected', JSON.stringify([]));
      }
      router.push({
        pathname: '/deposito/massivo',
        query: {
          calendar: 'true',
          isImportaMassivo: 'true',
        },
      });
    },
  });

  const [confermaModal, setConfermaModal] = useState({
    modal: false,
    closeConfirmModal,
    style: modalStyle,
    title: 'Provvedimenti non oscurati',
    body: (
      <Prosegui
        closeModal={() => closeConfirmModal([])}
        body={t('pages.libero.confermaProseguiSenzaSott')}
        confirm={() => {}}
      />
    ),
  });

  useEffect(() => {
    const storedData = localStorage.getItem('importaProvvedimenti');
    if (storedData) {
      const parse = JSON.parse(storedData);
      console.log(parse);
      setImportedData(parse);
    }
    if(datiGeneraliUdienza) {
      console.log(datiGeneraliUdienza)
      const eventSelect = {
        sezione: datiGeneraliUdienza?.udienza?.sezione?.sigla,
        aula: datiGeneraliUdienza?.udienza?.aula?.sigla,
        descrizione: datiGeneraliUdienza?.udienza?.tipoUdienza?.descrizione
      }
      let format = formatDate(datiGeneraliUdienza?.udienza?.termineDeposito, 'MM-DD-YYYY');
      setTermineDeposito(
        formatDate(format, 'DD-MM-YYYY')
      );
      setEventSelect(eventSelect);
    }
    /*  return () => {
      localStorage.removeItem('importaProvvedimenti');
    };*/
  }, [datiGeneraliUdienza]);

  const handleRefresh = (_event: any) => {
    console.log('refresh');
    //  localStorage.removeItem('importaProvvedimenti');
  };
  window.onbeforeunload = function () {
    return true;
  };

  useEffect(() => {
    addEventListener('beforeunload', handleRefresh);

    return () => {
      window.removeEventListener('beforeunload', handleRefresh);
    };
  }, []);

  const clickedFirma = (data: any) => {
    setModalProps({
      ...modalProps,
      isOpen: true,
      content: <VisualTable provvedimenti={data} />,
      onConfirm: () => reConfirm(data) as any,
    });
    setFirmaDeposita(false);
  };

  const reConfirm = async (provvedimenti: any[]) => {
    const requiresConfirmation = provvedimenti.some(
      (provv) => provv.file && provv.oscuratoSicComplessivo
    );
    setConfermaModal({
      ...confermaModal,
      modal: true,
      body: requiresConfirmation ? (
        <Prosegui
          closeModal={() => closeConfirmModal(provvedimenti)}
          body={t('pages.libero.confermaProseguiSenzaSott')}
          confirm={() => confirmImports(provvedimenti)}
        />
      ) : (
        <></>
      ),
    });
    if (!requiresConfirmation) {
      confirmImports(provvedimenti);
    }
  };

  const confirmImports = async (provvedimenti: any[]) => {
    try {
      setIsLoading(true);
      setModalProps({ ...modalProps, isOpen: false });
      setConfermaModal({ ...confermaModal, modal: false });

      const provvList = await Promise.all(
        provvedimenti.map(async (provv: any) => {
          const idProvv = provv.idProvvedimento;
          await callAddCodaDiFirma(idProvv);
          return idProvv;         
        })
      );
      
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('depositiSelected', JSON.stringify(provvList));
      }
      await router.push({
        pathname: '/deposito/massivo',
        query: {
          calendar: 'true',
          isImportaMassivo: 'true',
        },
      });
    } catch (error) {
      console.error('Error deposito massivo:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const callAddCodaDiFirma = async (idProvvedimento: number) => {
    return await axios
      .get(servizi + '/provvedimento/addCodaDeposito/' + idProvvedimento)
      .then(async (res) => {
        return true;
      })
      .catch((error) => {
        console.error(`Error durante aggiunta in coda di firma, idProvv:${idProvvedimento}`, error);
        throw error;
      });
  };

  return (
    <>
      <Grid p={5} container>
        <Grid item xs={12} mb={5}>
          <Typography variant="h2">
            <Link style={mainLinks} href="/calendario">
              {t('fascicolo.detailFascicolo.calendarioUdienze')}
            </Link>
            {' > '}
            <Box component="span">
              {t('fascicolo.provvedimentiTable.importaProvvedimento')}
            </Box>
          </Typography>
        </Grid>
        {importedData ? (
          <>
            <Grid xs={12} mb={5} md={3} mr={5}>
              <UdienzaDatiCalendarHeader
                mainCollegio={datiGeneraliUdienza?.udienza.collegio}
                termineDeposito={termineDeposito}
                selectedEvent={eventSelect}
              />
            </Grid>
            <Grid xs={12} md={8}>
              <Typography mb={2} variant="h1">
                {t('fascicolo.provvedimentiTable.importaProvvedimento')}
              </Typography>
              <ProvvedimentoTable
                provvedimenti={importedData}
                firmaDeposita={firmaDeposita}
                clickedFirma={(data: any) => clickedFirma(data)}
                getUploaded={(data: boolean) => setDisabledFirmaDeposita(data)}
                idUdienza={idUdienza}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={() => setFirmaDeposita(true)}
                disabled={disabledFirmaDeposita}
              >
                {t('provvedimento.importaProvvedimenti.firmaEDeposita')}
              </Button>
            </Grid>
          </>
        ) : (
          <Typography variant="h3">
            {t(
              'provvedimento.importaProvvedimenti.nessunProvvedimentoImportato'
            )}
          </Typography>
        )}
      </Grid>
      <DialogModal {...modalProps} />
      <MainModal {...confermaModal} />
      <NsFullPageSpinner isOpen={isLoading} value={100} />
      <style jsx global>{`
        .MuiModal-root > .MuiBox-root {
          width: 700px;
        }
      `}</style>
      ;
    </>
  );
}
