import { useTranslation } from 'react-i18next';
import RelayTable from '../shared/RelayTable';
import { Column } from 'src/interfaces';
import {
  Box,
  LinearProgress,
  TableCell,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier
} from '@netservice/astrea-react-ds';
import {getStateNames, updateElementById} from '../shared/Utils';
import { StatoProvvedimentiEnum } from '../../types/types';
import TrackingStato from '../calendar/TrackingStato';
import DetailReati from '../calendar/DetailReati';
import DetailParti from '../calendar/DetailParti';
import { useEffect, useState } from 'react';
import DatiUdienza from '../shared/DatiUdienza';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import { graphql, useMutation } from 'relay-hooks';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import {
  ProvvedimentiOrigineEnum,
  ProvvedimentiTipoEnum,
} from '@/generated/ApiLiberoCreazioneProvvedimentoMutation.graphql';
import axios from 'axios';
import { useConfig } from '../shared/configuration.context';
import { ProvvedimentoTableMutation } from '@/generated/ProvvedimentoTableMutation.graphql';
import DownloadIcon from '@mui/icons-material/Download';
import DialogModal from '../shared/DialogModal';
import {StatiSic} from "../../types";

const mutationImport = graphql`
  mutation ProvvedimentoTableMutation($input: CreateProvvLavorazioneInput!) {
    GenerazioneProvvedimentoCreateMutation(
      createProvvLavorazioneInput: $input
    ) {
      idProvvedimento
    }
  }
`;

export default function ProvvedimentoTable({
  provvedimenti,
  firmaDeposita,
  clickedFirma,
  getUploaded,
  idUdienza
}: any) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const { servizi } = useConfig();
  const { notify } = useNotifier();
  const [mutateChange, { loading }] =
    useMutation<ProvvedimentoTableMutation>(mutationImport);

  const [data, setData] = useState<[]>(provvedimenti);

  const closeModal: any = (_event: React.MouseEvent<HTMLButtonElement>) => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    title: '',
    content: <DatiUdienza />,
    openFromParent: true,
    onClose: closeModal,
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (firmaDeposita) {
      const uploaded = data.filter((provv: any) => provv.file);
      clickedFirma([...uploaded]);
    }
  }, [firmaDeposita]);

  const serviceUrl = `${servizi}/provvedimento/saveFileLavorazione/`;

  useEffect(() => {
    const map = provvedimenti?.map((provv: any) => ({ ...provv, file: null, progress: 0 }));
    setData(
      map
    );
  }, []);

  useEffect(() => {
    const uploaded = data.filter((provv: any) => provv.file);
    getUploaded(uploaded.length ? false : true);
  }, [data]);

  const handleFileChange = async (e: any, row: any) => {
    const selectedFile = e.target.files[0];
    const fileName = selectedFile.name.toLowerCase();
    
    // Check for supported file types
    if (!fileName.endsWith('.pdf') && !fileName.endsWith('.docx')) {
      notify({
        message: `Il formato del file "${selectedFile.name}" non è supportato. Si prega di utilizzare file PDF o DOCX.`,
        type: 'error',
      });
      return;
    }

    updateElementById('block', `linearProgress-${row.idRicudien}`);
    updateElementById('none', `upload-icon-${row.idRicudien}`);
    const tipologia = row.isPresidente
      ? row.tipologia
      : 'MINUTA_' + row.tipologia;

    // Convert file to PDF if it's a .docx
    let fileToUpload = selectedFile;
    if (selectedFile.name.toLowerCase().endsWith('.docx')) {
      const formData = new FormData();
      formData.append('file', selectedFile);
      try {
        const response = await axios.post(
          `${servizi}/provvedimento/convertToPdf`,
          formData,
          {
            headers: {
              'X-Origin': 'IMPORTAZIONE',
            },
            responseType: 'blob',
          }
        );
        fileToUpload = new File(
          [response.data],
          selectedFile.name.replace('.docx', '.pdf'),
          { type: 'application/pdf' }
        );
      } catch (error) {
        notify({
          message: 'Error converting file to PDF',
          type: 'error',
        });
        return;
      }
    }

    callUploadFile(
      tipologia,
      fileToUpload,
      row.ricorso.nrg,
      'LOCALE',
      idUdienza,
      row.ricorso.anno,
      row.ricorso.numero,
      row.idRicudien
    );
  };

  const callUploadFile = (
    tipologiaProvvedimento: ProvvedimentiTipoEnum,
    file: any,
    nrg: string,
    origine: ProvvedimentiOrigineEnum,
    idUdienza: string,
    anRuolo: number,
    numRuolo: number,
    idRicudien?: boolean
  ) => {
    const nrgNumber = parseInt(nrg);
    const idUdienzaNumber = parseInt(idUdienza);

    const formData = new FormData();
    formData.append('files', file);
    formData.append('codeFirma', JSON.stringify(false));
    return mutateChange({
      variables: {
        input: {
          nrg: nrgNumber,
          origine: origine,
          idUdienza: idUdienzaNumber,
          allegatoOscurato: false,
          argsProvvedimento: {
            tipologiaProvvedimento,
            anRuolo,
            numRuolo,
          },
        },
      },
    })
      .then(async (res) => {
        const result = await axios
          .post(
            serviceUrl +
              `${res.GenerazioneProvvedimentoCreateMutation.idProvvedimento}`,
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              onUploadProgress: (event: any) => {
                setData((prev): any => {
                  return prev.map((provv: any) => {
                    if (provv.idRicudien === idRicudien) {
                      return {
                        ...provv,
                        progress: (Math.round(100 * event.loaded) /
                          event.total) as number,
                        uploadError: false,
                        idProvvedimento:
                          res.GenerazioneProvvedimentoCreateMutation
                            .idProvvedimento,
                      };
                    } else {
                      return provv;
                    }
                  });
                });
              },
            }
          )
          .then((res) => {
            setData((prev): any => {
              return prev.map((provv: any) => {
                if (provv.idRicudien === idRicudien) {
                  return {
                    ...provv,
                    file,
                    uploadError: false,
                    stato: 'BOZZA',
                    tipoProvvedimento: tipologiaProvvedimento,
                  };
                } else {
                  return provv;
                }
              });
            });
            updateElementById('none', `linearProgress-${idRicudien}`);
            return true;
          })
          .catch((err) => {
            setData((prev): any => {
              return prev.map((provv: any) => {
                if (provv.idRicudien === idRicudien) {
                  return {
                    ...provv,
                    uploadError: true,
                    progress: 80,
                    file: null,
                  };
                } else {
                  return provv;
                }
              });
            });
            return false;
          });
        return result;
      })
      .catch((err) => {
        setData((prev): any => {
          return prev.map((provv: any) => {
            if (provv.idRicudien === idRicudien) {
              return {
                ...provv,
                uploadError: true,
                progress: 50,
                file: null,
              };
            } else {
              return provv;
            }
          });
        });
        updateElementById('block', `upload-icon-${idRicudien}`);
        return false;
      })
      .finally(() => {
        // setIsloading(false);
      });
  };

  const handleModal = (param: string, data?: any) => {
    let content, title;
    const id = data.provvedimento?.idProvvedimento;
    const isEstensorePresidente = Boolean(
      data.isEstensore && data.isPresidente
    );
    if (param == 'stato') {
      if (id !== undefined && id !== null) {
        content = (
          <TrackingStato
            id={id}
            sicCheckStato={null}
            roles="RELATORE"
            isEstensorePresidente={isEstensorePresidente}
          />
        );
        title = 'Stato provvedimento fascicolo ' + data.nrg;
      } else {
        return;
      }
    } else if (param == 'reati') {
      content = <DetailReati reati={data} />;
      title = '';
    } else {
      content = <DetailParti parti={data.ricorso.nrg} />;
      title = `Parti fascicolo ${data.ricorso.numero || ''}/${
        data.ricorso.anno || ''
      }`;
    }

    setModalProps({
      ...modalProps,
      content: content!,
      isOpen: true,
      title: title ?? '',
    });
  };

  const renderParti = (cell: any, row: any) => {
    const partePrincipale = row.ricorso?.detParti;
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {partePrincipale}
          </Typography>
          <NsButton
            sx={theme.custom.secondaryButton}
            onClick={() => handleModal('parti', row)}
          >
            {t('common.vedi')}
          </NsButton>
        </Box>
      </TableCell>
    );
  };
  const renderNrg = (cell: any, row: any) => {
    const handleClickVediRiuniti = () => handleModal('vediRiuniti', row);

    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
      <Box sx={{ display: 'flex', alignItems: 'center' }} className={'ns-display-2col'}>
        <Box>
          <Typography variant="body2" color="text.primary">
            {row.ricorso?.numero + '/' + row.ricorso?.anno}
          </Typography>
        </Box>
        <Box>
          {row.checkStatoOnSIC?.isPrincipalRicorsoRiunito && (
            <NsButton
              onClick={handleClickVediRiuniti}
              sx={theme.custom.secondaryButton}
            >
              {t('common.principale')}
            </NsButton>
          )}
        </Box>
      </Box>
      </TableCell>
    );
  };
  const renderValPonderale = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Typography variant="body2" color="text.primary">
          {row.ricorso?.spoglio?.valPond}
        </Typography>
      </TableCell>
    );
  };

  const renderReato = (cell: any, row: any) => {
    const reato = row.ricorso.reatiRicorso.find(
      (reato: any) => reato?.principale == true
    )?.reato.displayReati;
    const reati = row.ricorso.reatiRicorso.map((reato: any) => {
      return (
        reato?.reato?.displayReati + (reato?.principale ? ' Principale' : '')
      );
    });
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {reato}
          </Typography>
          {reati.length > 1 && (
            <NsButton sx={theme.custom.secondaryButton}>
              {t('common.vedi')}
            </NsButton>
          )}
        </Box>
      </TableCell>
    );
  };

  const renderStato = (cell: any, row: any) => {
      const res = row;
      const clickableStyle = { cursor: 'pointer', textDecoration: 'underline' };
      const clickableStyle2 = { textDecoration: 'none' };
      const handleClick = () => handleModal('stato', res);

      const getStateContent = (state: string, styleUnlessUnderline?: boolean) => {
        const isNoLink = [
          'RIUNITO',
          'DA_REDIGERE',
          'RIUNITO_CARTACEO',
          'REDAZIONE_ESTENSORE'
        ].includes(state);

        return (
          <TableCell
            key={cell.id}
            align={cell.align}
            sx={{ border: theme.custom.borders[0] }}
          >
          <Box
            display="flex"
            alignItems="center"
            onClick={handleClick}
            sx={isNoLink || styleUnlessUnderline ? clickableStyle2 : clickableStyle}
          >
            <Typography variant="body2" color="text.primary">
              {getStateNames('RELATORE', state)}
            </Typography>
          </Box>
          </TableCell>
        );
      };

      if (
        res.esitoParziale?.esitoParziale &&
        res.checkStatoOnSIC?.statoProvvedimento !== StatoProvvedimentiEnum.RIUNITO
      ) {
        return  <TableCell
          key={cell.id}
          align={cell.align}
          sx={{ border: theme.custom.borders[0] }}
        > <Box display="flex">-</Box></TableCell>;
      }

      if (
        !res.isEstensore &&
        res.checkStatoOnSIC?.statoProvvedimento !== StatoProvvedimentiEnum.RIUNITO
      ) {
        res.stato = 'REDAZIONE_ESTENSORE';
      }

      const { checkStatoOnSIC, provvedimento } = res;
      const stato = provvedimento?.stato || 'DA_REDIGERE';
      const statoProvvedimento = checkStatoOnSIC?.ricorsoRiunito
        ? StatoProvvedimentiEnum.RIUNITO
        : checkStatoOnSIC?.statoProvvedimento;

      if (
        [
          StatiSic.pubblicatoSic,
          StatiSic.minutaDepositataSic,
          StatiSic.provvedimentoDepositatoSic,
        ].includes(statoProvvedimento)
      ) {
        return getStateContent(statoProvvedimento);
      }

      if (
        stato === StatoProvvedimentiEnum.BOZZA &&
        (provvedimento.isRevisione ||
          provvedimento?.changeStatus?.prevStato === 'MINUTA_MODIFICATA_PRESIDENTE')
      ) {
        return getStateContent('MINUTA_IN_REVISIONE');
      }

      if (stato === 'BUSTA_RIFIUTATA') {
        return (
          <TableCell
            key={cell.id}
            align={cell.align}
            sx={{ border: theme.custom.borders[0] }}
          >
          <Box
            display="flex"
            alignItems="center"
            onClick={handleClick}
            sx={clickableStyle}
          >
            <Typography variant="body2" color="text.primary">
              {provvedimento?.changeStatus?.prevStato === 'INVIATO_IN_CANCEL_PRESIDENTE'
                ? 'Busta rifiutata al Presidente'
                : getStateNames('RELATORE', stato)}
            </Typography>
          </Box>
          </TableCell>
        );
      }

      if (
        [
          StatoProvvedimentiEnum.RIUNITO,
          StatoProvvedimentiEnum.RIUNITO_CARTACEO,
        ].includes(statoProvvedimento)
      ) {
        return getStateContent(statoProvvedimento, true);
      }

      const state = stato || 'DA_REDIGERE';
      return getStateContent(state, state == 'DA_REDIGERE');
  };

  const renderOscuramento = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.oscuratoSicComplessivo == true ? 'SI' : 'NO'}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderSemplificata = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.esito?.semplificata == '1' ? 'SI' : 'NO'}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const deleteProvvedimento = async (idProvv: number, idRicudien: string) => {
    setIsLoading(true);
    await axios
      .delete(servizi + '/provvedimento/deleteProvvedimento/' + idProvv)
      .then((response: any) => {
        notify({
          message: t('fascicolo.provvedimentiTable.provvedimentoEliminato'),
          type: 'success',
        });
        setData((rows: any) =>
          rows.map((row: any) => {
            if (row.idProvvedimento === idProvv) {
              return {
                ...row,
                stato: 'DA_REDIGERE',
                file: null,
                progress: 0,
                idProvvedimento: null,
              };
            }
            return row;
          })
        );
        updateElementById('none', `linearProgress-${idRicudien}`);
      })
      .catch((err: any) => {
        notify({
          message: 'Erore',
          type: 'error',
        });
      })
      .finally(() => {
        updateElementById('block', `upload-icon-${idRicudien}`);
        setIsLoading(false);
      });
  };

  const renderImporta = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0], cursor: 'pointer' }}
      >
        <input
          type="file"
          accept="application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          id={`file-${row.idRicudien}`}
          style={{ display: 'none' }}
          onChange={(e) => handleFileChange(e, row)}
          onClick={(event: any) => (event.target.value = null)}
        />
        <Box
          sx={{
            width: '150px',
          }}
        >
          {row.file ? (
            <Box display="flex">
              <NsTooltip
                title={row?.file?.name}
                icon={
                  <Box
                    sx={{
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                    }}
                    width={'80px'}
                  >
                    {row?.file?.name}
                  </Box>
                }
              />

              <DeleteOutlineIcon
                fontSize="small"
                onClick={() =>
                  deleteProvvedimento(row?.idProvvedimento, row.idRicudien)
                }
              />
              <DownloadIcon
                fontSize="small"
                onClick={() => downloadFile(row)}
              />
            </Box>
          ) : (
            <NsTooltip
              title="E' possibile importare solo file PDF o DOCX"
            >
              <FileUploadIcon
                onClick={() =>
                  document
                    ?.getElementById(`file-${row.idRicudien}`)
                    ?.click?.()
                }
                id={`upload-icon-${row.idRicudien}`}
                sx={{ marginLeft: '30px' }}
              />
            </NsTooltip>
          )}
          <LinearProgress
            variant="determinate"
            color={row.uploadError ? 'error' : 'primary'}
            value={row?.progress ?? 0}
            id={`linearProgress-${row.idRicudien}`}
            sx={{ display: 'none' }}
          />
        </Box>
      </TableCell>
    );
  };

  const downloadFile = (row: any) => {
    if (row.file) {
      const url = URL.createObjectURL(row.file);
      const a = document.createElement('a');
      a.href = url;
      a.download = row.file.name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const columns: Column[] = [
    {
      id: 'nrg',
      align: 'left',
      label: t('calendario.nrg') as string,
      minWidth: 170,
      render: renderNrg,
    },
    {
      id: 'parti',
      minWidth: 170,
      label: t('calendario.parti') as string,
      align: 'left',
      render: renderParti,
    },
    {
      id: 'reato',
      minWidth: 170,
      label: t('calendario.reato') as string,
      align: 'left',
      render: renderReato,
    },
    {
      id: 'valPond',
      minWidth: 170,
      label: t('calendario.valorePonderale') as string,
      align: 'left',
      render: renderValPonderale
    },
    {
      id: 'stato',
      minWidth: 170,
      label: t('calendario.stato') as string,
      align: 'left',
      render: renderStato,
    },
    {
      id: 'oscuramento',
      minWidth: 170,
      label: t('calendario.oscuramentoSic') as string,
      align: 'left',
      render: renderOscuramento,
    },
    {
      id: 'tipologia',
      minWidth: 170,
      label: t('calendario.tipologia') as string,
      align: 'left',
    },
    {
      id: 'semplificata',
      minWidth: 170,
      label: t('calendario.semplificata') as string,
      align: 'left',
      render: renderSemplificata,
    },
    {
      id: 'azioni',
      minWidth: 170,
      label: 'Importa',
      align: 'left',
      render: renderImporta,
    },
  ];

  return (
    <>
      <NsFullPageSpinner isOpen={isLoading} value={1} />
      <DialogModal {...modalProps} />

      <RelayTable rows={data} columns={columns} />
    </>
  );
}
