import { useTranslation } from 'react-i18next';
import RelayTable from '../shared/RelayTable';
import { Column } from 'src/interfaces';
import { Box, Grid, TableCell, Typography, useTheme } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';

export default function VisualTable({ provvedimenti }: any) {
  const { t } = useTranslation();
  const theme: any = useTheme();

  const renderParti = (cell: any, row: any) => {
    const partePrincipale = row.ricorso?.detParti;
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {partePrincipale}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderReato = (cell: any, row: any) => {
    const reato = row.ricorso.reatiRicorso.find(
      (reato: any) => reato?.principale == true
    )?.reato.displayReati;
    const reati = row.ricorso.reatiRicorso.map((reato: any) => {
      return (
        reato?.reato?.displayReati + (reato?.principale ? ' Principale' : '')
      );
    });
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {reato}
          </Typography>
          {reati.length > 1 && (
            <NsButton sx={theme.custom.secondaryButton}>
              {t('common.vedi')}
            </NsButton>
          )}
        </Box>
      </TableCell>
    );
  };

  const renderDocument = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.file.name}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const columns: Column[] = [
    {
      id: 'nrg',
      align: 'left',
      label: t('calendario.nrg') as string,
      minWidth: 170,
    },
    {
      id: 'parti',
      minWidth: 170,
      label: t('calendario.parti') as string,
      align: 'left',
      render: renderParti,
    },
    {
      id: 'reato',
      minWidth: 170,
      label: t('calendario.reato') as string,
      align: 'left',
      render: renderReato,
    },
    {
      id: 'tipologia',
      minWidth: 170,
      label: t('calendario.tipologia') as string,
      align: 'left',
    },
    {
      id: 'document',
      minWidth: 170,
      label: 'Documento',
      align: 'left',
      render: renderDocument,
    },
  ];

  return (
    <Grid mt={5} xs={12}>
      <RelayTable rows={provvedimenti} columns={columns}/>
    </Grid>
  );
}
