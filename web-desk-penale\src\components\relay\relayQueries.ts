import { graphql } from 'relay-runtime';

export const DatiGeneraliUdienzaSchema = graphql`
  query relayQueries_DatiGeneraliUdienzaQuery($id: Float!) {
    udienza(id: $id) {
      dataUdienza
      idUdien
      termineDeposito
      sezione {
        descrizione
        sigla
      }
      tipoUdienza {
        sigla
        descrizione
      }
      aula {
        descrizione
        sigla
      }
      collegio {
        tipoMag
        magistrato {
          anagraficaMagistrato {
            cognome
            nome
            dataNascita
          }
          grado
        }
        gradoMag
      }
    }
    getFascitoloDetExtraInfo(idUdien: $id){
      totalCount
      valorePonderaleTotale
    }
  }
`;

export const ProvvedimentoNoteSchema = graphql`
  query relayQueries_ProvvedimentoNoteQuery($id: String!) {
    provvedimentoNote(id: $id) {
      autore {
        nome
        cognome
      }
      dataInserimento
      note
      statoInserimento
    }
  }
`;
export const ricorsiUdienzaPaginato = graphql`
  query relayQueries_ricorsiUdienzaPaginatoQuery($idUdienza: Float!, $after: String, $last: Int,  $first: Int, $before: String, $status: ProvvedimentiStatoEnum!) {
    
ricorsiUdienza(idUdien: $idUdienza, after: $after,  first: $first,  last: $last, before: $before, status: $status) {
   edges {
      node {
      idRicudien
      isRelatore
      isEstensore
      isPresidente
      principale
      checkStatoOnSIC {
        nrg
        numRaccoltaGenerale
        numRaccoltaGeneraleString
        dataMinuta
        dataPubblicazione
        idUdienza
        statoProvvedimento
        isPrincipalRicorsoRiunito
        ricorsoRiunito {
          anno
          numero
        }
      }
      esito {
        semplificata
      }
      oscuratoSicComplessivo
      oscuratoSicSingle
      tipologia
      numOrdine
      provvedimento {
        idProvvedimento
        idUdienza
        nrg
        nomeDocumento
        tipo
        stato
        enabledRichiestaDiModificaEVerificato
        isRevisione
        changeStatus {
          idProvvedimentoChangeStatus
          dateChange
          idAutore
          prevStato
          stato
        }
      }
      esitoParziale {
        motivo
        esitoParziale
      }
      ricorso {
        detParti
        reatiRicorso {
          idReato
          principale
          dataA
          dataDa
          reato {
            idReato
            descrizione
            displayReati
            art
          }
        }
        parti {
          idParte
          ricorrente
          anagraficaParte {
            nome
            cognome
            codiceFiscale
            dataNascita
          }
          difensori {
            nrg
            operatore
            indirizzo
            comune
            idAnagraficaDifensore
            difensoreAnagrafica {
              codiceFiscale
              nome
              cognome
            }
            tipoDifensore {
              descrizione
            }
          }
        }
        note
        nrg
        anno
        numero
        spoglio {
          pgSuper
          nrg
          valPond
        }
      }
    }
    }
 pageInfo {
      endCursor
      hasNextPage
      startCursor
    }
  }
  }
`;

export const NotificheSchema = graphql`
  query relayQueries_NotificheQuery(
    $after: String
    $before: String
    $first: Int
    $last: Int
    $term: String
  ) {
    notificheByCurrentUser(
      first: $first
      after: $after
      before: $before
      last: $last
      term: $term
    ) {
      aggregate {
        count
        total
        unread
      }
      edges {
        cursor
        node {
          idNotifica
          descrizione
          tipo
          tipoProvvedimento
          read
          nrg
          annoFascicolo
          numeroFascicolo
          idUdienza
          dataCreazione
          idUtente
          coleggio {
            descrizione
          }
          sezione {
            descrizione
          }
          tipoUdienza {
            descrizione
          }
          dataUdienza
          inizioUdienza
          fineUdienza
          isEstensore
          isPrincipale
        }
      }
    }
  }
`;

export const NotificheSchemaPopover = graphql`
  query relayQueries_NotifichePopoverQuery(
    $after: String
    $before: String
    $first: Int
    $last: Int
    $term: String
  ) {
    notificheByCurrentUserPopover(
      first: $first
      after: $after
      before: $before
      last: $last
      term: $term
    ) {
      aggregate {
        count
        total
        unread
        totalElement
      }
      edges {
        cursor
        node {
          idNotifica
          descrizione
          tipo
          tipoProvvedimento
          read
          nrg
          annoFascicolo
          numeroFascicolo
          idUdienza
          dataCreazione
          idUtente
          coleggio {
            descrizione
          }
          sezione {
            descrizione
          }
          tipoUdienza {
            descrizione
          }
          dataUdienza
          inizioUdienza
          fineUdienza
        }
      }
    }
  }
`;

export const DatiPartiSchema = graphql`
  query relayQueries_DatiPartiQuery($id: Float!) {
    ricorso(id: $id) {
      listaParti {
        parte {
          tipoLegame
          displayParti
          anagraficaParte {
            nome
            cognome
          }
          difensori {
            tipoDifensore {
              descrizione
            }
            difensoreAnagrafica {
              nome
              cognome
            }
          }
          parteLegata {
            tipoLegame {
              descrizione
            }
            anagraficaParte {
              nome
              cognome
            }
          }
        }
        controParteList {
          parte {
            displayParti
            anagraficaParte {
              nome
              cognome
            }
            difensori {
              deceduto
              tipoDifensore {
                descrizione
              }
              difensoreAnagrafica {
                nome
                cognome
              }
            }
            parteLegata {
              tipoLegame {
                descrizione
              }
              anagraficaParte {
                nome
                cognome
              }
            }
          }
          controParte {
            displayParti
            anagraficaParte {
              nome
              cognome
            }
          }
        }
      }
    }
  }
`;


export const MainCalendarSchema = graphql`
  query relayQueries_MainCalendarQuery {
    udienze {
      termineDeposito
      aula {
        descrizione
      }
      allPubblicate
      isEstensore
      inizioUdienza
      fineUdienza
      idUdien
      dataUdienza
      tipoUdienza {
        sigla
        descrizione
      }
      sezione {
        sigla
        descrizione
      }
    }
  }
`;


// export const PenaleUdienzaWithStatoFascicolo = graphql`
//   query relayQueries_PenaleUdienzaWithStatoFascicoloQuery($idUdienza: Float!, $nrg: Float!) {
//     penaleUdienzaByIdUdienza(idUdienza: $idUdienza) {
//       idUdienza
//       dataUdienza
//       tipoUdienza
//       sezione
//       aula
//       operatore
//       idFunzione
//       oggi
//       inizioUdienza
//       fineUdienza
//       checkStatoOnSIC(idUdien: $idUdienza, nrg: $nrg){
//         nrg
//         numRaccoltaGenerale
//         dataMinuta
//         dataPubblicazione
//         idUdienza
//         statoProvvedimento
//       }
//     }
//   }
// `;

export const EsitoParzialeSchema = graphql`
  query relayQueries_EsitoParzialeQuery($ricorsoUdienza: Float!) {
    esitoParziale(ricorsoUdienza: $ricorsoUdienza) {
      descrizione
      motivo
    }
  }
`;


export const DetailStatoSchema = graphql`
  query relayQueries_DetailStatoQuery($id: String!, $roles: String!) {
    provvedimentoChangeStatusByIdProvvedimento(id: $id, roles: $roles) {
      idProvvedimentoChangeStatus
      dateChange
      idAutore
      idProvvedimento
      prevStato
      stato
      isRevisione
    }
  }
`;

export const TrackingStatoSchema = graphql`
  query relayQueries_TrackingStatoQuery($id: String!, $roles: String!) {
    provvedimentoTrackingByIdProvvedimento(id: $id, roles: $roles) {
      idProvvedimentoChangeStatus
      dateChange
      idAutore
      idProvvedimento
      prevStato
      stato
      isRevisione
    }
    checkStatoOnSICQuery(idProvv:$id){
      nrg
      numRaccoltaGenerale
      numRaccoltaGeneraleString
      dataMinuta
      dataPubblicazione
      idUdienza
      statoProvvedimento
    }
  }
`;

export const PenaleCollegioDetailsSchema = graphql`
  query relayQueries_PenaleCollegioDetailsQuery(
    $idUdienza: Float!
    $nrg: Float!
  ) {
    colleggioDetails(id: $idUdienza, nrg: $nrg) {
      colleggioMagistrati {
        tipoMag
        isRelatore
        isEstensore
        magistrato {
          idMagis
          anagraficaMagistrato {
            idAnagmagis
            nome
            cognome
            codiceFiscale
          }
        }
      }
      relatore {
        idMagis
        tipoMag {
          sigla
        }
        anagraficaMagistrato {
          idAnagmagis
          nome
          cognome
          codiceFiscale
        }
      }
    }
  }
`;

export const PenaleCollegioDetailsByOrdineSchema = graphql`
  query relayQueries_PenaleCollegioDetailsByOrdineQuery(
    $idUdienza: Float!
    $nrg: Float!
  ) {
    colleggioDetails(id: $idUdienza, nrg: $nrg) {
      colleggioMagistrati {
        tipoMag
        isRelatore
        isEstensore
        magistrato {
          idMagis
          anagraficaMagistrato {
            idAnagmagis
            nome
            cognome
          }
        }
      }
      relatore {
        idMagis
        tipoMag {
          sigla
        }
        anagraficaMagistrato {
          idAnagmagis
          nome
          cognome
        }
      }
    }
  }
`;

export const CodaDepositoSchema = graphql`
  query relayQueries_CodaDepositoSchemaQuery($role: String!) {
    codeDepositoByCf(ruolo: $role) {
      idProvv
      cf
      codeDepositoDto {
        tipoProvvedimento
        dataUdienza
        nrg
        numOrdine
        tipoProvvedimento
        udienza
      }
    }
  }
`;

export const SettingsQuery = graphql`
  query relayQueries_SettingsQuery {
    impostazioniByCf {
      cfUtente
      usernameFirma
      passwordFirma
    }
  }
`;

export const CreateSettingsMutation = graphql`
  mutation relayQueries_CreaSettingsMutation($settingsInput: SettingsInput!) {
    creaSettings(settings: $settingsInput) {
      cfUtente
      passwordFirma
      usernameFirma
    }
  }
`;

export const UpdateSettingsMutation = graphql`
  mutation relayQueries_UpdateSettingsMutation($settingsInput: SettingsInput!) {
    updateSetting(settings: $settingsInput) {
      cfUtente
      passwordFirma
      usernameFirma
    }
  }
`;

export const DeleteSettingMutation = graphql`
  mutation relayQueries_DeleteSettingMutation {
    deleteSetting
  }
`;

export const CodaDeleteDepositoMutation = graphql`
  mutation relayQueries_CodaDeleteDepositoMutation($input: String!) {
    eliminaCodeDeposito(idProvv: $input)
  }
`;

export const ReadNotify = graphql`
  mutation relayQueries_ReadNotificaMutation(
    $segnaLette: NotificheToReadInput!
  ) {
    updateReadNotifyList(segnaLette: $segnaLette)
  }
`;
