import { graphql } from 'relay-runtime';

// deve iniziare con il nome del file e finire con query
export const FascicoloPresidenteDetailsSchema = graphql`
  query relayQueriesFascicoloPresidenteDetails_FascicoloPresidenteDetailsQuery($idUdin: Float!, $nrg: Float!) {
  udienzeWithProvvedimentoDet(idUdien: $idUdin, nrg: $nrg) {
      dataUdienza
      idUdien
      termineDeposito
      sezione {
        descrizione
        sigla
      }
      tipoUdienza {
        descrizione
        sigla
      }
      aula {
        descrizione
        sigla
      }
      ricorsiUdienza(nrg: $nrg) {
        idRicudien
        statoProvvedimento
        relatore {
          anagraficaMagistrato {
            codiceFiscale
            nome
            cognome
          }
        }
        estensore {
          anagraficaMagistrato {
            codiceFiscale
            nome
            cognome
          }
        }
        isEstensore
        isPresidente
        isRelatore
        oscuratoSicComplessivo
        oscuratoSicSingle
        valPondComplessivo
        oscuramentoDeskCsp
        numOrdine
        esitoParziale {
          motivo
          esitoParziale
        }
        ricorso {
          tipoRicorso {
            descrizione
            sigla
          }
        detParti
        provvedimentoImpugnato
        dataIscrizione
        provvedimento(nrg: $nrg, idUdien: $idUdin){
          idProvvedimento
        }
          reatiRicorso {
            idReato
            principale
            dataA
            dataDa
            reato {
              displayReati
              idReato
              descrizione
              art
            }
          }
          parti {
            idParte
            ricorrente
            anagraficaParte {
              nome
              cognome
              codiceFiscale
              dataNascita
            }
            difensori {
              nrg
              operatore
              indirizzo
              comune
              idAnagraficaDifensore
              difensoreAnagrafica {
                codiceFiscale
                nome
                cognome
              }
              tipoDifensore {
                descrizione
              }
            }
          }
          note
          nrg
          anno
          numero
          spoglio {
            pgSuper
            nrg
            valPond
          }
        }
      }
    checkStatoOnSIC(idUdien: $idUdin, nrg: $nrg){
      nrg
      numRaccoltaGenerale
      numRaccoltaGeneraleString
      dataMinuta
      dataPubblicazione
      idUdienza
      statoProvvedimento 
      isPrincipalRicorsoRiunito
      ricorsoRiunito{
        anno
        numero
      }
    }
    provvedimentoByNrgPerPresidente(idUdien: $idUdin, nrg: $nrg) {
      enabledRichiestaDiModificaEVerificato
      idProvvedimento
      dataDeposito
      dataUltimaModifica
      dataDecisione
      nomeDocumento
      origine
      stato
      nrg
      tipo
      hasNote
      autore {
        cognome
        nome
        profilo {
          descrizioneProfilo
          operatore
        }
        codiceFiscale
      }
      listaFile {
        oscurato
        nomeFile
        tipoFile
        idCategoria
        tipoFile
      }
    }
  }
}
`;
