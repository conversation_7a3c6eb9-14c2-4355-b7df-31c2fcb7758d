import { graphql } from 'relay-runtime';

export const OrigineProvvedimentoSchema = graphql`
    query relayQueriesOrigineProvvedimentoQuery($nrg: Float!) {
        provvedimentoByNrgPerPresidente(nrg: $nrg) {
            idProvvedimento,
            origine,
            enabledRichiestaDiModificaEVerificato
        }
    }
`;

export const GetProvvedimentiByIdsSchema = graphql`
    query relayQueriesOrigineProvvedimentoGetProvvedimentiByIdsQuery($ids: [String!]!) {
        provvedimentiByIds(ids: $ids) {
            idProvvedimento
            stato
            tipo
            enabledRichiestaDiModificaEVerificato
        }
    }
`;