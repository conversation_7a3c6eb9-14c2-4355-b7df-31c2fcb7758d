import { graphql } from 'relay-runtime';

// deve iniziare con il nome del file e finire con query
//TODO DA rifare il servizio usare il servizio ricorsi
export const MinimalRicorsoDetailSchema = graphql`
  query relayQueriesRicorsoDetails_MinimalRicorsoDetailsQuery($idUdien: Float!, $nrg: Float!) {
    ricorsoByIdUdienAndNrg(idUdien: $idUdien, nrg: $nrg) {
      anno
      numero
      nrg
      provvedimento(idUdien: $idUdien, nrg: $nrg){
        idProvvedimento
        stato
      }
      udienza (idUdien: $idUdien, nrg: $nrg) {
        dataUdienza
        sezione {
          descrizione
          sigla
        }
        tipoUdienza {
          descrizione
          sigla
        }
        aula {
          descrizione
          sigla
        }
      }
    }
  }
`;
