import { graphql } from 'relay-runtime';

export const GetRiunitiDetailsSchema = graphql`
  query relayQueriesRiunitDetails_GetRiunitiDetailsQuery($idRicUdin: Float!) {
    getRiuntiByIdRicUdien(idRicUdien: $idRicUdin) {
      anno
      numero
      nrg
      idRicorsoUdienza
    }
  }
`;
export const GetRiunitiRicorsoUdienzaSchema = graphql`
  query relayQueriesRiunitDetails_GetRiunitiRicorsoUdienzaQuery($idRicUdienList: [Int!]!) {
    ricorsoUdienzaInRicUdien(idRicUdienList: $idRicUdienList) {
      idRicudien
      oscuratoSicComplessivo
      oscuratoSicSingle
      valPondComplessivo
      ricorso {
        nrg,
        anno
        numero,
        tipoRicorso {
          descrizione
        }
        detParti
        reatiRicorso {
          principale
          reato {
            displayReati
          }
        }
        dataIscrizione
        provvedimentoImpugnato
        spoglio {
          valPond
        }
      }
    }
  }
`;
