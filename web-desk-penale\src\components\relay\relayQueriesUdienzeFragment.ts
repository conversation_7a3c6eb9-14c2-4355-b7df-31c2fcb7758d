import { graphql } from 'relay-runtime';

export const CalendarioTableFragment = graphql`
  fragment relayQueriesUdienzeFragment_RicorsiUdienzaPaginata on Query
  @refetchable(queryName: "relayQueriesUdienzeFragment_RicorsiUdienzaPaginataRefetchQuery") {
    ricorsiUdienza(
      idUdien: $idUdienza,
      after: $after,
      first: $first,
      last: $last,
      before: $before,
      status: $status
    ) @connection(key: "relayQueriesUdienzeFragment_ricorsiUdienza") {
      edges {
        node {
          idRicudien
          isRelatore
          isEstensore
          isPresidente
          principale
          checkStatoOnSIC {
            nrg
            numRaccoltaGenerale
            numRaccoltaGeneraleString
            dataMinuta
            dataPubblicazione
            idUdienza
            statoProvvedimento
            isPrincipalRicorsoRiunito
            ricorsoRiunito {
              anno
              numero
            }
          }
          esito {
            semplificata
          }
          oscuratoSicComplessivo
          oscuratoSicSingle
          tipologia
          numOrdine
          provvedimento {
            idProvvedimento
            idUdienza
            nrg
            nomeDocumento
            tipo
            stato
            enabledRichiestaDiModificaEVerificato
            isRevisione
            changeStatus {
              idProvvedimentoChangeStatus
              dateChange
              idAutore
              prevStato
              stato
            }
          }
          esitoParziale {
            motivo
            esitoParziale
          }
          ricorso {
            detParti
            reatiRicorso {
              idReato
              principale
              dataA
              dataDa
              reato {
                idReato
                descrizione
                displayReati
                art
              }
            }
            parti {
              idParte
              ricorrente
              anagraficaParte {
                nome
                cognome
                codiceFiscale
                dataNascita
              }
              difensori {
                nrg
                operatore
                indirizzo
                comune
                idAnagraficaDifensore
                difensoreAnagrafica {
                  codiceFiscale
                  nome
                  cognome
                }
                tipoDifensore {
                  descrizione
                }
              }
            }
            note
            nrg
            anno
            numero
            spoglio {
              pgSuper
              nrg
              valPond
            }
          }
        }
      }
      pageInfo {
        endCursor
        hasNextPage
        startCursor
      }
    }
  }
`;

export const MainQueryCalendarioTable_ricorsiUdienza = graphql`
  query relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery(
    $idUdienza: Float!,
    $after: String,
    $first: Int,
    $before: String,
    $last: Int,
    $status: ProvvedimentiStatoEnum
  ) {
    getFascitoloDetExtraInfo(idUdien: $idUdienza) {
      totalCount
      valorePonderaleTotale
    }
    ...relayQueriesUdienzeFragment_RicorsiUdienzaPaginata
  }
`;
