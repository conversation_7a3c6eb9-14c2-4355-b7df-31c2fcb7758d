import { graphql } from 'relay-runtime';

export const datiProvvedimentoScrivaniaFragment = graphql`
  fragment relayQueryScrivaniaFragment_datiProvvedimentoScrivania on Query
  @refetchable(queryName: "relayQueryScrivaniaFragment_datiProvvedimentoScrivaniaQuery") {
    provvedimentiScrivania(
      idUdienza: $idUdienza
      after: $after
      first: $first
      last: $last
      before: $before
      status: $status
    ) @connection(key: "relayQueryScrivaniaFragment__provvedimentiScrivania") {
      edges {
        node {
          dataDeposito
          nrg
          nrgFormat
          numOrdine
          idProvvedimento
          tipologia
          idRicUdien
          stato
          relatore
          oscuratoSIC
          oscuratoDESKCSP
          riunito {
            idUdienza
            nrg
            isPrincipalRicorsoRiunito
            statoProvvedimento
            dataMinuta
          }
        }
      }
      aggregate {
        count
        total
        totalElement
      }
      pageInfo {
        endCursor
        hasNextPage
        startCursor
      }
    }
  }
`;

export const PenaleUdienza = graphql`
  query relayQueryScrivaniaFragment_PenaleUdienzaQuery(
    $idUdienza: Float!,
    $after: String,
    $first: Int,
    $before: String,
    $last: Int,
    $status: ProvvedimentiStatoEnum
  ) {
    penaleUdienzaByIdUdienza(idUdienza: $idUdienza) {
      idUdienza
      dataUdienza
      tipoUdienza
      sezione
      aula
      operatore
      idFunzione
      oggi
      inizioUdienza
      fineUdienza
    }
    ...relayQueryScrivaniaFragment_datiProvvedimentoScrivania
  }
`;
