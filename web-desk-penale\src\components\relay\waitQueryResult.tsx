import { Box, LinearProgress, Modal, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RenderProps } from 'relay-hooks/lib/RelayHooksTypes';
import { OperationType } from 'relay-runtime';

export function withWait<T extends Partial<RenderProps<OperationType>>>(
  WrappedComponent: React.ComponentType<T>,
  queryProps: RenderProps<OperationType>
) {
  // DisplayName per React Dev Tools.
  const displayName =
    WrappedComponent.displayName ||
    WrappedComponent.name ||
    'MysteryRelayComponent';

  // Componente da renderizzare, in base allo stato della query.
  const ResolvedComponent = (
    props: Omit<T, keyof RenderProps<OperationType>>
  ) => {
    if (queryProps.isLoading) {
      return (
        <Box my={4}>
          <LinearProgress />
        </Box>
      );
    } else if (queryProps.error) {
      return <ErrorModal error={queryProps.error} retry={queryProps.retry} />;
    } else if (queryProps.data) {
      return <WrappedComponent {...queryProps} {...(props as T)} />;
    } else {
      return <Box my={4}>Errore non gestito!</Box>;
    }
  };

  ResolvedComponent.displayName = `withTheme(${displayName})`;

  return ResolvedComponent;
}

interface ErrorModalProps {
  error: Error;
  retry: RenderProps<OperationType>['retry'];
}

const errorStyle = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '50vw',
  bgColor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

const ErrorModal = ({ error, retry }: ErrorModalProps) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(true);
  const handleClose = () => {
    retry();
    setOpen(false);
  };

  const handleCancel = () => {
    setOpen(false);
  };
  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={errorStyle}>
        <Typography id="modal-modal-title" variant="h5" component="h2">
          {t('common.error.title')}
        </Typography>
        <Typography id="modal-modal-title" variant="h6" component="h2">
          {t('common.error.name')} {error.name}
        </Typography>
        <Typography id="modal-modal-description" sx={{ mt: 2 }}>
          {error.message}
        </Typography>
        <Box sx={{ '& button': { m: 0.5 } }} textAlign="center">
          <NsButton size="small" variant="contained" onClick={() => retry()}>
            {t('common.error.retry')}
          </NsButton>

          <NsButton size="small" variant="contained" onClick={handleCancel}>
            {t('common.error.cancel')}
          </NsButton>
        </Box>
      </Box>
    </Modal>
  );
};
