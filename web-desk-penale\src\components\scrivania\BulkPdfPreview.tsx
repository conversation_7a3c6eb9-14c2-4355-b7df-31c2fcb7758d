import { Box, useTheme, Grid } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GetProvvedimentiByIdsSchema } from '../relay/relayQueriesOrigineProvvedimento';
import { useConfig } from '../shared/configuration.context';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { relayQueriesOrigineProvvedimentoGetProvvedimentiByIdsQuery } from '@/generated/relayQueriesOrigineProvvedimentoGetProvvedimentiByIdsQuery.graphql';

const ROOT_QUERY = GetProvvedimentiByIdsSchema;

interface BulkPdfPreviewProps {
  provv: string[];
  refreshPage: () => void;
  refreshTableOnly?: () => void;
  refreshCoda?: boolean;
  setRefreshCoda?: (value: boolean) => void;
}

export default function BulkPdfPreview({
  provv,
  refreshPage,
  refreshTableOnly,
  refreshCoda,
  setRefreshCoda,
}: BulkPdfPreviewProps) {
  const { servizi } = useConfig();
  const { t } = useTranslation();
  const theme: any = useTheme();
  const serviceUrl2 = `${servizi}`;
  const { notify } = useNotifier();
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isLoading, setIsloading] = useState<boolean>(false);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [passedProvv, setPassedProvv] = useState<any[]>([]);
  const [enabledRichiestaDiModificaEVerificato, setEnabledRichiestaDiModificaEVerificato] =
    useState<boolean>(false);
  const [allProvvedimentiVerificabili, setAllProvvedimentiVerificabili] = useState<boolean>(false);

  const { data } = useQuery<relayQueriesOrigineProvvedimentoGetProvvedimentiByIdsQuery>(
    ROOT_QUERY,
    {
      ids: provv,
    },
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  useEffect(() => {
    if (data?.provvedimentiByIds) {
      const currentProvvedimento = data.provvedimentiByIds.find(
        p => p.idProvvedimento === provv[currentIndex]
      );
      setEnabledRichiestaDiModificaEVerificato(
        currentProvvedimento?.enabledRichiestaDiModificaEVerificato || false
      );

      const allVerificabili = data.provvedimentiByIds.every(
        p => p.enabledRichiestaDiModificaEVerificato
      );
      setAllProvvedimentiVerificabili(allVerificabili);
    }
  }, [data, currentIndex, provv]);

  const verificato = () => {
    setIsloading(true);

    // Salva l'indice corrente per usarlo nelle callback
    const currentProvvIndex = currentIndex;
    const currentProvvId = provv[currentProvvIndex];

    console.log(`Verificando provvedimento ${currentProvvId} (indice ${currentProvvIndex})`);

    axios
      .post(serviceUrl2 + '/presidente/verificato/' + currentProvvId + '?disableWarn=true')
      .then((response: any) => {
        notify({
          message:
            'Verifica avvenuta con successo. Il provvedimento è stato verificato ed inserito in "Coda di deposito"',
          type: 'success',
        });

        console.log(`Verifica completata per ${currentProvvId}`);

        // Aggiorna sempre la tabella, indipendentemente se è l'ultimo elemento o no
        if (refreshTableOnly) {
          console.log("Chiamando refreshTableOnly...");
          refreshTableOnly();
        }

        // Sposto all'elemento successivo se c'è un altro elemento, altrimenti chiudi la preview
        if (currentProvvIndex < provv.length - 1) {
          console.log(`Passando al prossimo elemento (${currentProvvIndex + 1}/${provv.length - 1})`);
          setCurrentIndex(currentProvvIndex + 1);
        } else {
          // Questo è l'ultimo elemento, quindi chiudi la preview
          console.log("Ultimo elemento, chiudendo la preview");
          refreshPage();
        }

        if (setRefreshCoda) setRefreshCoda(!refreshCoda);
      })
      .catch((error: any) => {
        console.log('error', error);
        // In caso di errore, prova comunque ad aggiornare la tabella
        if (refreshTableOnly) {
          refreshTableOnly();
        } else {
          refreshPage();
        }
        if (setRefreshCoda) setRefreshCoda(!refreshCoda);
      })
      .finally(() => {
        setIsloading(false);
      });
  };

  const verificaTutti = () => {
    setIsloading(true);
    console.log(`Verificando tutti i provvedimenti: ${provv.join(', ')}`);

    axios
      .post(serviceUrl2 + '/presidente/verificaTutti', provv)
      .then((response: any) => {
        notify({
          message:
            'Verifica avvenuta con successo. I provvedimenti sono stati verificati ed inseriti in "Coda di deposito"',
          type: 'success',
        });

        console.log("Verifica di tutti i provvedimenti completata");

        // Prima aggiorna la tabella, poi chiudi la preview
        if (refreshTableOnly) {
          console.log("Aggiornamento tabella prima di chiudere la preview");
          refreshTableOnly();
        }

        // Chiudi la preview dopo un breve ritardo per assicurarsi che la tabella sia aggiornata
        setTimeout(() => {
          console.log("Chiusura preview dopo verifica tutti");
          refreshPage();
        }, 300);

        if (setRefreshCoda) setRefreshCoda(!refreshCoda);
      })
      .catch((error: any) => {
        console.log('error', error);
        notify({
          message: 'Errore nella verifica dei provvedimenti',
          type: 'error',
        });
        // Prova a aggiornare la pagina per assicurarsi che l'interfaccia sia coerente
        refreshPage();
      })
      .finally(() => {
        setIsloading(false);
      });
  };

  useEffect(() => {
    console.log('provv', provv);
    console.log('currentIndex', currentIndex);
    axios
      .get(
        serviceUrl2 +
          '/provvedimento/downloadPdfByIdProvv/' +
          provv[currentIndex],
        {
          responseType: 'blob',
        }
      )
      .then((response: any) => {
        console.log('response', response);
        const pdfBlob = new Blob([response.data], {
          type: 'application/' + 'pdf',
        });
        const pdfUrl = window.URL.createObjectURL(pdfBlob);
        setPdfUrl(pdfUrl);
      })
      .catch((error: any) => {
        console.log('error', error);
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      });
  }, [currentIndex, provv]);

  useEffect(() => {
    passedProvv.push(provv[currentIndex]);
    setPassedProvv(passedProvv);
  }, [currentIndex]);

  const handleNext = () => {
    if (currentIndex < provv.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const isPrevDisabled = currentIndex === 0;
  const isNextDisabled = currentIndex === provv.length - 1;

  return (
    <Grid item xs={12}>
      {pdfUrl && (
        <Box width="100%">
          <iframe src={pdfUrl} width="100%" height="800px" />
          <Box display="flex" justifyContent="space-between" mt={2} mb={2}>
            {provv.length > 1 && (
              <>
                <div>
                  <NsTooltip
                    title={t('scrivania.pdfPreview.MinutaPrecedente')}
                    icon={
                      <NsButton
                        variant="contained"
                        color="primary"
                        onClick={handlePrev}
                        disabled={isPrevDisabled}
                      >
                        <KeyboardDoubleArrowLeftIcon sx={{ mr: 1 }} />
                      </NsButton>
                    }
                  />
                </div>
                <div>
                  <NsTooltip
                    title={t('scrivania.pdfPreview.MinutaSuccessiva')}
                    icon={
                      <NsButton
                        variant="contained"
                        color="primary"
                        onClick={handleNext}
                        disabled={isNextDisabled}
                      >
                        <KeyboardDoubleArrowRightIcon sx={{ ml: 1 }} />
                      </NsButton>
                    }
                  />
                </div>
              </>
            )}
          </Box>
          <Box display="flex" justifyContent="space-between" mt={2} mb={2}>
            {allProvvedimentiVerificabili && provv.length > 1 && (
              <NsButton
                variant="contained"
                color="primary"
                onClick={verificaTutti}
              >
                {t('scrivania.pdfPreview.verificatiTutti')}
              </NsButton>
            )}

            {enabledRichiestaDiModificaEVerificato && (
              <NsButton
                variant="contained"
                color="primary"
                onClick={verificato}
              >
                {t('scrivania.pdfPreview.verificato')}
              </NsButton>
            )}
          </Box>
        </Box>
      )}
      {isLoading && <NsFullPageSpinner isOpen={true} value={1} />}
    </Grid>
  );
}
