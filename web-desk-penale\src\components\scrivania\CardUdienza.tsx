import ArticleIcon from '@mui/icons-material/Article';
import BorderColorIcon from '@mui/icons-material/BorderColor';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import EmailIcon from '@mui/icons-material/Email';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import HistoryEduIcon from '@mui/icons-material/HistoryEdu';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import WarningIcon from '@mui/icons-material/Warning';
import { Divider, Grid, Typography, useTheme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Box } from '@mui/system';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CardUdienzaProps } from 'src/interfaces';
import { SezioneDescEnum } from '../shared/Utils';
import RateReviewOutlinedIcon from '@mui/icons-material/RateReviewOutlined';
import { NsTooltip } from '@netservice/astrea-react-ds';

export default function CardUdienza({ record }: CardUdienzaProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const [color, setColor] = useState<any>();
  const router = useRouter();

  useEffect(() => {
    setColor(record.color);
  }, [record]);

  const useStyles = makeStyles({
    card: {
      position: 'relative',
    },
    ribbon: {
      width: 150,
      height: 130,
      overflow: 'hidden',
      position: 'absolute',
      '&::before': {
        position: 'absolute',
        zIndex: -1,
        content: "''",
        display: 'block',
        border:
          record.minutaAccettataModifica > 0
            ? '5px solid #1a5f7a' //AZZURRO PIÙ SCURO
            : '5px solid #4f7c8f', //AZZURRO
        top: 0,
        left: 20,
      },
      '&::after': {
        position: 'absolute',
        zIndex: -1,
        content: "''",
        display: 'block',
        border:
          record.minutaAccettataModifica > 0
            ? '5px solid #1a5f7a' //AZZURRO PIÙ SCURO
            : '5px solid #4f7c8f', //AZZURRO
        bottom: 0,
        right: 0,
      },
      '& span': {
        position: 'absolute',
        display: 'block',
        width: 240,
        padding: '6px 0',
        left: -25,
        top: 30,
        transform: 'rotate(45deg)',
        color: '#FFF',
        textAlign: 'center',
        backgroundColor:
          record.minutaAccettataModifica > 0
            ? '#2c88b1' //AZZURRO PIÙ SCURO CHIARO
            : 'skyblue',
      },
      top: -10,
      right: -10,
    },
    ribbonIliacSmall: {
      width: 150,
      height: 130,
      overflow: 'hidden',
      position: 'absolute',
      '&::before': {
        position: 'absolute',
        zIndex: -1,
        content: "''",
        display: 'block',
        border: '5px solid #1a5f7a',
        top: 0,
        left: 60,
      },
      '&::after': {
        position: 'absolute',
        zIndex: -1,
        content: "''",
        display: 'block',
        border: '5px solid #1a5f7a',
        bottom: 40,
        right: 0,
      },
      '& span': {
        position: 'absolute',
        zIndex: 1,
        display: 'block',
        width: 240,
        padding: '2px 0',
        left: -5,
        top: 20,
        transform: 'rotate(45deg)',
        color: '#FFF',
        textAlign: 'center',
        backgroundColor: '#2c88b1',
      },
      top: -10,
      right: -10,
    },
    ribbonBlueSmall: {
      width: 150,
      height: 150,
      overflow: 'hidden',
      position: 'absolute',
      '&::before': {
        position: 'absolute',
        zIndex: -1,
        content: "''",
        display: 'block',
        border: '5px solid #4f7c8f',
        top: 0,
        left: 15,
      },
      '&::after': {
        position: 'absolute',
        zIndex: -1,
        content: "''",
        display: 'block',
        border: '5px solid #4f7c8f',
        bottom: 15,
        right: 0,
      },
      '& span': {
        position: 'absolute',
        zIndex: 1,
        display: 'block',
        width: 240,
        padding: '2px 0',
        left: -30,
        top: 40,
        transform: 'rotate(45deg)',
        color: '#FFF',
        textAlign: 'center',
        backgroundColor: 'skyblue',
      },
      top: -10,
      right: -10,
    },
    span: {},
  });

  const classes = useStyles();

  // @ts-ignore
  return (
    <Grid
      item
      p={2}
      pl={0}
      xs={12}
      sm={6}
      md={4}
      lg={3}
      mt={2}
      sx={{ cursor: 'pointer' }}
      onClick={() => router.push(`/scrivania/${record.idUdienza}`)}
    >
      <Box
        id="minute-cards"
        border={theme.custom.borders[0]}
        p={2}
        sx={{ color }}
        className={classes.card}
      >
        {record.minutaAccettata > 0 && (
          <>
            <Typography variant="h3" sx={{ marginBottom: '20px' }}>
              {t('scrivania.nuoveMinute')}
            </Typography>
            {record.minutaAccettataModifica > 0 &&
            record.minutaAccettata > record.minutaAccettataModifica ? (
              <div>
                <div className={classes.ribbonIliacSmall}>
                  <NsTooltip
                    title={t('scrivania.tooltip.minutaRevisionata')}
                    icon={<span className={classes.span}>RESTITUITA</span>}
                  />
                </div>
                <div className={classes.ribbonBlueSmall}>
                  <NsTooltip
                    title={t('scrivania.tooltip.minutaNuova')}
                    icon={<span className={classes.span}>NUOVA</span>}
                  />
                </div>
              </div>
            ) : (
              <div>
                <div className={classes.ribbon}>
                  <NsTooltip
                    title={
                      record.minutaAccettataModifica > 0
                        ? t('scrivania.tooltip.minutaRevisionata')
                        : t('scrivania.tooltip.minutaNuova')
                    }
                    icon={
                      <span className={classes.span}>
                        {record.minutaAccettataModifica > 0
                          ? 'RESTITUITA'
                          : 'NUOVA'}
                      </span>
                    }
                  />
                </div>
              </div>
            )}
          </>
        )}
        <Box display="flex" justifyItems="space-between">
          <Typography mr={6} variant="h3">
            {t('scrivania.udienza')} {record.dataUdienza}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: '1em',
            marginBottom: '1em',
          }}
        >
          {(record.sezione == SezioneDescEnum.S1 ||
            record.sezione == SezioneDescEnum.S2 ||
            record.sezione == SezioneDescEnum.S3 ||
            record.sezione == SezioneDescEnum.S4 ||
            record.sezione == SezioneDescEnum.S5 ||
            record.sezione == SezioneDescEnum.S6) /*VERDE ACQUA*/ && (
            <Box
              sx={{
                width: 20,
                height: 20,
                padding: 0.5,
                bgcolor: '#068E87',
                marginRight: 1,
                borderRadius: '50%',
              }}
            />
          )}
          {record.sezione == SezioneDescEnum.S7 /*BLUE*/ && (
            <Box
              sx={{
                width: 20,
                height: 20,
                padding: 0.5,
                bgcolor: '#2852B5',
                marginRight: 1,
                borderRadius: '50%',
              }}
            />
          )}
          {record.sezione == SezioneDescEnum.SU /*LILLA*/ && (
            <Box
              sx={{
                width: 20,
                height: 20,
                padding: 0.5,
                bgcolor: '#5D2566',
                marginRight: 1,
                borderRadius: '50%',
              }}
            />
          )}
          <Typography
            variant="h4"
            mb={2}
            sx={{ textAlign: 'center', marginBottom: 0 }}
          >
            {record.sezione}&nbsp; | &nbsp;{record.tipo}&nbsp; | &nbsp;
            {t('scrivania.collegio')} {record.aula}
          </Typography>
        </Box>

        <Box display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <FileCopyIcon fontSize="small" />
            <Box ml={0.5}>{t('scrivania.ricorsiTotali')}</Box>
          </Box>
          <Box>{record.ricorsiTotali}</Box>
        </Box>
        <Box display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <FileCopyIcon sx={{ color: 'gray' }} fontSize="small" />
            <Box ml={0.5}>{t('scrivania.riuniti')}</Box>
          </Box>
          <Box>{record?.riuniti}</Box>
        </Box>
        <Box display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <HistoryEduIcon />
            <Box ml={0.5}>{t('scrivania.minuteCartacee')}</Box>
          </Box>
          <Box>{record.minutePervenuteSic}</Box>
        </Box>
        <Box mb={3} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <CheckCircleIcon fontSize="small" />
            <Box ml={0.5}>{t('scrivania.totaleProvvPubb')}</Box>
          </Box>
          <Box>{record.pubblicatiTotali}</Box>
        </Box>
        <Divider />
        <Typography variant="h3" sx={{ marginTop: 2 }}>
          {t('scrivania.datiTelematici')}
        </Typography>
        <Box mt={3} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <EmailIcon fontSize="small" />
            <Box ml={0.5} fontWeight={record.minutaAccettata > 0 ? 'bold' : ''}>
              {t('scrivania.minuteTelNuove')}
            </Box>
          </Box>
          <Box>{record.minutaAccettata}</Box>
        </Box>
        <Box mt={1} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <BorderColorIcon fontSize="small" />
            <Box ml={0.5}>{t('scrivania.richiestaModifica')}</Box>
          </Box>
          <Box>{record.richiestaModifica}</Box>
        </Box>
        <Box mt={1} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <RateReviewOutlinedIcon fontSize="small" />
            <Box
              ml={0.5}
              sx={{ fontWeight: record.bozzaPresidente ? 'bold' : '' }}
            >
              {t('scrivania.bozzaMinutamodificata')}
            </Box>
          </Box>
          <Box sx={{ fontWeight: record.bozzaPresidente ? 'bold' : '' }}>
            {record.bozzaPresidente}
          </Box>
        </Box>
        <Box mt={1} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <RateReviewOutlinedIcon sx={{ color: 'gray' }} fontSize="small" />
            <Box ml={0.5}>
              {t('scrivania.minutaModificataInoltrataEstensore')}
            </Box>
          </Box>
          <Box> {record.bozzaMinutaModificata}</Box>
        </Box>
        <Box mt={1} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <ArticleIcon fontSize="small" />
            <Box ml={0.5}>
              {t('scrivania.provvCodaDeposito')}&nbsp;&nbsp;&nbsp;
            </Box>
          </Box>
          <Box>{record.lavorati}</Box>
        </Box>
        <Box mt={1} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <InsertDriveFileIcon fontSize="small" />
            <Box ml={0.5}>
              {t('scrivania.provvInviatiCancelleria')}&nbsp;&nbsp;&nbsp;
            </Box>
            {record.bustaRifiutata > 0 && (
              <NsTooltip
                title="Buste rifiutate"
                icon={<WarningIcon style={{ color: 'orange' }} />}
              />
            )}
          </Box>
          <Box>{record.firmati}</Box>
        </Box>
        <Box mt={1} mb={5} display="flex" justifyContent="space-between">
          <Box display="flex" justifyItems="center">
            <CheckCircleIcon fontSize="small" />
            <Box ml={0.5}>{t('scrivania.provvPubblicati')}</Box>
          </Box>
          <Box>{record.pubblicati}</Box>
        </Box>
      </Box>
    </Grid>
  );
}
