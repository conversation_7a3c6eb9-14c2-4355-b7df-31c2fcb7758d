import FileCopyIcon from '@mui/icons-material/FileCopy';
import { Box, Grid, Typography } from '@mui/material';
import {useTranslation} from "react-i18next";

const button = {
    background: '#CEE7C3',
    display: 'flex',
    justifyContent: 'center',
    color: '#649E0E',
    cursor: 'pointer',
    width: '100%',
    marginTop: '10px',
};
export default function FilesUdienza() {
    const { t } = useTranslation();
    return (
        <Grid item mt={3} xs={6} md={2}>
            <Box width="120px">
                <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height={150}
                    sx={{
                        background: '#F2F2F2',
                        borderRadius: '5px',
                        boxShadow:
                            '1px 0px 15px rgba(127, 127, 127, 0.34901960784313724)',
                    }}
                >
                    <FileCopyIcon sx={{ fontSize: '70px' }} />
                </Box>
                <Typography mt={2} variant="h3">
                    Udienza 14.03.23
                </Typography>
                <Typography variant="h5">Sezione VI - PU - Aula D</Typography>
                <Typography sx={button}>{t('scrivania.filesUdienza.apri')}</Typography>
            </Box>
        </Grid>
    );
}
