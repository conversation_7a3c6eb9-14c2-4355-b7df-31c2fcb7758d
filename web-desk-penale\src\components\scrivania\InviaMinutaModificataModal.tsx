import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { Grid, TextareaAutosize, Typography, useTheme } from '@mui/material';
import { NsButton, useNotifier } from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useTranslation } from 'next-i18next';
import router from 'next/router';
import { useState } from 'react';
import { useConfig } from '../shared/configuration.context';

export default function InviaMinutaModificataModal({ provv }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();

  const [inviata, setInviata] = useState<any>(false);

  const { servizi } = useConfig();

  const { notify } = useNotifier();

  const serviceUrl = `${servizi}`;

  const [note, setNote] = useState<string | null>(null);

  const { idUdienza } = router.query;

  // hook per disattivare/attivare bottone di invio minuta modificata.
  const [disabled, setDisabled] = useState<boolean>(false);


  console.log('idUdienza', idUdienza);

  const handleRichiediModifica = () => {
    const body = {
      note: note ?? 'modificata dal presidente',
      tipoModifica: 'MINUTA_MODIFICATA_PRESIDENTE',
    };
    axios
      .post(serviceUrl + '/presidente/richiestaModifica/' + provv, { ...body })
      .then((response: any) => {
        setInviata(true);
        notify({
          message: 'Minuta modificata inviata',
          type: 'success',
        });
        router.push('/scrivania/'+idUdienza);
      })
      .catch((error: any) => {
        setInviata(false);
        console.log('error', error);
        notify({
          message: "Errore nell'invio della richiesta",
          type: 'error',
        });
      });
  };

  const handleNoteChange = (event: any) => {
    setNote(event.target.value);
  };

  // funzione per disattivare bottone di invio minuta modificata ed inviare la minuta.
  const disableAndSend = (event: Event) => {
    event.stopPropagation();
    event.preventDefault()
    setDisabled(true);
    handleRichiediModifica();
  }

  if (!inviata) {
    return (
      <Grid container>
        <Grid item xs={12} p={1}>
          <Typography variant="subtitle1" align="left" fontWeight="bold">
            {t(
              'scrivania.inviaMinutaModificataModal.specificheDelleModificheApportate'
            )}
          </Typography>
          <TextareaAutosize
            placeholder="E' possibile inserire ulteriori indicazioni per la modifica apportata"
            value={note as string}
            onChange={handleNoteChange}
            minRows={10}
            style={{
              width: '100%',
              resize: 'none',
              overflow: 'auto',
              height: '300px',
            }}
          />
        </Grid>
        <Grid item xs={12} p={1} container>
          <NsButton
            variant="contained"
            color="primary"
            sx={{ mt: 2, ml: 'auto' }}
            disabled={ disabled }
            onClick={ (event: any)=> { disableAndSend(event) }}
          >
            {t('scrivania.inviaMinutaModificataModal.inviaMinutaModificata')}
          </NsButton>
        </Grid>
      </Grid>
    );
  } else {
    return (
      <Grid container>
        <Grid item xs={12} p={1}>
          <CheckCircleIcon sx={{ fontSize: 40, color: 'green' }} />
          <Typography variant="h2" align="left" fontWeight="bold">
            {t(
              'scrivania.richiediModificaModal.richiestaDiModificaInviataConSuccesso'
            )}
          </Typography>
          <Typography variant="subtitle1" align="left">
            La richiesta è stata inoltrata.
            <NsButton
              variant="text"
              color="primary"
              onClick={() => router.push('/scrivania')}
            >
              {t('scrivania.richiediModificaModal.minutePerUdienza')}
            </NsButton>
          </Typography>
        </Grid>
      </Grid>
    );
  }
}
