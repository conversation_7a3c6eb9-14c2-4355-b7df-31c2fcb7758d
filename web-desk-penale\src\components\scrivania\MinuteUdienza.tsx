import { Box, Grid, Typography, useTheme } from '@mui/material';
import CardUdienza from './CardUdienza';
import axios from 'axios';
import { useConfig } from '../shared/configuration.context';
import React, { use, useEffect, useState, useCallback } from 'react';
import { SezioneDescEnum } from '../shared/Utils';
import { MinuteUdienzaProps } from 'src/interfaces';
import { useTranslation } from 'react-i18next';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import { SezioneEnum } from '../../types/types';

export default function MinuteUdienza({
  filteredResearch,
  dataUdienza,
  sezione,
  tipoUdienza,
  collegio,
  onlyMinuteNew,
  onlyProvvedimentiNew,
}: MinuteUdienzaProps) {
  const theme: any = useTheme();
  const { servizi } = useConfig();
  const serviceUrl = `${servizi}/datiScrivania/dashboard`;
  const serviceUrlByFilter = `${servizi}/datiScrivania/byFilter`;
  const { t } = useTranslation();
  const [minuteNew, setMinuteNew] = useState<boolean>(onlyMinuteNew || false);
  const [allMinute, setAllMinute] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  /*
   * This arrow function check and update sections and use JS trim function to remove
   * whitespace (spaces, tabs, and newlines) from both the beginning and the end of a string.
   *  */
  const checkAndUpdateSezione = (item: any): any => {
    switch (item.sezione.trim()) {
      case SezioneEnum.S1:
        sezione = SezioneDescEnum.S1;
        item.color = { borderBottom: '15px solid #068E87' }; /*VERDE ACQUA*/
        break;
      case SezioneEnum.S2:
        sezione = SezioneDescEnum.S2;
        item.color = { borderBottom: '15px solid #068E87' }; /*VERDE ACQUA*/
        break;
      case SezioneEnum.S3:
        sezione = SezioneDescEnum.S3;
        item.color = { borderBottom: '15px solid #068E87' }; /*VERDE ACQUA*/
        break;
      case SezioneEnum.S4:
        sezione = SezioneDescEnum.S4;
        item.color = { borderBottom: '15px solid #068E87' }; /*VERDE ACQUA*/
        break;
      case SezioneEnum.S5:
        sezione = SezioneDescEnum.S5;
        item.color = { borderBottom: '15px solid #068E87' }; /*VERDE ACQUA*/
        break;
      case SezioneEnum.S6:
        sezione = SezioneDescEnum.S6;
        item.color = { borderBottom: '15px solid #068E87' }; /*VERDE ACQUA*/
        break;
      case SezioneEnum.S7:
        sezione = SezioneDescEnum.S7;
        item.color = { borderBottom: '15px solid #2852B5' }; /*BLUE*/
        break;
      case SezioneEnum.SU:
        sezione = SezioneDescEnum.SU;
        item.color = { borderBottom: '15px solid #5D2566' }; /*LILLA*/
        break;
      default:
        sezione = '';
        break;
    }
    item.sezione = sezione;
    return item;
  };

  /*
   * This function group items by a key. In this case key is 'month'.
   * If I change my mind in the future, I'll change the key.
   *   * */
  const groupByKey = (array: any, key: string) => {
    return array.reduce((hash: any, obj: any) => {
      if (obj[key] === undefined) return hash;
      const itemAggiornato = checkAndUpdateSezione(obj);
      itemAggiornato.mese = itemAggiornato.mese.trim();
      return Object.assign(hash, {
        [itemAggiornato[key]]: (hash[itemAggiornato[key]] || []).concat(
          itemAggiornato
        ),
      });
    }, {});
  };

  const getContent = useCallback(async () => {
    try {
      let response: any;
      if (filteredResearch) {
        setAllMinute(new Map<string, any>());
        response = await axios.get(
          serviceUrlByFilter +
            '?dataUdienza=' +
            dataUdienza +
            '&sezione=' +
            sezione +
            '&tipoUdienza=' +
            tipoUdienza +
            '&collegio=' +
            collegio
        );
      } else {
        response = await axios.get(serviceUrl);
      }
      if (response.data && response.data.length > 0) {
        const result = groupByKey(response.data, 'mese');
        setAllMinute(result);
      }
    } catch (error) {
      setError('Nessuna udienza presente come Presidente di Collegio');
    } finally {
    }
    setIsLoading(false);
  }, [
    filteredResearch,
    dataUdienza,
    sezione,
    tipoUdienza,
    collegio,
    minuteNew,
  ]);

  useEffect(() => {
    getContent();
  }, [filteredResearch, dataUdienza, sezione, tipoUdienza, collegio]);

  const renderingCardUdienza = (
    value: any[],
    monthName: string | undefined
  ) => {
    let minuteFiltrate = value;
    if (value) {
      //////// mutually exclusive filters ////////
      minuteFiltrate = minuteFiltrate?.filter((u: any) =>
        onlyMinuteNew ? u.minutaAccettata > 0 : true
      );
      minuteFiltrate = minuteFiltrate?.filter((u: any) =>
        onlyProvvedimentiNew ? u.ricorsiTotali > u.pubblicatiTotali : true
      );

      //////// mutually exclusive filters ////////
      if (minuteFiltrate && minuteFiltrate.length > 0) {
        return (
          <>
            <Typography variant="h2">
              {t('scrivania.minuteUdienza.minute')} {monthName || ''}
            </Typography>
            <Grid container>
              {minuteFiltrate?.map((item: any) => (
                <CardUdienza key={item.idUdienza} record={item} />
              ))}
            </Grid>
          </>
        );
      }
    }
    return <></>;
  };
  return (
    <>
      <Grid container p={3} border={{ border: theme.custom.borders[0] }}>
        {error ? (
          <Typography variant="h2">{error}</Typography>
        ) : (
          <>
            {allMinute && Object.keys(allMinute).length > 0 ? (
              Object.keys(allMinute).map((key: string) =>
                renderingCardUdienza(allMinute[key], key)
              )
            ) : (
              <></>
            )}
          </>
        )}
      </Grid>
      <NsFullPageSpinner isOpen={isLoading} value={1} />
    </>
  );
}
