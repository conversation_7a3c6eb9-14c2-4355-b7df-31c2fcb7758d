import { Grid, Typography } from '@mui/material';
import FilesUdienza from './FilesUdienza';
import {useTranslation} from "next-i18next";

export default function ModifyUdienza() {
  const {t} = useTranslation();
    return (
        <>
            <Grid
                container
                p={5}
                sx={{
                    background: 'white',
                    borderRadius: '20px',
                    boxShadow:
                        '1px 0px 5px rgba(127, 127, 127, 0.34901960784313724)',
                }}
                mt={5}
            >
                <Grid
                    item
                    xs={11.8}
                    display="flex"
                    justifyContent="space-between"
                >
                    <Typography variant="h1">
                      {t('scrivania.modifyUdienza.minuteRichiestaModifica')}
                    </Typography>
                </Grid>
                <Grid mt={4} container>
                    <FilesUdienza />
                    <FilesUdienza />
                    <FilesUdienza />
                    <FilesUdienza />
                    <FilesUdienza />
                    <FilesUdienza />
                </Grid>
            </Grid>
        </>
    );
}
