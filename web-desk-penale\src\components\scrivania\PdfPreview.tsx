import { Box, Grid, useTheme } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier
} from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import MainModal from '../shared/MainModal';
import { useConfig } from '../shared/configuration.context';
import RichiediModificaModal from './RichiediModificaModal';
import { OrigineProvvedimentoSchema } from '../relay/relayQueriesOrigineProvvedimento';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';

import { useRouter } from 'next/router';
import { relayQueriesOrigineProvvedimentoQuery } from '@/generated/relayQueriesOrigineProvvedimentoQuery.graphql';
import { convertTipoProvvedimentoForRedazioneOnlinePresidente } from '../shared/Utils';

const ORIGINE_QUERY = OrigineProvvedimentoSchema;

interface PdfPreviewProps {
  provv: string;
  refreshPage: () => void;
  setRefreshCoda?: () => void;
  autoScroll?: boolean;
  fascicolo?: any;
  idUdienza?: any;
}

export default function PdfPreview({
  provv,

  refreshPage,
  setRefreshCoda,
  autoScroll,
  fascicolo,
  idUdienza,
}: PdfPreviewProps) {
  const { servizi } = useConfig();
  const { t } = useTranslation();
  const theme: any = useTheme();
  const serviceUrl2 = `${servizi}`;
  const { notify } = useNotifier();
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isLoading, setIsloading] = useState<boolean>(false);
  const router = useRouter();

  const [origine, setOrigine] = useState<string | null>(null);

  const [enabledRichiestaDiModificaEVerificato, setEnabledRichiestaDiModificaEVerificato] = useState<boolean>(false);

  const nrgNumeric = fascicolo?.nrgNumeric ?? fascicolo?.nrg ?? router.query.params as string;

  const { data } = useQuery<relayQueriesOrigineProvvedimentoQuery>(
    ORIGINE_QUERY,
    {
      nrg: nrgNumeric,
    },
    {
      fetchPolicy: 'store-and-network',
      skip: !nrgNumeric,
    }
  );

  useEffect(() => {
    if (data?.provvedimentoByNrgPerPresidente) {
      const matchingProvvedimento = data.provvedimentoByNrgPerPresidente.find(
      p => p.idProvvedimento === provv
    );

    if (matchingProvvedimento) {
      setOrigine(matchingProvvedimento.origine);
      setEnabledRichiestaDiModificaEVerificato(matchingProvvedimento.enabledRichiestaDiModificaEVerificato ?? false);
    } else {
      setOrigine(fascicolo?.origine);
      setEnabledRichiestaDiModificaEVerificato(false);
    }
    } else {
      setOrigine(fascicolo?.origine);
    setEnabledRichiestaDiModificaEVerificato(false);}
  }, [data, fascicolo, provv]);

  const verificato = () => {
    setIsloading(true);
    axios
      .post(serviceUrl2 + '/presidente/verificato/' + provv)
      .then((response: any) => {
        notify({
          message:
            'Verifica avvenuta con successo. Il provvedimento è stato verificato ed inserito in "Coda di deposito"',
          type: 'success',
        });
        refreshPage();
        if (setRefreshCoda) setRefreshCoda();
      })
      .catch((error: any) => {
        console.log('error', error);
        refreshPage();
        if (setRefreshCoda) setRefreshCoda();
      })
      .finally(() => {
        setIsloading(false);
      });
  };

  const scroll = () => {
    const element = document.getElementById('pdf-scrolled');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };
  useEffect(() => {
    if (autoScroll) {
      if (pdfUrl) {
        scroll();
      }
    }
  }, [pdfUrl]);

  useEffect(() => {
    setIsloading(true);

    axios
      .get(serviceUrl2 + '/provvedimento/getProvvedimentiDaFirmare/' + provv)
      .then((_: any) => {
        axios
          .get(serviceUrl2 + '/provvedimento/downloadPdfByIdProvv/' + provv, {
            responseType: 'blob',
          })
          .then((response: any) => {
            // console.log('response', response); commentato perché potrebbe essere presente bearer token
            const pdfBlob = new Blob([response.data], {
              type: 'application/' + 'pdf',
            });
            const pdfUrl = window.URL.createObjectURL(pdfBlob);
            setPdfUrl(pdfUrl);
          })
          .catch((error: any) => {
            console.log('error', error);
            notify({
              message: 'Errore nel download del pdf',
              type: 'error',
            });
          });
      })
      .catch((error: any) => {
        console.log('error', error);
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      })
      .finally(() => {
        setIsloading(false);
      });
  }, []);

  const handleOpenModal = () => {
    setModalProps({ ...modalProps, modal: true });
  };

  const closeModal = () => {
    setModalProps({ ...modalProps, modal: false });
    refreshPage();
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    p: 2,
  };

  const [modalProps, setModalProps] = useState({
    modal: false,
    closeModal,
    style: style,
    title: t('scrivania.pdfPreview.richiediModifica'),
    body: <RichiediModificaModal provv={provv} closeModal={closeModal} />,
  });

  const serviceUrl = `${servizi}`;

  const handleModifica = () => {
    setIsloading(true);
    axios
      .get(serviceUrl + '/provvedimento/duplicate/' + provv)
      .then((response) => {
        // notify({
        //   message: 'Provvedimento duplicato con successo',
        //   type: 'success',
        // });

        let tipologia: any = '';
        if (fascicolo?.tipo) {
          tipologia = convertTipoProvvedimentoForRedazioneOnlinePresidente(
            fascicolo.tipo
          );
        } else {
          tipologia = convertTipoProvvedimentoForRedazioneOnlinePresidente(
            fascicolo.tipologia
          );
        }
        router.push({
          pathname: '/editor',
          query: {
            idUdienza: idUdienza,
            params: nrgNumeric,
            tipoProvvedimento: tipologia,
            idProvvedimento: response.data,
            edit: true,
            //IRP (isRuoloPresidente) se a true vuol dire che sono presidente
            IRP: true,
          },
        });
      })
      .catch((error) => {
        setIsloading(false);
        console.log('error', error);
        refreshPage();
        if (setRefreshCoda) setRefreshCoda();
      });
  };

  return (
    <Grid item xs={12}>
      {pdfUrl && (
        <Box width="100%" id="pdf-scrolled">
          <iframe src={pdfUrl} width="100%" height="800px" />
          <Box display="flex" justifyContent="space-between" mt={2} mb={2}>
            {enabledRichiestaDiModificaEVerificato ? (
              <Box
                display="flex"
                justifyContent="space-between"
                width="fit-content"
              >
                <NsButton
                  variant="outlined"
                  color="primary"
                  onClick={handleOpenModal}
                >
                  {t('scrivania.pdfPreview.richiediModifica')}
                </NsButton>
                {origine === 'SYSTEM' && (
                  <NsButton
                    variant="outlined"
                    color="primary"
                    onClick={handleModifica}
                    style={{ marginLeft: '10px' }}
                    disabled={isLoading}
                  >
                    {t('scrivania.pdfPreview.modifica')}
                  </NsButton>
                )}
              </Box>
            ) : (
              <div></div>
            )}
            {enabledRichiestaDiModificaEVerificato ? (
              <NsButton
                variant="contained"
                color="primary"
                onClick={verificato}
              >
                {t('scrivania.pdfPreview.verificato')}
              </NsButton>
            ) : (
              <div></div>
            )}
          </Box>
          <MainModal {...modalProps} />
        </Box>
      )}
      {isLoading && <NsFullPageSpinner isOpen={true} value={1} />}
    </Grid>
  );
}
