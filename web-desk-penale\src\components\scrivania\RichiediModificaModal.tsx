import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import {
  Grid,
  MenuItem,
  Select,
  TextareaAutosize,
  Typography,
  useTheme,
} from '@mui/material';
import { NsButton, useNotifier } from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useTranslation } from 'next-i18next';
import router from 'next/router';
import { useEffect, useState } from 'react';
import { useConfig } from '../shared/configuration.context';

export default function RichiediModificaModal({ provv, closeModal }: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const options = [
    { name: 'seleziona motivo', value: 1 },
    { name: 'dati anagrafici errati', value: 2 },
    { name: 'punteggiatura errata', value: 3 },
    { name: 'ripetizione paragrafo/frase', value: 4 },
    { name: 'richiesto oscuramento', value: 5 },
    { name: 'altro', value: 6 },
  ];

  const [inviata, setInviata] = useState<any>(false);

  const { servizi } = useConfig();

  const { notify } = useNotifier();

  const serviceUrl = `${servizi}`;

  const [note, setNote] = useState<any>([]);

  const [selectedOption, setSelectedOption] = useState<any>(1);

  const [nrg, setNrg] = useState<any>([]);
  const [numeroFascicolo, setNumeroFascicolo] = useState<any>([]);
  const [annoFascicolo, setAnnoFascicolo] = useState<any>([]);

  const handleOptionChange = (event: any) => {
    setSelectedOption(event.target.value);
  };

  const [isButtonAlreadyClicked, setIsButtonAlreadyClicked] = useState(false);

  const selectedOptionName = options.find(
    (item: any) => item.value == selectedOption
  )?.name;

  const getInfoNote = (values: any) => {
    axios
      .get(serviceUrl + '/provvedimento/getInfoNote/' + provv)
      .then((response: any) => {
        setNrg(response.data.nrg);
        setNumeroFascicolo(response.data.numeroFascicolo);
        setAnnoFascicolo(response.data.annoFascicolo);
      })
      .catch((error: any) => {
        console.log('error', error);
        notify({
          message: 'Errore nel get info note',
          type: 'error',
        });
      });
  };

  useEffect(() => {
    getInfoNote(provv);
  }, []);

  const handleRichiediModifica = () => {
    setIsButtonAlreadyClicked(true);
    const body = {
      nrg: nrg,
      note: selectedOptionName + '.' + note,
      numeroFascicolo: numeroFascicolo,
      annoFscicolo: annoFascicolo,
      tipoModifica: 'RICHIESTA_MODIFICA',
    };
    axios
      .post(serviceUrl + '/presidente/richiestaModifica/' + provv, { ...body })
      .then((response: any) => {
        setInviata(true);
        notify({
          message: 'Richiesta inviata con successo',
          type: 'success',
        });
      })
      .catch((error: any) => {
        setInviata(false);
        setIsButtonAlreadyClicked(false)
        console.log('error', error);
        closeModal();
      });
  };

  const handleNoteChange = (event: any) => {
    setNote(event.target.value);
  };

  console.log('note', note);

  if (!inviata) {
    return (
      <Grid container>
        <Grid item xs={12} p={1}>
          <Typography variant="subtitle1" align="left" fontWeight="bold">
            {t('scrivania.richiediModificaModal.categoriaDellaModifica')}
          </Typography>
          <Select value={selectedOption} onChange={handleOptionChange}>
            {options.map((option, i) => (
              <MenuItem key={i} value={option.value}>
                {option.name}
              </MenuItem>
            ))}
          </Select>
          <Typography variant="subtitle1" align="left" fontWeight="bold">
            {t(
              'scrivania.richiediModificaModal.specificheDellaRichiestaDiModifica'
            )}
          </Typography>
          <TextareaAutosize
            placeholder="E' possibile inserire ulteriori indicazioni per la modifica"
            value={note}
            onChange={handleNoteChange}
            minRows={10}
            style={{
              width: '100%',
              resize: 'none',
              overflow: 'auto',
              height: '300px',
            }}
          />
        </Grid>
        <Grid item xs={12} p={1} container>
          <NsButton
            variant="outlined"
            color="primary"
            sx={{ mt: 2, mr: 'auto' }}
            onClick={closeModal}
          >
            {t('scrivania.richiediModificaModal.annulla')}
          </NsButton>
          <NsButton
            variant="contained"
            color="primary"
            sx={{ mt: 2, ml: 'auto' }}
            onClick={handleRichiediModifica}
            disabled={selectedOption == 1 || isButtonAlreadyClicked}
          >
            {t('scrivania.richiediModificaModal.inviaRichiesta')}
          </NsButton>
        </Grid>
      </Grid>
    );
  } else {
    return (
      <Grid container>
        <Grid item xs={12} p={1}>
          <CheckCircleIcon sx={{ fontSize: 40, color: 'green' }} />
          <Typography variant="h2" align="left" fontWeight="bold">
            {t(
              'scrivania.richiediModificaModal.richiestaDiModificaInviataConSuccesso'
            )}
          </Typography>
          <Typography variant="subtitle1" align="left">
            La richiesta è stata inoltrata. Puoi monitorare le tue richieste di
            modifica in
            <NsButton
              variant="text"
              color="primary"
              onClick={() => router.push('/scrivania')}
            >
              {t('scrivania.richiediModificaModal.minutePerUdienza')}
            </NsButton>
          </Typography>
        </Grid>
      </Grid>
    );
  }
}
