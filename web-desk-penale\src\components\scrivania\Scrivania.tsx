import ClearIcon from '@mui/icons-material/Clear';
import {
  Box,
  Switch,
  FormControlLabel,
  Grid,
  Typography,
  useTheme,
  Button,
} from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import CodaButton from '../shared/CodaButton';
import MainModal from '../shared/MainModal';
import { formatDate } from '../shared/Utils';
import MinuteUdienza from './MinuteUdienza';
import ScrivaniaFilter from './ScrivaniaFilter';
import VisualTable from './VisualTable';

const criteriaParameters = {
  filteredResearch: false,
  dataUdienza: '',
  sezione: '',
  tipoUdienza: '',
  collegio: '',
};

const styledFilter = {
  display: 'flex',
  background: '#308a7d',
  borderRadius: '10px',
  width: '79px',
  color: 'white',
  marginBottom: '5px',
  alignItems: 'center',
  justifyContent: 'space-around',
  paddingRight: '5px',
  cursor: 'pointer',
  height: '30px',
};

export default function Scrivania() {
  const theme: any = useTheme();
  const { t } = useTranslation();

  const [filteredResourceData, setFilteredResourceData] = useState<any>();
  const [onlyMinuteNew, setOnlyMinuteNew] = useState<boolean>(true);
  const [onlyProvvedimentiNew, setOnlyProvvedimentiNew] =
    useState<boolean>(true);
  const [viewTable, setViewTable] = useState<boolean>(false);

  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    p: 2,
  };

  const closeModal = () => {
    setModalProps({ ...modalProps, modal: false });
  };

  const [modalProps, setModalProps] = useState({
    modal: false,
    closeModal,
    style,
    title: '',
    body: <></>,
  });

  const handleReset = () => {
    setFilteredResourceData(criteriaParameters);
  };

  const changeView = () => {
    setViewTable((oldValue) => !oldValue);
  };

  const handleModal = (title: string) => {
    let body = (
      <ScrivaniaFilter
        filteredResearch={filteredResourceData?.filteredResearch}
        setFilteredResourceData={(filteredResourceData: any) =>
          setFilteredResourceData(filteredResourceData)
        }
        closeModal={closeModal}
      />
    );

    setModalProps({ ...modalProps, body, modal: true, title, style });
  };

  const changeMinuteTelematiche = (status: boolean) => {
    localStorage.setItem('minuteTelematiche', JSON.stringify(status));
    setOnlyMinuteNew(status);
  };

  useEffect(() => {
    const storedValue = JSON.parse(
      localStorage.getItem('minuteTelematiche') as string
    );
    if(storedValue != null){
      setOnlyMinuteNew(storedValue);
    }

  }, []);

  function getMinuteUdienza() {
    if(filteredResourceData?.filteredResearch) {
      return <MinuteUdienza
        filteredResearch={filteredResourceData.filteredResearch}
        dataUdienza={filteredResourceData.dataUdienza}
        sezione={filteredResourceData.sezione}
        tipoUdienza={filteredResourceData.tipoUdienza}
        collegio={filteredResourceData.collegio}
        onlyMinuteNew={onlyMinuteNew}
        onlyProvvedimentiNew={onlyProvvedimentiNew}
      />;
    }
    return <MinuteUdienza
        onlyMinuteNew={onlyMinuteNew}
        onlyProvvedimentiNew={onlyProvvedimentiNew}
    />
  }

  return (
    <Grid container>
      <Grid
        item
        xs={12}
        mb={4}
        pl={2}
        display="flex"
        justifyContent="space-between"
      >
        <CodaButton ruolo={'PRESIDENTE'} />
      </Grid>
      <Grid
        xs={12}
        mb={4}
        pl={2}
        item
        display="flex"
        justifyContent="space-between"
        sx={{ paddongTop: '120', marginBottom: '0' }}
      >
        <Grid
          xs={6}
          item
          mb={3}
          pl={2}
          container
          gridAutoFlow="columns"
          justifyContent="flex-start"
        >
          <Typography variant="h1" mr={5.5}>
            {t('scrivania.minuteDaVerificare')}
          </Typography>

          <Button
            size="small"
            variant="contained"
            color="primary"
            onClick={() => changeView()}
          >
            {!viewTable ? t('scrivania.visualizzaLista') : 'Torna indietro'}
          </Button>
        </Grid>
        { filteredResourceData?.filteredResearch && (
          <Grid
            item
            xs={12}
            border={'2px solid #308A7D'}
            borderRadius={'10px'}
            justifyContent="space-between"
          >
            <Typography
              mt={-1}
              ml={2}
              display="flex"
              justifyContent={'center'}
              width={'125px'}
              bgcolor={'#FEFEFE'}
              variant="h3"
            >
              {t('scrivania.filtriImpostati')}
            </Typography>
            <Grid container justifyContent={'space-between'} pr={2} mt={2}>
              {filteredResourceData?.dataUdienza && (
                <Grid pl={2}>
                  <Typography
                    variant="h3"
                    align="center"
                    justifyContent="center"
                  >
                    {t('scrivania.dataUdienza')}:{' '}
                    {formatDate(
                      filteredResourceData?.dataUdienza,
                      'DD/MM/YY'
                    )}
                  </Typography>
                </Grid>
              )}
              {filteredResourceData?.sezione && (
                <Grid pl={2}>
                  <Typography variant="h3">
                    {t('scrivania.sezione')}: {filteredResourceData?.sezione}
                  </Typography>
                </Grid>
              )}
              {filteredResourceData?.tipoUdienza && (
                <Grid pl={2}>
                  <Typography variant="h3">
                    {t('scrivania.tipoUdienza')}:{' '}
                    {filteredResourceData?.tipoUdienza}
                  </Typography>
                </Grid>
              )}
              {filteredResourceData?.collegio && (
                <Grid pl={2}>
                  <Typography variant="h3">
                    {t('scrivania.collegio')}:{' '}
                    {filteredResourceData?.collegio}
                  </Typography>
                </Grid>
              )}
              <Grid pl={2}>
                <Box sx={styledFilter} onClick={() => handleReset()}>
                  <ClearIcon fontSize="small" />
                  {t('common.reset')}
                </Box>
              </Grid>
            </Grid>
          </Grid>
        )}
      </Grid>
      <Grid xs={12} item display="flex" justifyContent="space-between">
        <Grid
          item
          xs={7}
          mb={3}
          pl={2}
          display="grid"
          gridAutoFlow={'column'}
        >
          <FormControlLabel
            control={
              <Switch
                className="switchUdienzeConMinuteTelematicheNuove"
                defaultChecked
                checked={onlyMinuteNew}
                onChange={(evt) =>
                  changeMinuteTelematiche(evt.target.checked)
                }
              />
            }
            label={t('scrivania.udienzeConMinuteTelematicheNuove')}
          />
          <FormControlLabel
            control={
              <Switch
                className="switchUdienzeConAlmenoUnProvvedimentoNonPubblicato"
                defaultChecked
                checked={onlyProvvedimentiNew}
                onChange={(evt) =>
                  setOnlyProvvedimentiNew(evt.target.checked)
                }
              />
            }
            label={t('scrivania.soloUdienzeConAlmenoUnProvvNonPubblicato')}
          />
        </Grid>
        <Grid
          xs={6}
          pl={2}
          item
          display="grid"
          justifyContent="end"
          gridAutoFlow={'column'}
        >
          <Box>
            <NsButton
              // sx={{
              //   position: 'absolute',
              //   top: '250px',
              //   right: '-2px',
              //   zIndex: 1,
              // }}
              onClick={() => handleModal(t('common.filtraPer'))}
              variant="contained"
            >
              {t('common.filtra')}
            </NsButton>
          </Box>
        </Grid>
      </Grid>
      <Grid
        item
        xs={12}
        mb={4}
        pl={2}
        display="flex"
        justifyContent="space-between"
      >
        {viewTable ? (
          <VisualTable
            onlyMinuteNew={onlyMinuteNew}
            onlyProvvedimentiNew={onlyProvvedimentiNew}
            filteredResourceData={filteredResourceData}
          />
        ) : getMinuteUdienza() }
        <MainModal {...modalProps} />
      </Grid>
    </Grid>
  );
}
