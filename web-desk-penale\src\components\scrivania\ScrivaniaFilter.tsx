import { Grid, MenuItem, Select, TextField, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MenuOptionsProps, ScrivaniaFilterProps } from 'src/interfaces';

let criteriaParameters = {
  filteredResearch: false,
  dataUdienza: '',
  sezione: '',
  tipoUdienza: '',
  collegio: '',
};

export default function ScrivaniaFilter({
  filteredResearch,
  setFilteredResourceData,
  closeModal,
}: ScrivaniaFilterProps) {
  const { t } = useTranslation();
  const [tipoUdienza, setTipoUdienza] = useState<string>('');
  const [collegio, setCollegio] = useState<string>('');
  const [sezione, setSezione] = useState<string>('');
  const [dataUdienza, setDataUdienza] = useState<string>('');

  const collegioOptions: MenuOptionsProps[] = [
    { value: '', label: t('scrivania.mostraTutti') },
    { value: '0', label: '0' },
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' },
    { value: '5', label: '5' },
  ];

  const sezioneOptions: MenuOptionsProps[] = [
    { value: '', label: t('scrivania.tutteLeSezioni') },
    { value: 'S1', label: 'I Sezione' },
    { value: 'S2', label: 'II Sezione' },
    { value: 'S3', label: 'III Sezione' },
    { value: 'S4', label: 'IV Sezione' },
    { value: 'S5', label: 'V Sezione' },
    { value: 'S6', label: 'VI Sezione' },
    { value: 'S7', label: 'VII Sezione' },
    { value: 'SU', label: 'Sezioni Unite' },
  ];

  const tipoUdienzaOptions: MenuOptionsProps[] = [
    { value: '', label: t('scrivania.tutte') },
    { value: 'CC', label: 'CC' },
    { value: 'PU', label: 'PU' },
  ];

  const handleAnnulla = () => {
    if (closeModal) {
      closeModal();
    }
  };

  useEffect(() => {
    if (filteredResearch) {
      setTipoUdienza(criteriaParameters.tipoUdienza);
      setDataUdienza(criteriaParameters.dataUdienza);
      setCollegio(criteriaParameters.collegio);
      setSezione(criteriaParameters.sezione);
    }
  }, [criteriaParameters]);

  const handleApplicaFiltri = () => {
    const dataUdienzaRes = dataUdienza != '' ? dataUdienza : null;
    const sezioneRes = sezione != '' ? sezione : null;
    const tipoUdienzaRes = tipoUdienza != '' ? tipoUdienza : null;
    const collegioRes = collegio != '' ? collegio : null;

    criteriaParameters = {
      filteredResearch: true,
      sezione: sezioneRes ? sezioneRes : '',
      tipoUdienza: tipoUdienzaRes ? tipoUdienzaRes : '',
      collegio: collegioRes ? collegioRes : '',
      dataUdienza: dataUdienzaRes ? dataUdienzaRes : '',
    };
    setFilteredResourceData(criteriaParameters);

    if (closeModal) {
      closeModal();
    }
  };

  return (
    <Grid container>
      <Grid item xs={12} mt={6}>
        <Typography mb={1}>{t('scrivania.dataUdienza')}</Typography>
        <TextField
          size="small"
          type="date"
          value={dataUdienza}
          label=""
          name="dataUdienza"
          sx={{ minWidth: 350 }}
          onChange={(e) => setDataUdienza(e.target.value)}
        />
        {/*<ValidatedDatePicker size="small" name="test" label="Data Udienza" />*/}
      </Grid>
      <Grid item xs={12} mt={6}>
        <Typography mb={1}>{t('scrivania.sezione')}</Typography>
        <Select
          size="small"
          defaultValue={''}
          label=""
          name="sezione"
          value={sezione}
          onChange={(e) => setSezione(e.target.value)}
          sx={{ minWidth: 350 }}
        >
          {sezioneOptions.map((sezione, i) => (
            <MenuItem key={i} value={sezione.value}>
              {sezione.label}
            </MenuItem>
          ))}
        </Select>
      </Grid>
      <Grid item xs={12} mt={6}>
        <Typography mb={1}>{t('scrivania.tipoUdienza')}</Typography>
        <Select
          size="small"
          label=""
          name="tipoUdienza"
          value={tipoUdienza}
          onChange={(e) => setTipoUdienza(e.target.value)}
          sx={{ minWidth: 350 }}
        >
          {tipoUdienzaOptions.map((tipoUdienza, i) => (
            <MenuItem key={i} value={tipoUdienza.value}>
              {tipoUdienza.label}
            </MenuItem>
          ))}
        </Select>
      </Grid>
      <Grid item xs={12} mt={6}>
        <Typography mb={1}>{t('scrivania.collegio')}</Typography>
        <Select
          size="small"
          defaultValue={''}
          label=""
          name="collegio"
          value={collegio}
          onChange={(e) => setCollegio(e.target.value)}
          sx={{ minWidth: 350 }}
        >
          {collegioOptions.map((collegio, i) => (
            <MenuItem key={i} value={collegio.value}>
              {collegio.label}
            </MenuItem>
          ))}
        </Select>
      </Grid>

      <Grid item container justifyContent="space-between" xs={12} mt={5}>
        <NsButton onClick={handleAnnulla}>{t('buttonsLabel.annulla')}</NsButton>
        <NsButton variant="contained" onClick={handleApplicaFiltri}>
          {t('common.applicaFiltri')}
        </NsButton>
      </Grid>
    </Grid>
  );
}
