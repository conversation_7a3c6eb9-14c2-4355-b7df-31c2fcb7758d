import { Box, Grid, Typography, useTheme } from '@mui/material';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { MagType, UdienzaDatiHeaderProps } from 'src/interfaces';
import { Sezione, TipoUdienza } from '../shared/Utils';

const ruolo: MagType = {
  PRE: 'Presidente',
  CON: 'Consigliere',
  PM: 'PM',
};

export default function UdienzaDatiCalendarHeader({
  showValore = false,
  mainCollegio,
  termineDeposito,
  valPondale,
  selectedEvent,
}: UdienzaDatiHeaderProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const termineDepositoDate = moment(termineDeposito, 'DD/MM/YYYY');
  const today = moment();
  const diffDays =
    today.diff(termineDepositoDate, 'days') > 0
      ? today.diff(termineDepositoDate, 'days')
      : today.diff(termineDepositoDate, 'days') - 1;

  return (
    <>
      <Grid
        border={theme.custom.borders[0]}
        item
        container
        justifyContent="space-between"
        p={2}
        sx={{ background: '#FFFFFF' }}
      >
        {selectedEvent && (
          <Typography color={'#4F7479'} variant="h3" mb={2}>
            {`${Sezione[selectedEvent.sezione]} | ${
              TipoUdienza[selectedEvent.descrizione]
            } |
                      COLL ${selectedEvent.aula}`}
          </Typography>
        )}

        <Typography mb={2} sx={{ width: '100%' }} variant="h1">
          {t('scrivania.udienzaDatiCalendarHeader.datiUdienza')}
        </Typography>
        <Box sx={{ width: '100%' }}>
          <Box>{t('scrivania.udienzaDatiCalendarHeader.collegio')}:</Box>
          <Box sx={{ width: '100%' }}>
            <Typography sx={{ display: 'inline-block' }} variant="h4">
              {mainCollegio?.length > 0
                ? mainCollegio
                    ?.map((collegio: any, index: number) => {
                      const name =
                        collegio?.magistrato?.anagraficaMagistrato?.nome;
                      const surname =
                        collegio?.magistrato?.anagraficaMagistrato?.cognome;
                      const role =
                        collegio.tipoMag === 'PRE' ? 'Presidente' : '';
                      const separator =
                        index === mainCollegio.length - 1 ? '' : ', ';

                      return `${name} ${surname}${
                        role ? ` (${role})` : ''
                      }${separator}`;
                    })
                    .join('')
                : 'N/A'}
            </Typography>
          </Box>
        </Box>
        <Box>
          <Box>{t('scrivania.udienzaDatiCalendarHeader.termineDeposito')}:</Box>
          <Box sx={{ fontWeight: 700 }}>
            {termineDepositoDate.format('DD/MM/YYYY')}
            {diffDays > 0
              ? ` (Scaduto da ${diffDays} ${t(
                  'scrivania.udienzaDatiCalendarHeader.giorni'
                )})`
              : ` (Scade tra ${Math.abs(diffDays)} ${t(
                  'scrivania.udienzaDatiCalendarHeader.giorniDaOggi'
                )})`}
          </Box>
        </Box>
        {showValore && (
          <Box>
            <Box>
              {t('scrivania.udienzaDatiCalendarHeader.valorePonderale')}:
            </Box>
            <Box sx={{ fontWeight: 700 }}>{valPondale}</Box>
          </Box>
        )}
      </Grid>
    </>
  );
}
