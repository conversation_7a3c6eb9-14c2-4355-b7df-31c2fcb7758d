import {Box, Grid, Typography, useTheme} from '@mui/material';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { UdienzaDatiHeaderProps } from 'src/interfaces';
import { DetailUdienza } from '../calendar/DetailUdienza';
import React from "react";
import {NsTooltip} from "@netservice/astrea-react-ds";
import InfoIcon from "@mui/icons-material/Info";

export default function UdienzaDatiHeader({
  showValore = false,
  termineDeposito,
  valPondale,
  valPondComplessivo,
  idUdienza,
  nrg,
}: Readonly<UdienzaDatiHeaderProps>) {
  const theme: any = useTheme();
  const { t } = useTranslation();

  const termineDepositoDate = moment(termineDeposito, 'DD/MM/YYYY');
  const today = moment();
  const diffDays =
    today.diff(termineDepositoDate, 'days') > 0
      ? today.diff(termineDepositoDate, 'days')
      : today.diff(termineDepositoDate, 'days') - 1;

  /* This function component renderValPonderale is used to
  conditionally render components based on the showValore and valPondComplessivo properties.
  */
 const renderValPonderale = () => {
   if(showValore) {
     if (valPondComplessivo) {
       return (
         <Box sx={{ width: '30%' }}>
           <Box sx={{ display: 'flex', alignItems: 'center' }} className='svgInfoVal'>
             {t('scrivania.udienzaDatiHeader.valorePonderaleComplessivo')}
             <NsTooltip title={t('scrivania.udienzaDatiHeader.valPondComplessivoTooltipTitle')} placement="top-start">
               <InfoIcon  />
             </NsTooltip>:
           </Box>
           <Box sx={{ fontWeight: 700 }}>{valPondComplessivo}</Box>
         </Box>
       )
     }
     return (
        <Box sx={{width: '30%'}}>
         <Box>{t('scrivania.udienzaDatiHeader.valorePonderale')}</Box>
         <Box sx={{ fontWeight: 700 }}>{valPondale}</Box>
       </Box>
     )
   }
   return <></>
 }

  return (
    <Grid
      border={theme.custom.borders[0]}
      mt={2}
      item
      container
      justifyContent="space-between"
      p={2}
      sx={{background: '#FFFFFF'}}
    >
      <Typography mb={2} sx={{width: '100%'}} variant="h1">
        {t('scrivania.udienzaDatiHeader.datiUdienza')}
      </Typography>
      <Box sx={{width: '100%'}}>
        <Box>{t('scrivania.udienzaDatiHeader.collegio')}</Box>
        <DetailUdienza idUdienza={Number(idUdienza)} nrg={nrg}/>
        <br></br>
      </Box>
      <Box sx={{width: '30%'}}>
        <Box>{t('scrivania.udienzaDatiHeader.termineDeposito')}:</Box>
        <Box sx={{fontWeight: 700}}>
          {termineDepositoDate.format('DD/MM/YYYY')}
          {diffDays > 0
            ? ` (Scaduto da ${diffDays} ${t(
              'scrivania.udienzaDatiCalendarHeader.giorni'
            )})`
            : ` (Scade tra ${Math.abs(diffDays)}  ${t(
              'scrivania.udienzaDatiCalendarHeader.giorniDaOggi'
            )})`}
        </Box>
      </Box>
      { renderValPonderale() }
    </Grid>
  );
}
