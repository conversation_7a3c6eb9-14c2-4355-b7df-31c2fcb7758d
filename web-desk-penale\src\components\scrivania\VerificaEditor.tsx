import { Box, Grid, Typography } from '@mui/material';
import { azioniButton } from '../shared/Utils';
import { useTranslation } from 'react-i18next';

export default function VerificaEditor() {
  const azioni = azioniButton();
  const { t } = useTranslation();
  return (
    <Grid p={5} item bgcolor={'#FEFEFE'} xs={9}>
      <Typography variant="h1">
        {t('scrivania.verificaEditor.verificaOrichiedi')}
      </Typography>
      <Box display="flex" alignItems="center">
        <Typography mr={1} mt={1}>
          Per verificare le minute clicca nella tabella sull&apos;icona{' '}
        </Typography>
        {azioni.getIcons('preview').map((icon: any) => (
          <Box
            key={icon.id}
            sx={{ ...azioni.iconBoxStyles, background: icon.bgColor }}
          >
            {icon.icon}
          </Box>
        ))}
      </Box>
    </Grid>
  );
}
