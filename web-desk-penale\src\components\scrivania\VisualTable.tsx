import {Box, Grid, TableCell, Tooltip, useTheme} from '@mui/material';
import WarningIcon from '@mui/icons-material/Warning';
import axios from 'axios';
import { useCallback, useEffect, useState } from 'react';
import { useConfig } from '../shared/configuration.context';
import RelayTable from '../shared/RelayTable';
import { Column, VisualTableProps } from 'src/interfaces';
import { NsFullPageSpinner, NsTooltip } from '@netservice/astrea-react-ds';
import { useNotifier } from '../../utils/NsNotifier';
import { useTranslation } from 'react-i18next';
import FolderIcon from '@mui/icons-material/Folder';
import { useRouter } from 'next/router';
import { SezioneColors, SezioneShort } from '../shared/Utils';

export default function VisualTable({
  filteredResourceData,
  onlyMinuteNew,
  onlyProvvedimentiNew,
}: VisualTableProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const [rows, setRows] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const router = useRouter();

  const { servizi } = useConfig();
  const serviceUrl = `${servizi}/datiScrivania/dashboard`;
  const serviceUrlByFilter = `${servizi}/datiScrivania/byFilter`;

  const [orderBy, setOrderBy] = useState<string>();
  const [order, setOrder] = useState<string>('asc');
  const [showCellTitle, setShowCellTitle] = useState(true);

  const getContent = useCallback(async () => {
    try {
      let response: any;
      if (filteredResourceData?.filteredResearch) {
        response = await axios.get(
          serviceUrlByFilter +
            '?dataUdienza=' +
            filteredResourceData?.dataUdienza +
            '&sezione=' +
            filteredResourceData?.sezione +
            '&tipoUdienza=' +
            filteredResourceData?.tipoUdienza +
            '&collegio=' +
            filteredResourceData?.collegio
        );
      } else {
        response = await axios.get(serviceUrl);
      }

      if (response.data && response.data.length > 0) {
        const result = response.data;
        filterByOptions(result);
      }
    } catch (error) {
      notify({
        message: t('errors.nessunaUdienza') as string,
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  }, [filteredResourceData, onlyMinuteNew, onlyProvvedimentiNew]);

  const filterByOptions = (data: any) => {
    const filteredRows = data?.filter(
      (u: any) =>
        (onlyProvvedimentiNew ? u.ricorsiTotali > u.pubblicati : true) &&
        (onlyMinuteNew ? u.minutaAccettata > 0 : true)
    );

    setRows(filteredRows);
  };

  function parseDate(dateString: any) {
    // Split the date string by "." and extract the day, month, and year
    let dateParts = dateString.split('.');
    let day = parseInt(dateParts[0]);
    let month = parseInt(dateParts[1]) - 1; // Subtract 1 because months are zero-based
    let year = parseInt(dateParts[2]);

    // Return a new Date object with the extracted values
    return new Date(year, month, day);
  }

  const createSortHandler = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrderBy(property);
    switch (property) {
      case 'dataUdienza':
        if (isAsc)
          rows.sort(
            (a: any, b: any) =>
              parseDate(a.dataUdienza).getTime() -
              parseDate(b.dataUdienza).getTime()
          );
        else
          rows.sort(
            (a: any, b: any) =>
              parseDate(b.dataUdienza).getTime() -
              parseDate(a.dataUdienza).getTime()
          );
        break;
    }
    setOrder(isAsc ? 'desc' : 'asc');
  };

  useEffect(() => {
    getContent();
  }, [filteredResourceData, onlyMinuteNew, onlyProvvedimentiNew]);

  const renderSezione = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{
          border: theme.custom.borders[0],
        }}
        title="Sez, Tipo Udienza, Collegio"
      >
        <Box display="flex" alignItems="center">
          <Box
            component={'span'}
            width="15px"
            height="15px"
            minHeight="15px"
            maxHeight="15px"
            minWidth="15px"
            maxWidth="15px"
            sx={{
              background: SezioneColors[row.sezione],
              borderRadius: '50%',
              marginRight: '5px',
              marginLeft: '-10px',
            }}
          />
          <Box>
            <Grid
              sx={{
                whiteSpace: 'nowrap',
              }}
            >
              <Grid>{`${SezioneShort[row.sezione]} | ${row.tipo}`}</Grid>
              <Grid>{`${t('common.collegio')} ${row.aula}`}</Grid>
            </Grid>
          </Box>
        </Box>
      </TableCell>
    );
  };

  const renderTelematiche = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{
          border: theme.custom.borders[0],
          fontWeight: row.minutaAccettata ? 'bold' : '',
        }}
        title={t('scrivania.minuteTelNuove') as string}
      >
        {row.minutaAccettata}
      </TableCell>
    );
  };

  const renderInvinviatiCancelleria = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{
          border: theme.custom.borders[0],
        }}
        title={showCellTitle ? (t('scrivania.provvInviatiCancelleria') as string) : undefined}
      >
        <Box display="flex" alignItems="center">
          <Box>{row.firmati + row.bustaRifiutata}</Box>
          <Box ml={0.5}>
            {row.bustaRifiutata > 0 && (
              <NsTooltip title={t('scrivania.busteRifiutate')}>
                <WarningIcon
                  style={{ color: 'orange' }}
                  onMouseEnter={() => setShowCellTitle(false)}
                  onMouseLeave={() => setShowCellTitle(true)}
                />
              </NsTooltip>
            )}
          </Box>
        </Box>
      </TableCell>
    );
  }

  const renderAzioni = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box display="center" alignItems="center">
          <NsTooltip
            title={t('shared.datiUdienza.visualizzaUdienza')}
            icon={
              <FolderIcon
                fontSize="small"
                sx={{ cursor: 'pointer', color: '#2E5A60' }}
                onClick={() => router.push(`/scrivania/${row.idUdienza}`)}
              />
            }
          />
        </Box>
      </TableCell>
    );
  };


  const columns: Column[] = [
    {
      id: 'dataUdienza',
      minWidth: 170,
      label: 'Data Udienza',
      align: 'left',
    },
    {
      id: 'sezione',
      minWidth: 170,
      label: 'Sez, Tipo Udienza, Collegio',
      align: 'left',
      render: renderSezione,
      disabledSort: true,
    },
    {
      id: 'ricorsiTotali',
      align: 'left',
      label: 'Ricorsi Tot.',
      minWidth: 170,
      disabledSort: true,
    },
    {
      id: 'riuniti',
      align: 'left',
      label: t('scrivania.riuniti') as string,
      minWidth: 170,
      disabledSort: true,
    },
    {
      id: 'minutePervenuteSic',
      align: 'left',
      label: t('scrivania.minuteCartacee') as string,
      minWidth: 170,
      disabledSort: true,
    },
    {
      id: 'pubblicatiTotali',
      minWidth: 170,
      label: 'Provv. Pubblicati Tot.',
      align: 'left',
      disabledSort: true,
    },
    {
      id: 'minutaAccettata',
      minWidth: 170,
      label: t('scrivania.minuteTelNuove') as string,
      align: 'left',
      render: renderTelematiche,
      disabledSort: true,
    },
    {
      id: 'richiestaModifica',
      minWidth: 170,
      label: 'Richiesta Modifica',
      align: 'left',
      disabledSort: true,
    },
    {
      id: 'bozzaPresidente',
      minWidth: 170,
      label: t('scrivania.bozzaMinutamodificata') as string,
      align: 'left',
      disabledSort: true,
    },
    {
      id: 'bozzaMinutaModificata',
      minWidth: 170,
      label: t('scrivania.minutaModificataInoltrataEstensore') as string,
      align: 'left',
      disabledSort: true,
    },
    {
      id: 'lavorati',
      minWidth: 170,
      label: 'Provv. Coda di Firma',
      align: 'left',
      disabledSort: true,
    },
    {
      id: 'firmati',
      minWidth: 170,
      label: t('scrivania.provvInviatiCancelleria') as string,
      align: 'left',
      render: renderInvinviatiCancelleria,
      disabledSort: true,
    },
    {
      id: 'pubblicati',
      minWidth: 170,
      label: 'Provv. Pubblicati',
      align: 'left',
      disabledSort: true,
    },
    {
      id: 'azioni',
      minWidth: 10,
      label: t('calendario.azioni') as string,
      align: 'left',
      render: renderAzioni,
      disabledSort: true,
    },
  ];

  return (
    <>
      <Grid container p={3} border={{ border: theme.custom.borders[0] }}>
        {rows?.length > 0 && (
          <RelayTable
            rows={rows}
            columns={columns}
            sorting={true}
            createSortHandler={createSortHandler}
            order={order}
            orderBy={orderBy}
          />
        )}
        {rows?.length === 0 && <>Nessun minuta ...</>}
      </Grid>
      <NsFullPageSpinner isOpen={isLoading} value={1} />
    </>
  );
}
