import { ColumnDef } from '@tanstack/table-core/src/types';

export const getColumnConfig = (
  t: (key: string) => string,
  renderFunctions: any
): ColumnDef<any>[] => {
  const {
    renderCheckbox,
    renderHeadCheckbox,
    renderNrg,
    renderStato,
    renderTipologia,
    renderData,
    renderVerifica,
    renderAzioni,
    renderNumOrdine,
    renderRelatore,
    renderOscuratoSIC,
    renderOscuratoDESKCSP,
  } = renderFunctions;

  return [
    {
      id: 'select',
      header: renderHeadCheckbox,
      cell: renderCheckbox,
    },
    {
      id: 'numOrdine',
      header: t('calendario.ordine').toString(),
      cell: renderNumOrdine,
    },
    {
      id: 'nrg',
      header: t('calendario.nrg').toString(),
      cell: renderNrg,
    },
    {
      id: 'relatore',
      header: t('calendario.estensore').toString(),
      cell: renderRelatore,
    },
    {
      id: 'oscuratoSIC',
      header: t('calendario.oscuramentoSic').toString(),
      cell: renderOscuratoSIC,
    },
    {
      id: 'oscuratoDESKCSP',
      header: t('calendario.oscuramentoDeskCSP').toString(),
      cell: renderOscuratoDESKCSP,
    },
    {
      id: 'stato',
      header: t('calendario.stato').toString(),
      cell: renderStato,
    },
    {
      id: 'tipologia',
      header: t('calendario.tipologia').toString(),
      cell: renderTipologia,
    },
    {
      id: 'data',
      header: t('calendario.dataDeposito').toString(),
      cell: renderData,
    },
    {
      id: 'verificato',
      header: t('calendario.verificato').toString(),
      cell: renderVerifica,
    },
    {
      id: 'azioni',
      header: t('calendario.azioni').toString(),
      cell: renderAzioni,
    },
  ];
};
