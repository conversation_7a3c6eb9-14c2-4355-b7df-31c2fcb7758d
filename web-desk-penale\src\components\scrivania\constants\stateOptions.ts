// constants/stateOptions.ts
import { FilterOption } from '../../../types/types';
import { StatoProvvedimentiEnum } from '../../../types/types';

export const getStateOptions = (t: (key: string) => string, userRole?: string[] | null): FilterOption[] => {
  const allOptions: FilterOption[] = [
  { KeyName: t('scrivania.verificaProvvedimento.tutti'), value: 'TUTTI' },
  { KeyName: t('scrivania.verificaProvvedimento.inBozza'), value: StatoProvvedimentiEnum.IN_BOZZA },
  {
    KeyName: t('scrivania.verificaProvvedimento.minutaPervenuta'),
    value: StatoProvvedimentiEnum.MINUTA_ACCETTATA,
  },
  {
    KeyName: t('scrivania.verificaProvvedimento.minutaDaModificare'),
    value: StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE,
  },
  {
    KeyName: t(
      'scrivania.verificaProvvedimento.inviatoInCancelDalPresidente'
    ),
    value: StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE,
  },
  {
    KeyName: t('scrivania.verificaProvvedimento.bozzaMinutamodificata'),
    value: StatoProvvedimentiEnum.BOZZA_PRESIDENTE,
  },
  {
    KeyName: t(
      'scrivania.verificaProvvedimento.minutaModificataInoltrataEstensore'
    ),
    value: StatoProvvedimentiEnum.MINUTA_MODIFICATA_PRESIDENTE,
  },
  {
    KeyName: t('scrivania.verificaProvvedimento.inCodaDiFirma'),
    value: StatoProvvedimentiEnum.IN_CODE_FIRMA_REL,
  },
  {
    KeyName: t('scrivania.verificaProvvedimento.inviatoInCancelleria'),
    value: StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE,
  },
  {
    KeyName: t('scrivania.verificaProvvedimento.bustaRifiutata'),
    value: StatoProvvedimentiEnum.BUSTA_RIFIUTATA,
  },
  {
    KeyName: t('scrivania.verificaProvvedimento.bustaRifiutataAlPresidente'),
    value: StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE,
  },
  {
    KeyName: t('scrivania.verificaProvvedimento.pubblicato'),
    value: StatoProvvedimentiEnum.PUBBLICATA,
  },
];

  // Se sono presidente, rimuovo le opzioni che non deve vedere
  if (userRole && userRole.includes('PRESIDENTE')) {
    return allOptions.filter(option => {
      // Rimuovo "Bozza" (IN_BOZZA)
      if (option.value === StatoProvvedimentiEnum.IN_BOZZA) {
        return false;
      }
      // Rimuovo "Busta Rifiutata al Presidente" (BUSTA_RIFIUTATA_AL_PRESIDENTE)
      if (option.value === StatoProvvedimentiEnum.BUSTA_RIFIUTATA_AL_PRESIDENTE) {
        return false;
      }
      // Rimuovo "Inviato in Cancelleria dal Presidente" ma mantengo "Inviato in Cancelleria"
      if (option.value === StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE &&
          option.KeyName === t('scrivania.verificaProvvedimento.inviatoInCancelDalPresidente')) {
        return false;
      }
      return true;
    });
  }

  return allOptions;
};
