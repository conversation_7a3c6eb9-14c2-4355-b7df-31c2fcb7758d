// BreadcrumbHeader.tsx
import React from 'react';
import { Typography } from '@mui/material';
import { NsBreadcrumbs } from '@netservice/astrea-react-ds';
import { BreadcrumbHeaderProps } from '../../../types/types';
import { formatDate } from '../../shared/Utils';

const BreadcrumbHeader: React.FC<BreadcrumbHeaderProps> = ({ udienza, t }) => {
  const getTitleBreadcrumb = () => {
    if (!udienza) return '';

    return `${t('scrivania.udienza')}: 
            ${udienza.sezione} - 
            ${formatDate(udienza.dataUdienza, 'DD MMMM YYYY')} - 
            ${udienza.tipoUdienza} - 
            ${t('scrivania.collegio')}: 
            ${udienza.aula}`;
  };

  return (
    <Typography variant="h2">
      <NsBreadcrumbs
        linkItems={[
          {
            href: '/scrivania',
            name: t('scrivania.minuteDaVerificare')
          }
        ]}
        linkUnderline="always"
        title=''
      />
      {getTitleBreadcrumb()}
    </Typography>
  );
};

export default BreadcrumbHeader;
