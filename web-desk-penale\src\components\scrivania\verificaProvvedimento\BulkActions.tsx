// BulkActions.tsx
import React from 'react';
import { Grid, Button } from '@mui/material';
import { BulkActionsProps } from '../../../types/types';

const BulkActions: React.FC<BulkActionsProps> = ({
                                                   selected,
                                                   onVerificaSelezionati,
                                                   disabled,
                                                   t
                                                 }) => {
  return (
    <Grid item mt={1} container justifyContent="flex-start">
      <Button
        variant="contained"
        color="primary"
        disabled={disabled || !(selected && selected?.length > 0)}
        onClick={onVerificaSelezionati}
      >
        {t('scrivania.verificaProvvedimento.verificaSelezionati')}
      </Button>
    </Grid>
  );
};

export default BulkActions;
