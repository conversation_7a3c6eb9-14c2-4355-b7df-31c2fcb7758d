// ProvvedimentiPreview.tsx
import React from 'react';
import { Box, Grid } from '@mui/material';
import { ProvvedimentiPreviewProps } from '../../../types/types';
import VerificaEditor from '../VerificaEditor';
import PdfPreview from '../PdfPreview';
import BulkPdfPreview from '../BulkPdfPreview';

const ProvvedimentiPreview: React.FC<ProvvedimentiPreviewProps> = ({
  showPdfPreview,
  showBulkPdfPreview,
  selectedProvv,
  selected,
  nrg,
  idUdienza,
  refreshCoda,
  setRefreshCoda,
  refreshPage,
  refreshTableOnly,
}) => {
  const theme = { custom: { borders: ['1px solid rgba(0, 0, 0, 0.12)'] } };

  return (
    <Grid container p={2} mb={5} item xs={12}>
      <Box
        border={theme.custom.borders[0]}
        display="flex"
        justifyContent="center"
        bgcolor="#E0EEEC"
        width="100%"
      >
        {showPdfPreview && selectedProvv ? (
          <PdfPreview
            autoScroll={true}
            provv={selectedProvv}
            setRefreshCoda={() => setRefreshCoda(!refreshCoda)}
            refreshPage={refreshPage}
            idUdienza={idUdienza}
            fascicolo={{
              nrg: nrg,
              idUdienza: idUdienza,
            }}
          />
        ) : !showBulkPdfPreview ? (
          <VerificaEditor />
        ) : null}

        {showBulkPdfPreview && selected && selected.length > 0 && (
          <Grid container justifyContent="center">
            <BulkPdfPreview
              provv={selected}
              refreshCoda={refreshCoda}
              setRefreshCoda={setRefreshCoda}
              refreshPage={refreshPage}
              refreshTableOnly={refreshTableOnly}
            />
          </Grid>
        )}
      </Box>
    </Grid>
  );
};

export default ProvvedimentiPreview;
