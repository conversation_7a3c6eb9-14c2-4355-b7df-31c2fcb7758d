import React, {useState, forwardRef, ForwardedRef, useCallback, useMemo} from 'react';
import {
  Grid,
  FormControl,
  MenuItem,
  Select,
  Typography,
  useTheme
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { createTableRenderers } from './TableRenderers';
import { getColumnConfig } from '../constants/columnConfig';
import { getStateOptions } from '../constants/stateOptions';
import { ProvvedimentiTableProps, ProvvedimentiTableRef } from '../../../types/types';
import { userRole } from '../../shared/Utils';
import { useGetRuolo } from '../../shared/GetRuolo';
import {usePagination} from "relay-hooks";
import {datiProvvedimentoScrivaniaFragment} from "../../relay/relayQueryScrivaniaFragment";
import {
  relayQueryScrivaniaFragment_datiProvvedimentoScrivania$key
} from "@/generated/relayQueryScrivaniaFragment_datiProvvedimentoScrivania.graphql";
import {
  relayQueryScrivaniaFragment_PenaleUdienzaQuery
} from "@/generated/relayQueryScrivaniaFragment_PenaleUdienzaQuery.graphql";
import { NsDataGridVirtualizedInfiniteScrolling, RelayLoadMoreOptions } from 'src/components/shared/NsDataGridVirtualizedInfiniteScrolling';
import { ProvvedimentiStatoEnum } from '@/generated/relayQueriesFascicoloDetails_FascicoloDetailsQuery.graphql';

const ProvvedimentiTable = forwardRef<ProvvedimentiTableRef, ProvvedimentiTableProps>(
  (props, forwardedRef) => {
    const {
      queryFragment,
      selected,
      onSelect,
      onRowClick,
      onViewClick,
      onModalOpen,
      idUdienza
    } = props;

    const { t } = useTranslation();
    const theme: any = useTheme();
    const getRuolo = useGetRuolo();
    const ruolo = userRole() ?? getRuolo();

    // Espone il metodo refreshData tramite ref
    React.useImperativeHandle(forwardedRef, () => ({
      refreshData: () => {
        // Resetta la query per aggiornare i dati
        refetch({
          idUdienza: idUdienza,
          first: 20,
          after: null,
          before: null,
          last: null,
          status: selectedOption === 'TUTTI' ? null : selectedOption as ProvvedimentiStatoEnum
        });
      }
    }));

    // Stati spostati da VerificaProvvedimento
    const [selectedOption, setSelectedOption] = useState<string | null>('TUTTI');
    const {
      data: fragmentData,
      error,
      loadNext,
      hasNext,
      isLoadingNext,
      refetch,
      isLoading: isLoadingFascicoli,
    } = usePagination<
      relayQueryScrivaniaFragment_PenaleUdienzaQuery,
      relayQueryScrivaniaFragment_datiProvvedimentoScrivania$key
    >(
      datiProvvedimentoScrivaniaFragment,
      queryFragment
    );

    const queryVariables: any =  {
      idUdienza: idUdienza,
      first: 20,
      after: null,
      before: null,
      last: null,
      status: null
    }
    const fascicoli = useMemo(() => fragmentData?.provvedimentiScrivania?.edges ? [...fragmentData.provvedimentiScrivania.edges.map(res => res.node)] : [], [fragmentData]);
    const totalRowCount = fragmentData.provvedimentiScrivania?.aggregate.totalElement || 0;
    const handleLoadMore = useCallback((options: RelayLoadMoreOptions) => {
      console.log(`Caricamento pagina ${options.pageParam} con fetchSize ${options.fetchSize}`);
      // Chiama loadNext di Relay
      loadNext(options.fetchSize);
    }, [loadNext]);

    // Handlers
    const handleClick = (
      event: React.MouseEvent<unknown>,
      index: number,
      row: any
    ) => {
      const array = selected.includes(row.idProvvedimento)
        ? selected.filter((id) => id !== row.idProvvedimento)
        : [...selected, row.idProvvedimento];

      onSelect(array);

      if (typeof onRowClick === 'function') {
        onRowClick(row);
      }
    };

    const handleSelectAllClick = (
      rows: any
    ) => {
      const selectedRows = rows.filter(
        (row: any) => row.original.stato === 'MINUTA_ACCETTATA'
      );
      const allIndexes = selectedRows.map((row: any) => row.original.idProvvedimento);
      let newSelected = [...selected];

      if (allIndexes.length > selected.length) {
        allIndexes.forEach((i: any) => {
          const isSelected = selected.includes(i);
          if (!isSelected) {
            newSelected.push(i);
          }
        });

        onSelect(newSelected);
      } else {
        onSelect([]);
      }
    };


    // Handle cambio opzione del filtro
    const handleSelectedOption = (e: any) => {
      setSelectedOption(e.target.value);
      queryVariables.status = !e.target.value || e.target.value === 'TUTTI'? null : e.target.value as ProvvedimentiStatoEnum;
      refetch(queryVariables);
    };

    // Creazione dei renderer per la tabella
    const tableRenderers = createTableRenderers({
      theme,
      t,
      handleViewClick: onViewClick,
      handleModal: onModalOpen,
      selected,
      handleClick,
      handleSelectAllClick,
      data: fascicoli,
    });

    // Ottieni la configurazione delle colonne
    const columns = getColumnConfig(t, tableRenderers);

    // Ottieni le opzioni di stato per il filtro
    const stateOptions = getStateOptions(t, ruolo);

    return (
      <Grid container justifyContent="center" item>
        <Grid mb={5} p={5} border={theme.custom.borders[0]} item xs={12}>
          <Grid container justifyContent="flex-end" item xs={12} mt={2} mb={2}>
            <Grid item xs={2}>
              <Typography variant="h4">{t('intestazioni.stato')}</Typography>
              <FormControl fullWidth>
                <Select value={selectedOption} onChange={handleSelectedOption}>
                  {stateOptions.map((option, index) => (
                    <MenuItem key={`${option.value}-${index}`}  value={option.value}>
                      {option.KeyName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          <div style={{
            width: '100%',
            position: 'relative',
            height: '350px'
          }}>
            {fascicoli && <div className={"provvedimenti-verifica"} ><NsDataGridVirtualizedInfiniteScrolling
              columns={columns}
              mode="relay" // Aggiungi questa proprietà
              data={fascicoli}
              totalRowCount={totalRowCount}
              onLoadMore={handleLoadMore}
              isLoading={isLoadingNext}
              hasMore={hasNext}
              containerHeight="350px"
              estimatedRowHeight={50}
              enableColumnGrouping={true}
              fetchSize={20}
            /></div>}
          </div>
        </Grid>
      </Grid>
    );
  }
);

// Aggiungiamo un displayName esplicito
ProvvedimentiTable.displayName = 'Dettaglio Scrivania';

export default ProvvedimentiTable;
