import CheckIcon from '@mui/icons-material/Check';
import { Box, Checkbox, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { azioniButton, formatDate, getStateNames } from '../../shared/Utils';
import { ProvvedimentiTipoEnum, StatoProvvedimentiEnum } from '../../../types/types';
import { Row } from '@tanstack/react-table';

interface TableRenderersConfig {
  theme: any;
  t: (key: string) => string;
  handleViewClick: (provvId: string, nrg: any, actionId: number) => void;
  handleModal: (param: string, data?: any) => void;
  selected: string[];
  handleClick: (
    event: React.MouseEvent<unknown>,
    index: number,
    row: any
  ) => void;
  handleSelectAllClick: (rows: any[]) => void;
  data: any[];
}
const checkboxStyle = {
  '& .MuiSvgIcon-root': {
    width: '24px',
    height: '24px',
    color: 'black !important',
  },
  padding: 0,
  marginLeft: '-3px',
};
// Definisci un'interfaccia per il tipo di cella
interface CellProps {
  row: Row<any>;
}
export const createTableRenderers = ({
  theme,
  t,
  handleViewClick,
  handleModal,
  selected,
  handleClick,
  handleSelectAllClick,
  data,
}: TableRenderersConfig) => {
  const azioni = azioniButton();

  // Modifica le funzioni di rendering per accettare { row } come parametro
  const renderVerifica = ({ row }: CellProps) => (
    <Box>
      {row.original.stato === StatoProvvedimentiEnum.CODA_DI_FIRMA && (
        <CheckIcon color="success" />
      )}
    </Box>
  );

  const renderAzioni = ({ row }: CellProps) => (
    <Box display="center" alignItems="center">
      {azioni.getIcons('view', 'preview').map((icon: any) => (
        <Box
          key={icon.id}
          sx={{ ...azioni.iconBoxStyles, background: icon.bgColor }}
          onClick={() => {
            handleViewClick(
              row.original.idProvvedimento,
              row.original.nrg,
              icon.id
            );
          }}
        >
          {icon.icon}
        </Box>
      ))}
    </Box>
  );

  const renderData = ({ row }: CellProps) => (
    <>{formatDate(row.original.dataDeposito, 'DD/MM/YYYY HH:mm')}</>
  );

  const renderStato = ({ row }: CellProps) => (
    <>{getStateNames('PRESIDENTE', row.original.stato)}</>
  );

  const renderNrg = ({ row }: CellProps) => {
    const handleClickRiunito = () => {
      handleModal('riuniti', row.original);
    };
    const handleClickVediRiuniti = () => {
      handleModal('vediRiuniti', row.original);
    };
    return (
      <Box
        sx={{ display: 'flex', alignItems: 'center', maxWidth: '170px' }}
        className={'ns-display-2col'}
      >
        <Box>
          <Typography variant="body2" color="text.primary">
            {row.original.nrgFormat}
          </Typography>
        </Box>
        <Box>
          {row.original.riunitoView?.isPrincipalRicorsoRiunito && (
            <NsButton
              onClick={handleClickVediRiuniti}
              sx={theme.custom.secondaryButton}
            >
              {' '}
              {t('common.principale')}{' '}
            </NsButton>
          )}
          {row.original.riunitoView?.ricorsoRiunito && (
            <NsButton
              onClick={handleClickRiunito}
              sx={theme.custom.secondaryButton}
            >
              {' '}
              (R){' '}
            </NsButton>
          )}
        </Box>
      </Box>
    );
  };

  const renderTipologia = ({ row }: CellProps) => (
    <>
      {row.original.tipologia === ProvvedimentiTipoEnum.MINUTA_SENTENZA && (
        <Box>{t('scrivania.verificaProvvedimento.minutaDiSentenza')}</Box>
      )}
      {row.original.tipologia === ProvvedimentiTipoEnum.MINUTA_ORDINANZA && (
        <Box> {t('scrivania.verificaProvvedimento.minutaDiOrdinanza')} </Box>
      )}
      {row.original.tipologia === ProvvedimentiTipoEnum.SENTENZA && (
        <Box> {t('scrivania.verificaProvvedimento.sentenza')} </Box>
      )}
      {row.original.tipologia === ProvvedimentiTipoEnum.ORDINANZA && (
        <Box> {t('scrivania.verificaProvvedimento.ordinanza')} </Box>
      )}
    </>
  );

  const isCheckbox = (row: any) =>
    getStateNames('PRESIDENTE', row?.stato) === 'Minuta pervenuta';

  const getSelectableRows = () => data.filter((row: any) => isCheckbox(row));

  const areAllCheckboxesSelected = () => {
    const _selectableRows = getSelectableRows();
    return (
      _selectableRows.length > 0 &&
      _selectableRows.every((row: any) =>
        selected.includes(row.idProvvedimento)
      )
    );
  };

  const renderCheckbox = ({ row }: CellProps) => (
    <>
      {isCheckbox(row.original) && (
        <Checkbox
          color="primary"
          checked={selected.includes(row.original.idProvvedimento)}
          onClick={(event) => handleClick(event, row.index, row.original)}
        />
      )}
    </>
  );

  const renderHeadCheckbox = ({ table }: any) => (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      width="100%"
      height="100%"
      marginLeft={1}
    >
      <Checkbox
        color="primary"
        checked={areAllCheckboxesSelected()}
        onClick={() => handleSelectAllClick(table.getFilteredRowModel().rows)}
        inputProps={{
          'aria-label': 'select all',
        }}
        sx={checkboxStyle}
      />
    </Box>
  );

  const renderNumOrdine = ({ row }: CellProps) => (
    <Typography variant="body2" color="text.primary">
      {row.original.numOrdine}
    </Typography>
  );

  const renderRelatore = ({ row }: CellProps) => (
    <Typography variant="body2" color="text.primary">
      {row.original.relatore}
    </Typography>
  );

  const renderOscuratoSIC = ({ row }: CellProps) => (
    <Typography variant="body2" color="text.primary">
      {row.original.oscuratoSIC}
    </Typography>
  );

  const renderOscuratoDESKCSP = ({ row }: CellProps) => (
    <Typography variant="body2" color="text.primary">
      {row.original.oscuratoDESKCSP}
    </Typography>
  );

  return {
    renderVerifica,
    renderAzioni,
    renderData,
    renderStato,
    renderNrg,
    renderTipologia,
    renderCheckbox,
    renderHeadCheckbox,
    renderNumOrdine,
    renderRelatore,
    renderOscuratoSIC,
    renderOscuratoDESKCSP,
  };
};
