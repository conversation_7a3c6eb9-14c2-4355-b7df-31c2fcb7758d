// UdienzaInfo.tsx
import React from 'react';
import { Box, Grid, Typography } from '@mui/material';
import { UdienzaInfoProps, SezioneEnum, TipoUdienzaEnum } from '../../../types/types';
import { formatDate } from '../../shared/Utils';
import { sezioni } from '../constants/sezioni';

const UdienzaInfo: React.FC<UdienzaInfoProps> = ({ udienza, t }) => {
  if (!udienza) return null;

  return (
    <Grid container item xs={12}>
      <Grid item xs={12}>
        <Typography variant="h1">
          {t('scrivania.udienza')} {t('scrivania.del')}{' '}
          {formatDate(udienza.dataUdienza, 'DD/MM/YYYY')}{' '}
        </Typography>
      </Grid>
      <Grid container mt={2} item xs={5}>
        <Box
          mr={1}
          p={0.5}
          sx={{
            backgroundColor: '#BDC5C7',
            fontWeight: 700,
            borderRadius: '5px',
          }}
        >
          {t('scrivania.chiusa')}
        </Box>

        {/* Colore indicatore sezione */}
        {(udienza.sezione === SezioneEnum.S1 ||
          udienza.sezione === SezioneEnum.S2 ||
          udienza.sezione === SezioneEnum.S3 ||
          udienza.sezione === SezioneEnum.S4 ||
          udienza.sezione === SezioneEnum.S5 ||
          udienza.sezione === SezioneEnum.S6) && (
          <Box
            mr={0.5}
            sx={{
              width: 20,
              height: 20,
              padding: 0.5,
              bgcolor: '#068E87',
              marginRight: 1,
              borderRadius: '50%',
            }}
          />
        )}

        {udienza.sezione === SezioneEnum.S7 && (
          <Box
            mr={0.5}
            sx={{
              width: 20,
              height: 20,
              padding: 0.5,
              bgcolor: '#2852B5',
              marginRight: 1,
              borderRadius: '50%',
            }}
          />
        )}

        {udienza.sezione === SezioneEnum.SU && (
          <Box
            mr={0.5}
            sx={{
              width: 20,
              height: 20,
              padding: 0.5,
              bgcolor: '#2852B5',
              marginRight: 1,
              borderRadius: '50%',
            }}
          />
        )}

        {/* Nome sezione */}
        <Box>&nbsp;{sezioni[udienza.sezione]} | </Box>

        {/* Tipo udienza */}
        {udienza.tipoUdienza === 'CC' ? (
          <Box mr={1}>&nbsp;{TipoUdienzaEnum.CC} | </Box>
        ) : (
          <Box mr={1}>&nbsp;{TipoUdienzaEnum.PU} | </Box>
        )}

        {/* Collegio */}
        <Box mr={1}>
          {t('scrivania.collegio')} {udienza.aula}{' '}
        </Box>
      </Grid>
    </Grid>
  );
};

export default UdienzaInfo;
