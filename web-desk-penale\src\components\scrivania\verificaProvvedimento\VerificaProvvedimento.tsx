// VerificaProvvedimento.tsx
import {Box, Grid, Typography} from '@mui/material';
import { NsFullPageSpinner, useNotifier } from '@netservice/astrea-react-ds';
import axios from 'axios';
import { useRouter } from 'next/router';
import {useEffect, useState, useRef, useMemo} from 'react';
import { useTranslation } from 'react-i18next';
import { STORE_THEN_NETWORK, useQuery } from 'relay-hooks';
import { VerificaProvvedimentoProps, Udienza, Provvedimento, ProvvedimentiTableRef } from '../../../types/types';
import CodaButton from '../../shared/CodaButton';
import { PenaleUdienza } from "../../relay/relayQueryScrivaniaFragment";
import { relayQueryScrivaniaFragment_PenaleUdienzaQuery } from "@/generated/relayQueryScrivaniaFragment_PenaleUdienzaQuery.graphql";
import DatiUdienza from '../../shared/DatiUdienza';
import DialogModal from '../../shared/DialogModal';

// Componenti ristrutturati
import BreadcrumbHeader from './BreadcrumbHeader';
import UdienzaInfo from './UdienzaInfo';
import ProvvedimentiTable from './ProvvedimentiTable';
import BulkActions from './BulkActions';
import ProvvedimentiPreview from './ProvvedimentiPreview';
import {useConfig} from "../../shared/configuration.context";
import DetailRiuniti from "../../calendar/components/DetailRiuniti";

export default function VerificaProvvedimento({
                                                onRowClick,
                                              }: VerificaProvvedimentoProps) {
  // Stato del componente - ora ridotto dopo lo spostamento
  const [selected, setSelected] = useState<string[]>([]);
  const [filteredFascicoli, setFilteredFascicoli] = useState<Provvedimento[]>([]);
  const [showPdfPreview, setShowPdfPreview] = useState<boolean>(false);
  const [showBulkPdfPreview, setShowBulkPdfPreview] = useState<boolean>(false);
  const [selectedProvv, setSelectedProvv] = useState<string | null>(null);
  const [refreshCoda, setRefreshCoda] = useState<boolean>(false);

  // Hooks e utility
  const { t } = useTranslation();
  const router = useRouter();
  const { servizi } = useConfig();
  const serviceUrl2 = `${servizi}`;
  const idUdienza = Number(router.query.id);
  const { notify } = useNotifier();

  // Riferimento alla tabella per poter chiamare il refresh
  const tableRef = useRef<ProvvedimentiTableRef>(null);

  // Modal state
  const closeModal = (
    _event: React.MouseEvent<HTMLButtonElement>,
    reason: string
  ) => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    title: '',
    content: <DatiUdienza />,
    openFromParent: true,
    onClose: closeModal,
  });
  const queryVariables = useMemo(() => {
    return {
      idUdienza: idUdienza,
      first: 20,
      after: null,
      before: null,
      last: null,
      status: null
    };
  }, [idUdienza]);

  // Query del componente
  const { data: udienzaData, isLoading } = useQuery<relayQueryScrivaniaFragment_PenaleUdienzaQuery>(
    PenaleUdienza,
    queryVariables,
    {
      fetchPolicy: STORE_THEN_NETWORK,
    }
  );

  // Handler per il modal
  const handleModal = (param: string, data?: any) => {
    let content, title;
    if (param == 'riuniti') {
      content = (
        <Typography p={1} border={1} variant="body2" color="text.primary">
          {data.riunitoView.ricorsoRiunito.numero}/
          {data.riunitoView.ricorsoRiunito.anno}
        </Typography>
      );
      title = t('calendario.calendarioTable.riunitoAl');
    } else if (param == 'vediRiuniti') {
      content = (
        <DetailRiuniti idRicorsoUdienza={data?.idRicUdien}></DetailRiuniti>
      );
      title = t('fascicolo.fascicoliRiuniti');
    } else {
      title = ``;
      content = <></>;
    }

    setModalProps({
      ...modalProps,
      content: content!,
      isOpen: true,
      title: title ?? '',
    });
  };

  // Funzione per visualizzare anteprima PDF
  const previewPdfByIdProvv = (provv: string) => {
    setShowPdfPreview(false);
    setShowBulkPdfPreview(false);

    axios
      .get(serviceUrl2 + '/provvedimento/getProvvedimentiDaFirmare/' + provv)
      .then((response: any) => {
        console.log('getprovvedimentidafirmare', response);
        const provvedimentiDaFirmare = response.data;
        const fileName = provvedimentiDaFirmare.find(
          (item: any) => item.idProvvedimento == provv && item.tipoFile == 'pdf'
        )?.nomeFile;
        axios
          .get(serviceUrl2 + '/provvedimento/downloadPdfByIdProvv/' + provv, {
            responseType: 'blob',
          })
          .then((response: any) => {
            console.log('response', response);
            const pdfBlob = new Blob([response.data], {
              type: 'application/' + 'pdf',
            });
            const pdfUrl = window.URL.createObjectURL(pdfBlob);
            setSelectedProvv(provv);
            setShowPdfPreview(true);
          })
          .catch((error: any) => {
            console.log('error', error);
            notify({
              message: 'Errore nel download del pdf',
              type: 'error',
            });
          });
      })
      .catch((error: any) => {
        console.log('error', error);
        notify({
          message: 'Errore nel download del pdf',
          type: 'error',
        });
      });
  };

  const [selectedNrg, setSelectedNrg] = useState<any>(null);

  // Handler per le azioni della tabella
  const handleViewClick = (provv: string, nrg: any, id: number) => {
    setSelectedNrg(nrg);
    switch (id) {
      case 5:
        previewPdfByIdProvv(provv);
        break;
      case 1:
        router.push(`/scrivania/fascicolo/${idUdienza}/?params=${nrg}`);
        break;
      default:
        break;
    }
  };

  // Handler per la verifica dei selezionati
  const verificaSelezionati = () => {
    setShowPdfPreview(false);
    setShowBulkPdfPreview(true);
  };

  const refreshData = () => {
    console.log("Refreshing data and closing previews...");

    // Prima chiudi la preview
    setShowPdfPreview(false);
    setShowBulkPdfPreview(false);

    // Dopo refresha i dati della tabella per vedere lo stato aggiornato
    if (tableRef.current) {
      tableRef.current.refreshData();
    }

    // Forza un aggiornamento dello stato per assicurarsi che la UI si aggiorni
    setRefreshCoda(prev => !prev);

    // Pulisci gli item selezionati dopo il verificato
    setSelected([]);
  };

  // Funzione per aggiornare solo la tabella senza chiudere la preview
  const refreshTableOnly = () => {
    // Aggio la tabella per mostrare lo stato aggiornato del verificato
    console.log("Refreshing table only...");
    if (tableRef.current) {
      // Forza un aggiornamento completo della tabella
      tableRef.current.refreshData();

      // Aggiorna lo stato locale per forzare un re-render
      setRefreshCoda(prev => !prev);
    }
  };

  // Preparazione dell'oggetto udienza per i componenti figli
  const udienza: Udienza | undefined = udienzaData?.penaleUdienzaByIdUdienza ? {
    idUdienza: idUdienza,
    dataUdienza: udienzaData.penaleUdienzaByIdUdienza.dataUdienza,
    sezione: udienzaData.penaleUdienzaByIdUdienza.sezione as any,
    tipoUdienza: udienzaData.penaleUdienzaByIdUdienza.tipoUdienza,
    aula: udienzaData.penaleUdienzaByIdUdienza.aula || '' // Se null, imposta come stringa vuota
  } : undefined;

  // Rendering del componente
  return isLoading ? (
    <NsFullPageSpinner isOpen={true} value={1} />
  ) : (
    <>
      <DialogModal {...modalProps} />

      {/* Header con breadcrumb e bottone coda */}
      <Grid
        xs={12}
        mb={4}
        pl={2}
        pt={2}
        item
        display="flex"
        justifyContent="space-between"
        alignItems="center"
      >
        <BreadcrumbHeader udienza={udienza} t={t} />
        <Box>
          <CodaButton
            refreshCoda={refreshCoda}
            refreshPage={refreshData}
            ruolo={'PRESIDENTE'}
          />
        </Box>
      </Grid>

      {/* Informazioni udienza */}
      <Grid container item pl={2} mb={4}>
        <UdienzaInfo udienza={udienza} t={t} />
      </Grid>

      {/* Tabella provvedimenti con filtro - ora contiene la logica dei fascicoli */}
      <ProvvedimentiTable
        queryFragment={udienzaData}
        ref={tableRef}
        idUdienza={idUdienza}
        selected={selected}
        onSelect={setSelected}
        onRowClick={onRowClick}
        onViewClick={handleViewClick}
        onModalOpen={handleModal}
      />

      {/* Bulk Actions */}
      <Grid p={2} container justifyContent="center" item>
        <BulkActions
          selected={selected}
          onVerificaSelezionati={verificaSelezionati}
          disabled={false}
          t={t}
        />
      </Grid>

      {/* Preview dei provvedimenti */}
      <ProvvedimentiPreview
        showPdfPreview={showPdfPreview}
        showBulkPdfPreview={showBulkPdfPreview}
        selectedProvv={selectedProvv}
        selected={selected}
        nrg={selectedNrg}
        idUdienza={idUdienza}
        refreshCoda={refreshCoda}
        setRefreshCoda={(value) => setRefreshCoda(value)}
        refreshPage={refreshData}
        refreshTableOnly={refreshTableOnly}
      />
    </>
  );
}
