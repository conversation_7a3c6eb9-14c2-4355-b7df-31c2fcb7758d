import {ApiLiberoCreazioneProvvedimentoMutation} from '@/generated/ApiLiberoCreazioneProvvedimentoMutation.graphql';
import {useNotifier} from '@netservice/astrea-react-ds';
import axios from 'axios';
import {useRouter} from 'next/router';
import {useTranslation} from 'react-i18next';
import {useMutation} from 'relay-hooks';
import {graphql} from 'relay-runtime';
import {EditorAction, EditorQueryParams, SalvaBozzaProps} from "../editor/editor.interfaces";
import {useConfig} from "./configuration.context";

const mutationImport = graphql`
  mutation ApiLiberoCreazioneProvvedimentoMutation(
    $input: CreateProvvLavorazioneInput!
  ) {
    GenerazioneProvvedimentoCreateMutation(
      createProvvLavorazioneInput: $input
    ) {
      idProvvedimento
    }
  }
`;

export const Api = (): any => {
  const [mutateChange] =
    useMutation<ApiLiberoCreazioneProvvedimentoMutation>(mutationImport);
  const { t } = useTranslation();
  const router = useRouter();
  const { notify } = useNotifier();
  const {servizi} = useConfig();
  const serviceUrlAndGeneratePdf = `${servizi}/provvedimento/saveEditorAndGeneraDocx/`;
  const salvaBozzaUrl = `${servizi}/provvedimento/saveBozzaDocx/`;
  function redirectAction(salvaBozza:SalvaBozzaProps) {
    let redirectPath: string = '';
    let queryParam : EditorQueryParams =  {
      params: salvaBozza.params,
    };

    switch (salvaBozza.action) {
      case EditorAction.CODA_FIRMA:
        notify({
          message: t('editor.strutturaEditor.aggiuntoAllaCodaDiDeposito'),
          type: 'success',
        });
        redirectPath = '/calendario';
        break;
      case EditorAction.FIRMA_DEPOSITA:
        redirectPath = '/firmadeposita';
        queryParam = {...queryParam,
          idUdienza: salvaBozza.idUdienza,
          tipoProvvedimento: salvaBozza.tipologiaProvvedimento,
          idProvvedimento:salvaBozza.idProvvedimento};
        break;
      case EditorAction.REDIRECT_EDITOR:
        redirectPath = '/editor';
        queryParam = {...queryParam,
          idUdienza: salvaBozza.idUdienza,
          tipoProvvedimento: salvaBozza.tipologiaProvvedimento,
          idProvvedimento:salvaBozza.idProvvedimento};
        if (salvaBozza?.edit) {
          queryParam.edit = salvaBozza?.edit;
        }
        if (salvaBozza?.IRP) {
          queryParam.IRP = salvaBozza?.IRP;
        }
        break;
      case EditorAction.MODIFICA_DIRETTA_PRESIDENTE:
        // non eseguo nulla
      default:
        return;

    }
    router.push({
      pathname: redirectPath,
      query: queryParam,
    });
  }
  const currentUrl = (action: EditorAction | undefined| null) => {
    switch (action){
      case EditorAction.FIRMA_DEPOSITA:
      case EditorAction.CODA_FIRMA:
        return serviceUrlAndGeneratePdf;
      case EditorAction.REDIRECT_EDITOR:
      default:
        return salvaBozzaUrl;
    }
  }

  const CallSalvataggioBozza =  async (salvaBozza: SalvaBozzaProps) => {
    const urlCall = currentUrl(salvaBozza?.action)+ `${salvaBozza.idProvvedimento}`
    const result = await axios
      .post(urlCall , {
        nrg: salvaBozza?.nrg || salvaBozza.params,
        origine: salvaBozza.origine,
        idUdienza: salvaBozza?.idUdienza,
        strutturato: salvaBozza.strutturato,
        addCodeFirma: salvaBozza.action === EditorAction.CODA_FIRMA,
        argsProvvedimento: {
          tipologiaProvvedimento: salvaBozza.tipologiaProvvedimento,
          anRuolo: salvaBozza.anRuolo,
          numRuolo: salvaBozza.numRuolo,
          text: salvaBozza.text,
          introduzione: salvaBozza.introduzione,
          motivoRicorso: salvaBozza.motivoRicorso,
          finaleDeposito: salvaBozza.finaleDeposito,
          textOscurato: salvaBozza.textOscurato,
          generaOscurato: salvaBozza.generaOscurato,
          dataDecisione: salvaBozza.dataDecisione,
          pqm: salvaBozza.pqm,
        },
      })
      .then((res) => {
        console.log('res', res);
        notify({
          type: 'success',
          message: t('pages.libero.bozzaSalvata'),
        });
        if(salvaBozza.action) {
          redirectAction(salvaBozza);
        }
        return true;
      })
      .catch((err) => {
        console.log('err', err);
        notify({
          type: 'error',
          message: 'Errore nel salvataggio della bozza',
        });
        return false;
      });
    return result;
  }
  const CallCreazioneProvvedimentoESalvaInBozza = async (
    salvaBozza: SalvaBozzaProps
  ) => {
    if (!salvaBozza.idProvvedimento) {
      return mutateChange({
        variables: {
          input: {
            nrg: salvaBozza?.params,
            origine: salvaBozza.origine,
            allegatoOscurato: true,
            idUdienza: salvaBozza?.idUdienza,
            argsProvvedimento: {
              tipologiaProvvedimento: salvaBozza?.tipologiaProvvedimento ?? null,
              anRuolo: salvaBozza.anRuolo,
              numRuolo: salvaBozza.numRuolo,
            },
          },
        },
      })
        .then(async (res: any) => {
          salvaBozza.idProvvedimento =
            res.GenerazioneProvvedimentoCreateMutation.idProvvedimento;
          // qua devo eseguire sicuro il redirect aall'editor avviene la prima creazione del provedimento
          salvaBozza.action = EditorAction.REDIRECT_EDITOR;
          return await CallSalvataggioBozza(salvaBozza)
        })
        .catch((err) => {
          console.log('err', err);
          notify({
            type: 'error',
            message: 'Errore nel salvataggio della bozza',
          });
          return false;
        });
    } else {
      return await CallSalvataggioBozza(salvaBozza)
    }
  };

  return {
    CallCreazioneProvvedimentoESalvaInBozza, CallSalvataggioBozza
  };
};
