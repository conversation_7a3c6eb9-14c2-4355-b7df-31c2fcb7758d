import { Box, Menu, MenuItem, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useState } from 'react';
import { ButtonProps } from 'src/interfaces';

export default function ButtonMenu({
  name,
  menuNames,
  endIcon,
  handleButton,
  disabled,
  disableImport,
}: ButtonProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  return (
    <Box>
      <NsButton
        variant="contained"
        color="primary"
        endIcon={endIcon}
        onClick={handleClick}
        disabled={disabled}
      >
        <Typography variant="h5">{name}</Typography>
      </NsButton>

      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        {menuNames?.map((menu, i) => (
          <MenuItem
            key={i}
            onClick={() => handleButton!(menu.value)}
            disabled={menu.value == 'provvedimenti' ? disableImport : false}
            sx={{
              borderLeft: '1px solid #ccc',
              borderRight: '1px solid #ccc',
            }}
          >
            {menu.title}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
}
