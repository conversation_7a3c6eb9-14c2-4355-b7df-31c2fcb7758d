import { relayQueries_CodaDepositoSchemaQuery } from '@/generated/relayQueries_CodaDepositoSchemaQuery.graphql';
import { Box } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { CodaDepositoSchema } from '../relay/relayQueries';
import SideModal from './SideModal';
import { useTranslation } from 'next-i18next';
import { CodaButtonProps } from 'src/interfaces';

const ROOT_QUERY = CodaDepositoSchema;

const styleNr = {
  background: 'white',
  color: 'green',
  width: '25px',
  height: '25px',
  borderRadius: '50%',
  fontWeight: 800,
  fontSize: '18px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

export default function CodaButton({
  refreshCoda,
  refreshPage,
  ruolo,
}: CodaButtonProps) {
  const { t } = useTranslation();
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);
  const [count, setCount] = useState<number>(-1);

  const router = useRouter();

  const { data, isLoading, retry } =
    useQuery<relayQueries_CodaDepositoSchemaQuery>(
      ROOT_QUERY,
      { role: ruolo },
      {
        fetchPolicy: STORE_OR_NETWORK,
      }
    );

  useEffect(() => {
    retry();
  }, [router.asPath, refreshCoda]);

  useEffect(() => {
    if (data?.codeDepositoByCf) {
      const total = (
        data?.codeDepositoByCf.filter((codeDep) => codeDep.codeDepositoDto) ||
        []
      ).length;

      setCount(total);
    }
  }, [data?.codeDepositoByCf]);

  const openCoda = () => {
    setOpenDrawer(true);
  };

  const closeCoda = () => {
    setOpenDrawer(false);
  };

  const refresh = () => {
    retry();
    if (refreshPage) refreshPage();
  };

  return (
    <>
      <NsButton
        sx={{
          position: 'fixed',
          top: '200px',
          right: '-140px',
          '&:hover': {
            right: '25px',
            transition: 'right 1s',
          },
          display: count === -1 ? 'none' : '',
          zIndex: 1,
        }}
        variant="contained"
        onClick={openCoda}
      >
        <Box sx={styleNr} mr={2}>
          {count}
        </Box>
        {t('shared.codaButton.codaDiDeposito')}
      </NsButton>
      <SideModal
        openDrawer={openDrawer}
        refreshCoda={() => refresh()}
        closeDrawer={() => closeCoda()}
        ruolo={ruolo}
      />
    </>
  );
}
