import { Box, Grid, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { ConfermaProps } from 'src/interfaces';
import { useTranslation } from 'next-i18next';
import { padding } from '@mui/system';

export const ConfirmModal = ({
  body,
  closeModal,
  confirm,
  confirmText,
}: any) => {
  const { t } = useTranslation();
  return (
    <Grid>
      <Typography variant="h5" style={{ padding: '.7em 0' }}>
        {body}
      </Typography>
      <Box display={'flex'} justifyContent={'space-between'} mt={1}>
        <NsButton onClick={closeModal}>{t('shared.conferma.annulla')}</NsButton>
        <NsButton size="small" onClick={confirm} variant="contained">
          {confirmText}
        </NsButton>
      </Box>
    </Grid>
  );
};

export function Conferma({ body, closeModal, confirm }: ConfermaProps) {
  const { t } = useTranslation();
  return (
    <ConfirmModal
      body={body}
      closeModal={closeModal}
      confirm={confirm}
      confirmText={t('shared.conferma.conferma')}
    />
  );
}

export function Prosegui({ body, closeModal, confirm }: ConfermaProps) {
  const { t } = useTranslation();
  return (
    <ConfirmModal
      body={body}
      closeModal={closeModal}
      confirm={confirm}
      confirmText={t('shared.conferma.prosegui')}
    />
  );
}
