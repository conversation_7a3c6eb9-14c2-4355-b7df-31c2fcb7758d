import { Box, Grid, TextField, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

export default function DatiUdienza() {
  const { t } = useTranslation();
  return (
    <Grid
      p={2}
      item
      mr={2}
      sx={{
        background: '#FBFBFB',
        borderTop: '12px solid #E4E4E4',
        height: 'fit-content',
      }}
      xs={12}
    >
      <Box mt={1}>
        <Typography variant="h2">
          {t('shared.datiUdienza.datiUdienza')}
        </Typography>
      </Box>
      <Box mt={2} sx={{ fontSize: '17px', fontWeight: 600 }}>
        {t('common.data')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          sx={{ width: '100%' }}
          value="9 febbraio 2023"
          variant="standard"
        />
      </Box>
      <Box mt={2} sx={{ fontSize: '17px', fontWeight: 600 }}>
        {t('common.info')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          sx={{ width: '100%' }}
          value="Sezione V, CC, Aula S -  Chiusa"
          variant="standard"
        />
      </Box>
      <Box mt={2} sx={{ fontSize: '17px', fontWeight: 600 }}>
        {t('common.data')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          sx={{ width: '100%' }}
          value="20 - Tot peso 80"
          variant="standard"
        />
      </Box>
      <Box mt={2} sx={{ fontSize: '17px', fontWeight: 600 }}>
        {t('common.collegio')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          sx={{ width: '100%' }}
          multiline
          value="20 - Tot peso 80"
          variant="standard"
        />
      </Box>
      <Box mt={2} sx={{ fontSize: '17px', fontWeight: 600 }}>
        {t('shared.datiUdienza.termineDeposito')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          sx={{ width: '100%' }}
          multiline
          value="25 giorni - 6.03.2023"
          variant="standard"
        />
      </Box>
    </Grid>
  );
}
