import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  NsButton,
  NsDialog,
  NsDialogActions,
  NsDialogContent,
  NsDialogTitle,
} from '@netservice/astrea-react-ds';

export default function DialogModal({
  isOpen,
  onClose,
  onConfirm,
  showCancelButton,
  title,
  content,
  maxWidth,
}: any) {
  const { t } = useTranslation();

  return (
    <NsDialog
      maxWidth={maxWidth}
      setOpen={() => onClose('backdropClick')}
      open={isOpen}
    >
      <NsDialogTitle setOpen={() => onClose()} closeButton={t('modal.close')}>
        <Typography variant="h2" sx={{ maxWidth: '80%', wordWrap: 'break-word' }}>
              {title}
        </Typography>
      </NsDialogTitle>
      <NsDialogContent>{content}</NsDialogContent>
      <NsDialogActions
        sx={{
          justifyContent: 'space-between',
          padding: '1em',
        }}
        setOpen={() => onClose()}
      >
        { showCancelButton &&
          <NsButton
            onClick={onClose}
            variant="outlined"
            color="primary"
          >
            {t('modal.buttons.cancel')}
          </NsButton>
        }
        { onConfirm &&
          <NsButton
            onClick={onConfirm}
            variant="contained"
            color="primary"
          >
            {t('modal.buttons.proceed')}
          </NsButton>
        }
      </NsDialogActions>
    </NsDialog>
  );
}
