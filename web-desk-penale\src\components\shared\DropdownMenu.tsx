import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Typography } from '@mui/material';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { NsButton } from '@netservice/astrea-react-ds';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { DropdownMenuProps } from 'src/interfaces';
import { Link } from './Link';

export const DropdownMenu: React.FC<DropdownMenuProps> = ({
  icon,
  title,
  entries,
  color,
  sx,
  fullWidth,
}) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const { t } = useTranslation();

  return (
    <>
      <NsButton
        component={MenuItem}
        id="basic-button"
        sx={{
          ...sx,
          color: { color },
          fontSize: '17px',
          textTransform: 'none',
        }}
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        disableFocusRipple={true}
        onClick={handleClick}
        fullWidth={fullWidth}
        startIcon={icon}
        endIcon={<KeyboardArrowDownIcon />}
        variant={undefined}
      >
        <Typography>{title}</Typography>
      </NsButton>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        {entries.map((menuItem) => {
          return (
            <MenuItem
              key={menuItem.link}
              tabIndex={0}
              component={Link}
              href={menuItem.link}
            >
              <Typography textAlign="center">{t(menuItem.label)}</Typography>
            </MenuItem>
          );
        })}
      </Menu>
    </>
  );
};
