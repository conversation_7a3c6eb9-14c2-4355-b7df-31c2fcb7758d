import { useConfig } from './configuration.context';
import { useNotifier } from '@netservice/astrea-react-ds';
import axios from 'axios';

export const useGetRuolo = () => {
  const { servizi } = useConfig();
  const { notify } = useNotifier();

  const getRuolo = async () => {

    const serviceUrl = `${servizi}/auth/roles`;
    try {
      console.log('Requesting roles from:', serviceUrl);
      const response = await axios.get(serviceUrl);
      if (response.data.length === 0) {
        notify({
          message: 'Nessun ruolo',
          type: 'warning',
        });
      }
      localStorage.setItem('roles', JSON.stringify(response.data));
      console.log('roles', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching roles:', error.response || error);
      localStorage.setItem('roles', JSON.stringify([]));
      notify({
        message: 'Errore nel recupero dei ruoli',
        type: 'error',
      });
    }
  };

  return getRuolo;
};
