import { LinkProps, Link as MuiLink } from '@mui/material';
import NextLink from 'next/link';
import React from 'react';

/**
 * Wrapper per associare al Link di Mui le funzionalità del Link di NextJS (cioè il routing)
 */
export const Link = React.forwardRef<HTMLAnchorElement, LinkProps<'a'>>(
  (props, ref) => (
    <MuiLink ref={ref} component={NextLink} underline="none" {...props} />
  )
);

Link.displayName = 'EnhancedMuiLink';
