import { Box, Grid, Modal, Typography, useTheme } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import { MainModalProps } from 'src/interfaces';

export default function MainModal({
  modal,
  style,
  closeModal,
  title,
  body,
}: MainModalProps) {
  const { t } = useTranslation();
  const theme: any = useTheme();

  return (
    <Modal
      open={modal}
      onClose={undefined}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={style} onClick={(e) => e.stopPropagation()}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Typography variant="h4">{title}</Typography>
          {closeModal && (
            <NsButton sx={theme.custom.secondaryButton} onClick={closeModal}>
              {t('common.chiudi')}
            </NsButton>
          )}
        </Grid>
        <Grid item xs={12}>
          {body}
        </Grid>
      </Box>
    </Modal>
  );
}
