import Pagination from '@mui/material/Pagination';
import Stack from '@mui/material/Stack';
import { MainPaginationProps } from 'src/interfaces';
import { useState } from 'react';
import { TablePagination } from '@mui/material';

export default function MainPagination({
  aggregate,
  pageNumber,
  rowsPerPageFunction,
}: MainPaginationProps) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = (
    event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    setPage(newPage);
    pageNumber(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    rowsPerPageFunction(parseInt(event.target.value, 10));
  };

  return (
    <Stack mt={2} spacing={2}>
      <TablePagination
        page={page}
        onPageChange={handleChangePage}
        count={aggregate.total}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage={'Notifiche per pagina'}
        labelDisplayedRows={({ from, to, count }) =>
          `${from}-${to} di ${count}`
        }
      />
    </Stack>
  );
}
