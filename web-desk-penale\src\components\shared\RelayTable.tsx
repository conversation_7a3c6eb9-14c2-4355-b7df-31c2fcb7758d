import {
  Box,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  useTheme,
} from '@mui/material';
import TableCell from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';
import { styled } from '@mui/material/styles';
import { useState } from 'react';
import { RelayTableProps } from 'src/interfaces';

const StyledTableCell: any = styled(TableCell)(({ theme }: any) => ({
  '&.MuiTableCell-head': {
    backgroundColor: 'rgb(230,230,230)',
    color: theme.palette.common.black,
    fontWeight: 600,
    border: theme.custom.borders[0],
  },
  '&.MuiTableCell-body': {
    fontSize: 16,
    border: theme.custom.borders[0],
  },
}));

export default function RelayTable({
  rows,
  columns,
  sorting,
  createSortHandler,
  orderBy,
  order,
  pageInfo,
  changePageOrRowsEmitter
}: RelayTableProps) {
  const theme: any = useTheme();
  const [rowsPerPage, setRowsPerPage] = useState(pageInfo?.size || 10);
  const [page, setPage] = useState(0);

  const handleChangePage = (_: unknown, newPage: number) => {
    if(changePageOrRowsEmitter instanceof Function){
      changePageOrRowsEmitter?.(newPage);
    } else {
      setPage(newPage);
    }
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (changePageOrRowsEmitter instanceof Function){
      changePageOrRowsEmitter?.(0, +event.target.value);
    } else {
      setRowsPerPage(pageInfo?.size || +event.target.value);
      setPage(0);
    }
  };

  return (
    <>
      <TableContainer
        sx={{ border: theme.custom.borders[0], borderBottom: 'unset' }}
        component={Paper}
      >
        <TableContainer>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              {sorting && createSortHandler ? (
                <TableRow className={'ns-custom-tr'}>
                  {columns.map((column: any, i: number) => {
                    return (
                      <StyledTableCell
                        key={i}
                        className={'ns-custom-td'}
                        onClick={() => createSortHandler(column.id)}
                        sx={{ cursor: !column?.disabledSort ? 'pointer' : 'default '}}
                      >
                        {column.id != 'azioni' &&
                        column.id != 'verificato' &&
                        !column?.disabledSort ? (
                          <TableSortLabel
                            active={orderBy === column.id}
                            direction={
                              orderBy === column.id &&
                              order != '' &&
                              order == 'desc'
                                ? 'desc'
                                : 'asc'
                            }
                          >
                            <Box component="span">
                              {column.renderHeadCell
                                ? column.renderHeadCell(column.label, rows)
                                : column.label}{' '}
                            </Box>
                          </TableSortLabel>
                        ) : (
                          <Box component="span">
                            {column.renderHeadCell
                              ? column.renderHeadCell(column.label, rows)
                              : column.label}{' '}
                          </Box>
                        )}
                      </StyledTableCell>
                    );
                  })}
                </TableRow>
              ) : (
                <TableRow className={'ns-custom-tr'}>
                  {columns.map((column: any, i: number) => {
                    return (
                      <StyledTableCell key={i} className={'ns-custom-td'}>
                        <Box component="span">
                          {column.renderHeadCell
                            ? column.renderHeadCell(column.label, rows)
                            : column.label}{' '}
                        </Box>
                      </StyledTableCell>
                    );
                  })}
                </TableRow>
              )}
            </TableHead>
            <TableBody>
              {rows?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row: any, i: number) => {
                  const currentId = i + 10 * page;
                  return (
                    <TableRow hover role="checkbox" className={'ns-custom-tr'} tabIndex={-1} key={i}>
                      {columns.map((column: any, index: number) => {
                        const value = row[column.id];
                        if (column.render) {
                          return column.render(column, row, currentId);
                        }
                        return (
                          <TableCell
                            key={column.id}
                            align={column.align}
                            sx={{
                              border: theme.custom.borders[0],
                            }}
                            title={column.label}
                          >
                            {value}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </TableContainer>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 20, 25, 50, 100]}
        component="div"
        count={pageInfo?.totalElement || rows?.length || 10}
        rowsPerPage={pageInfo?.size || rowsPerPage}
        page={pageInfo?.page || page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Righe per pagina"
      />
    </>
  );
}
