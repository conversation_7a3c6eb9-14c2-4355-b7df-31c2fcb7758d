import { relayQueries_CodaDepositoSchemaQuery } from '@/generated/relayQueries_CodaDepositoSchemaQuery.graphql';
import { Box, Drawer, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';
import { NsFullPageSpinner, useNotifier } from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { STORE_OR_NETWORK, useMutation, useQuery } from 'relay-hooks';
import CodaDeposito from '../deposito/CodaDeposito';
import {
  CodaDeleteDepositoMutation,
  CodaDepositoSchema,
} from '../relay/relayQueries';
import { formatDate } from './Utils';
import { relayQueries_CodaDeleteDepositoMutation } from '@/generated/relayQueries_CodaDeleteDepositoMutation.graphql';

const ROOT_QUERY = CodaDepositoSchema;
const ROOT_DELETE = CodaDeleteDepositoMutation;

export default function SideModal({
  openDrawer,
  closeDrawer,
  refreshCoda,
  ruolo,
}: any) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const router = useRouter();
  const [depositi, setDepositi] = useState<any>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [deletebyidprovv] =
    useMutation<relayQueries_CodaDeleteDepositoMutation>(ROOT_DELETE);

  const CustomDrawer = styled(Drawer)(({ theme }) => ({
    '& .MuiDrawer-paper': {
      top: '250px',
      height: 'calc(100% - 250px)',
    },
  }));

  const { data } = useQuery<relayQueries_CodaDepositoSchemaQuery>(
    ROOT_QUERY,
    { role: ruolo },
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  useEffect(() => {
    const codeDepositoByCf =
      data?.codeDepositoByCf.filter((codeDep) => codeDep.codeDepositoDto) || [];

    if (codeDepositoByCf.length > 0) {
      const mapped = codeDepositoByCf.map((code) => ({
        idProvv: code.idProvv,
        numOrdine: code.codeDepositoDto?.numOrdine,
        nrg: code.codeDepositoDto?.nrg,
        dataUdienza: formatDate(
          code.codeDepositoDto?.dataUdienza,
          'DD/MM/YYYY'
        ),
        udienza: code.codeDepositoDto?.udienza,
      }));
      setDepositi(mapped);
    }
  }, [data]);

  const deleteCoda = (idProvv: string) => {
    setIsLoading(true);
    return deletebyidprovv({
      variables: {
        input: idProvv,
      },
    })
      .then((res) => {
        notify({
          type: 'success',
          message: 'Provvedimento Eliminato dalla Coda di Deposito',
        });
        const refreshedDepositi = depositi.filter(
          (deposito: any) => deposito.idProvv != idProvv
        );
        refreshCoda();
        setDepositi([...refreshedDepositi]);
        // window.location.reload();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <>
      <CustomDrawer anchor="right" open={openDrawer} onClose={closeDrawer}>
        <Box p={3} sx={{ width: 500 }}>
          <CodaDeposito
            deleteCoda={deleteCoda}
            depositi={depositi}
            ruolo={ruolo}
          />
        </Box>
      </CustomDrawer>
      {isLoading && (
        <Box position={'absolute'} zIndex={1400}>
          <NsFullPageSpinner isOpen={true} value={1} />
        </Box>
      )}
    </>
  );
}
