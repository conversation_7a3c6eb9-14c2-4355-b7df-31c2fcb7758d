import ArticleIcon from '@mui/icons-material/Article';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import FolderIcon from '@mui/icons-material/Folder';
import UploadIcon from '@mui/icons-material/Upload';
import axios from 'axios';
import moment from 'moment';
import { signOut, useSession } from 'next-auth/react';
import { PartiProps, PlaceholdersProps } from 'src/interfaces';
import { useNotifier } from '@netservice/astrea-react-ds';
import { NextRouter, useRouter } from 'next/router';
import EditIcon from '@mui/icons-material/Edit';
import RateReviewIcon from '@mui/icons-material/RateReview';
import SimCardAlertIcon from '@mui/icons-material/SimCardAlert';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { FileDownloadOff } from '@mui/icons-material';
import FileDownloadOffIcon from '@mui/icons-material/FileDownloadOff';
import { useConfig } from './configuration.context';
import { NsTooltip } from '@netservice/astrea-react-ds';
import { ProvvedimentiTipoEnum, StatoProvvedimentiEnum } from '../../types/types';

export const useDownloadUtils = (serviceUrl: string) => {
  const { notify } = useNotifier();

  const downloadFile = (
    url: string,
    filename: string,
    successMessage: string,
    errorMessage: string
  ) => {
    return new Promise<void>((resolve, reject) => {
      axios
        .get(url, { responseType: 'blob' })
        .then((response) => {
          notify({ message: successMessage, type: 'success' });
          const blob = new Blob([response.data]);
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          resolve();
        })
        .catch((error) => {
          notify({ message: errorMessage, type: 'error' });
          reject(error);
        });
    });
  };

  const handleDownloadAtto = (
    idUdienza: string,
    params: string,
    tipologia: string,
    nrg: string,
    isOscurato: boolean
  ) => {
    const nomeFile = `${tipologia}_${nrg}.pdf`;
    return downloadFile(
      `${serviceUrl}/provvedimento/downloadAtto/${idUdienza}/${params}/${isOscurato}`,
      `${nomeFile}`,
      'Atto scaricato con successo',
      "Impossibile scaricare l'atto"
    );
  };

  const previewPdf = (
    url: string,
    successMessage: string,
    errorMessage: string
  ) => {
    return new Promise<void>((resolve, reject) => {
      axios
        .get(url, { responseType: 'blob' })
        .then((response) => {
          notify({ message: successMessage, type: 'success' });
          const blob = new Blob([response.data], { type: 'application/pdf' });
          const pdfUrl = window.URL.createObjectURL(blob);
          window.open(pdfUrl, '_blank');
          resolve();
        })
        .catch((error) => {
          notify({message: errorMessage, type: 'error'});
          reject(error);
        });
    });
  };

  const handlePreviewAtto = (
    idUdienza: string,
    params: string,
    isOscurato: boolean
  ) => {
    return previewPdf(
      `${serviceUrl}/provvedimento/downloadAtto/${idUdienza}/${params}/${isOscurato}`,
      "Anteprima dell'atto caricata con successo",
      "Impossibile caricare l'anteprima dell'atto"
    );
  };


  return { downloadFile, handleDownloadAtto, previewPdf, handlePreviewAtto };
};

export const formatDate = (dateString: string, typeFormat: string): string => {
  const date = moment(dateString);
  return date.format(typeFormat);
};

export const mapParti = (parti: any) => {
  const mappedParti: PartiProps = {
    fullName: parti.anagraficaParte.nome + ' ' + parti.anagraficaParte.cognome,
    codiceFiscale: parti?.codiceFiscale,
    type: parti.ricorrente === '1' ? 'parte' : 'controparte',
    lawyers:
      parti?.difensori?.map((difensore: any) => ({
        fullName: `${difensore?.difensoreAnagrafica?.nome} ${difensore?.difensoreAnagrafica?.cognome}`,
        codiceFiscale: difensore?.difensoreAnagrafica?.codiceFiscale,
      })) ?? [],
  };

  return mappedParti;
};
export const regexForTestoSottolineato = (
  content: string | undefined | null
) => {
  if (content) {
    const regex = /<s \s*.*?>\s*.*?<\/s>/gi;
    return regex.test(content);
  }
  return false;
};

const allIcons = [
  {
    id: 1,
    name: 'view',
    icon: (
      <NsTooltip
        title="Apri fascicolo"
        icon={<FolderIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        placement='top'
      />
    ),
    bgColor: '#e0eeec',
    href: true,
  },
  {
    id: 2,
    name: 'upload',
    icon: (
      <NsTooltip
        title="Upload"
        icon={<UploadIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        placement='top'
      />
    ),
    bgColor: '#e0eeec',
  },
  {
    id: 3,
    name: 'delete',
    icon: (
      <NsTooltip
        title="Delete"
        icon={<DeleteOutlineIcon fontSize="small" color="error" />}
        placement='top'
      />
    ),
    bgColor: '#F9E1DD',
  },
  {
    id: 4,
    name: 'download',
    icon: (
      <NsTooltip
        title="Download"
        icon={<FileDownloadIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        placement='top'
      />
    ),
    bgColor: '#e0eeec',
  },
  {
    id: 5,
    name: 'preview',
    icon: (
      <NsTooltip
        title="Visualizza"
        icon={<ArticleIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        placement='top'
      />
    ),
    bgColor: '#e0eeec',
  },
];

export function azioniButton() {
  const iconBoxStyles = {
    width: '30px',
    height: '30px',
    marginRight: '10px',
    cursor: 'pointer',
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  };

  const getIcons = (...names: string[]) => {
    return names.map((nome: string) =>
      allIcons.find((icon) => icon.name === nome)
    );
  };

  return { iconBoxStyles, getIcons };
}

export const circleStyle = {
  background: '#308A7D',
  borderRadius: '50%',
  width: '20px',
  height: '20px',
};

export enum SezioneDescEnum {
  S1 = 'I Sezione',
  S2 = 'II Sezione',
  S3 = 'III Sezione',
  S4 = 'IV Sezione',
  S5 = 'V Sezione',
  S6 = 'VI Sezione',
  S7 = 'VII Sezione',
  SU = 'Sezioni Unite',
}

export const Sezione: { [key: string]: string } = {
  S1: 'I Sezione',
  S2: 'II Sezione',
  S3: 'III Sezione',
  S4: 'IV Sezione',
  S5: 'V Sezione',
  S6: 'VI Sezione',
  S7: 'VII Sezione',
  SU: 'Sezioni Unite',
};

export const SezioneShort: { [key: string]: string } = {
  S1: 'I Sez.',
  S2: 'II Sez.',
  S3: 'III Sez.',
  S4: 'IV Sez.',
  S5: 'V Sez.',
  S6: 'VI Sez.',
  S7: 'VII Sez',
  SU: 'Sez. Unite',
};

export const SezioneColors: { [key: string]: string } = {
  S1: '#068E87',
  S2: '#068E87',
  S3: '#068E87',
  S4: '#068E87',
  S5: '#068E87',
  S6: '#068E87',
  S7: '#2852B5',
  SU: '#5D2566',
};

export enum StatoInserimentoNoteEnum {
  MINUTA_ACCETTATA = 'MINUTA_ACCETTATA',
}

export enum RuoloEnum {
  PRESIDENTE = 'PRESIDENTE',
  PRESIDENTE_RELATORE = 'PRESIDENTE_RELATORE',
  ESTENSORE = 'ESTENSORE',
}

export enum TipoUdienzaEnum {
  CC = 'Camera di Consiglio',
  PU = 'Pubblica Udienza',
}

export const TipoUdienza: { [key: string]: string } = {
  'CAMERA DI CONSIGLIO': 'CC',
  'PUBBLICA UDIENZA': 'PU',
};

export const udienzeColors: { [key: string]: string } = {
  SCADENZA: '#FFA500',
  SU: '#990381',
};

export const TrackingState: { [key: string]: string } = {
  CODA_DI_FIRMA: 'In coda di firma',
  PUBBLICATA: 'Pubblicata',
  PROVV_DEPOSITATO_SIC: 'Provvedimento depositato dal sic',
  PUBBLICATO_SIC: 'Pubblicato da Sic',
  MINUTA_DEPOSITATA: 'Minuta depositata e inviata al Presidente',
  INVIATO_IN_CANCEL_PRESIDENTE: 'Inviata in cancelleria dal Presidente',
  MINUTA_MODIFICATA: 'Minuta modificata',
  MINUTA_DA_MODIFICARE: 'Richiesta modifica',
  MINUTA_ACCETTATA: 'Minuta accettata e inviata al Presidente',
  BUSTA_RIFIUTATA: 'Busta rifutata',
  BUSTA_RIFIUTATA_AL_PRESIDENTE: 'Busta rifiutata al Presidente',
  INVIATO_IN_CANCELLERIA_RELATORE: 'Inviata in cancelleria',
  BOZZA: 'Bozza',
  MINUTA_IN_REVISIONE: 'Minuta in revisione',
  IN_CODE_FIRMA_REL: 'In coda di firma',
  MINUTA_MODIFICATA_PRESIDENTE: 'Minuta modificata dal Presidente',
  BOZZA_PRESIDENTE: 'Bozza',
};

const mainStates: Record<string, string> = {
  BOZZA: 'Bozza',
  INVIATO_IN_CANCELLERIA_RELATORE: 'Inviato in cancelleria',
  BUSTA_RIFIUTATA: 'Busta rifiutata',
  BUSTA_RIFIUTATA_AL_PRESIDENTE: 'Busta rifiutata al Presidente',
  MINUTA_DA_MODIFICARE: 'Richiesta modifica',
  MINUTA_MODIFICATA: 'Minuta modificata',
  INVIATO_IN_CANCEL_PRESIDENTE: 'Inviato in cancelleria dal Presidente',
  MINUTA_DEPOSITATA: 'Minuta depositata',
  DA_REDIGERE: 'Da redigere',
  PUBBLICATA: 'Pubblicato',
  PUBBLICATO_SIC: 'Pubblicato da Sic',
  PROVV_DEPOSITATO_SIC: 'Provvedimento depositato da Sic',
  MINUTA_DEPOSITATA_SIC: 'Minuta depositata da Sic',
  REDAZIONE_ESTENSORE: 'In redazione dall’Estensore',
  MINUTA_IN_REVISIONE: 'Minuta in revisione',
  IN_CODE_FIRMA_REL: 'In coda di firma',
  MINUTA_MODIFICATA_PRESIDENTE: 'Minuta modificata dal Presidente',
  BOZZA_PRESIDENTE: 'Bozza',
};

export const getStateNames = (role: string, state: string) => {
  const customStates: Record<string, string> = {
    ...mainStates,
    MINUTA_ACCETTATA:
      role === 'PRESIDENTE'
        ? 'Minuta pervenuta'
        : 'Minuta accettata e inviata al Presidente',
    CODA_DI_FIRMA:
      role === 'PRESIDENTE'
        ? 'In coda di firma'
        : 'Minuta accettata e inviata al Presidente',
    INVIATO_IN_CANCEL_PRESIDENTE:
      role === 'PRESIDENTE'
        ? 'Inviato in cancelleria'
        : 'Inviato in cancelleria dal Presidente',
    MINUTA_MODIFICATA_PRESIDENTE:
      role === 'PRESIDENTE'
        ? "Minuta modificata e inoltrata all' Estensore"
        : 'Minuta modificata dal Presidente',
    BOZZA_PRESIDENTE: 'Bozza minuta modificata',
    RIUNITO: '-',
    RIUNITO_CARTACEO: 'Da redigere in cartaceo',
  };
  return customStates[state];
};

export const getStatesNamesTracking = (role: string, state: string) => {
  const customStates: Record<string, string> = {
    ...TrackingState,
    INVIATO_IN_CANCEL_PRESIDENTE:
      role === 'PRESIDENTE'
        ? 'Inviato in cancelleria'
        : 'Inviato in cancelleria dal Presidente',
    MINUTA_ACCETTATA:
      role === 'PRESIDENTE'
        ? 'Minuta pervenuta'
        : 'Minuta accettata e inviata al Presidente',
    MINUTA_MODIFICATA_PRESIDENTE: role === 'PRESIDENTE' ? 'Minuta modificata e inoltrata all\' Estensore' : 'Minuta modificata dal Presidente',
  };
  return customStates[state];
};

export const oscuramentoTxt =
  "IN CASO DI DIFFUSIONE DEL PRESENTE PROVVEDIMENTO OMETTERE LE GENERALITA' E GLI ALTRI DATI IDENTIFICATIVI A NORMA DELL'ART. 52 D.LGS. 196/03 E SS.MM.";

export const GetAuthUser = () => {
  const { data, status } = useSession();
  return { data: data, status, token: data?.accessToken };
};

export function checkStatoSic(checkStatoOnSIC: any): boolean {
  if (
    checkStatoOnSIC?.statoProvvedimento === StatoProvvedimentiEnum.PROVV_DEPOSITATO_SIC ||
    checkStatoOnSIC?.statoProvvedimento === StatoProvvedimentiEnum.PUBBLICATO_SIC ||
    checkStatoOnSIC?.statoProvvedimento === StatoProvvedimentiEnum.MINUTA_DEPOSITATA_SIC
  ) {
    return true;
  }
  return false;
}

export function editorEventInput(inputType: string) {
  console.log('before input check', inputType);
  switch (inputType) {
    case 'deleteContentBackward':
    case 'deleteContentForward':
    case 'insertParagraph':
      console.log('before input check return', inputType);
      return true;
    default:
      return false;
  }
}

export function editorEventInputKeyPress(inputType: string) {
  console.log('before input check', inputType);
  switch (inputType) {
    case 'ArrowDown':
    case 'ArrowUp':
    case 'ArrowLeft':
    case 'ArrowRight':
    case 'End':
      console.log('before input check return', inputType);
      return true;
    default:
      return false;
  }
}

export function setupEditor(editor: any) {
  editor.ui.registry.addIcon(
    'reset',
    '<img src="images/cancella_sottolineatura.png">'
  );
  editor.ui.registry.addButton('clear', {
    text: '',
    icon: 'reset',
    tooltip: 'Cancella sottolineature',
    onAction: function () {
      let x = editor.getContent();
      x = x.replaceAll('<s style="background: yellow;">', '');
      x = x.replaceAll('<s/>', '');
      editor.setContent(x);
    },
    onRender: function (api: any) {
      api.container.style.width = '40px !important';
    },
  });
  editor.ui.registry.addButton('oscuramentoButton', {
    text: '',
    icon: 'strike-through',
    tooltip: 'Trova e sottolinea',
    onAction: function () {
      editor.windowManager.open({
        title: 'Sottolineatura multipla',
        body: {
          type: 'panel',
          items: [
            {
              type: 'input',
              name: 'cerca',
              label: 'Cerca e sottolinea',
            },
          ],
        },
        onSubmit: function (api: any) {
          let data = api.getData();
          let searchTerm = data.cerca.trim();

          if (searchTerm && searchTerm !== 's') {
            try {
              // Usa l'API di ricerca di TinyMCE
              editor.focus();

              // Ottieni contenuto per controlli
              let content = editor.getContent();

              // Crea un documento temporaneo per lavorare con DOM
              const tempDoc = document.implementation.createHTMLDocument();
              const tempDiv = tempDoc.createElement('div');
              tempDiv.innerHTML = content;

              // Funzione per cercare e evidenziare testo in un nodo DOM
              const markText = (node: unknown): boolean => {
                if (!Node.prototype.isPrototypeOf(node as Node)) {
                  return false;
                }
                const domNode = node as Node;
                if (domNode.nodeType === 3) { // Nodo di testo
                  const text = (node as Text).nodeValue ?? '';
                  if (text.toLowerCase().indexOf(searchTerm.toLowerCase()) >= 0) {
                    // Crea un pattern case-insensitive
                    const pattern = new RegExp(`(${searchTerm.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')})`, 'gi');
                    const frag = document.createDocumentFragment();
                    let lastIdx = 0;
                    let match;

                    // Sostituisci ogni occorrenza
                    while ((match = pattern.exec(text)) !== null) {
                      // Testo prima della corrispondenza
                      if (match.index > lastIdx) {
                        frag.appendChild(document.createTextNode(text.substring(lastIdx, match.index)));
                      }

                      // Testo sottolineato
                      const marked = tempDoc.createElement('s');
                      marked.style.background = 'yellow';
                      marked.appendChild(document.createTextNode(match[0]));
                      frag.appendChild(marked);

                      lastIdx = pattern.lastIndex;
                    }

                    // Testo dopo l'ultima corrispondenza
                    if (lastIdx < text.length) {
                      frag.appendChild(document.createTextNode(text.substring(lastIdx)));
                    }

                    // Sostituisci il nodo originale con il frammento
                    if (lastIdx > 0) {
                      if (node instanceof Node && node.parentNode) {
                        node.parentNode.replaceChild(frag, node);
                      }
                      return true; // Indica che è avvenuta una sostituzione
                    }
                  }
                } else if (node instanceof Node && node.nodeType === 1 && (node as Element).nodeName !== 'SCRIPT' && (node as Element).nodeName !== 'STYLE') {
                  // Elemento - processa i figli ricorsivamente
                  let foundMatch = false;
                  const childNodes = Array.from(node.childNodes);
                  for (const child of childNodes) {
                    foundMatch = markText(child) || foundMatch;
                  }
                  return foundMatch;
                }
                return false;
              };

              // Esegui la ricerca e la sostituzione
              markText(tempDiv);

              // Imposta il contenuto modificato nell'editor
              editor.setContent(tempDiv.innerHTML);

              // Log per debug
              if (tempDiv.innerHTML === content) {
                console.info('Nessun risultato trovato per:', searchTerm);
              }
            } catch (e) {
              console.error('Errore nella ricerca:', e);
            }
          }
          api.close();
        }, buttons: [{
          text: 'Close', type: 'cancel', onclick: 'close',
        }, {
          text: 'Sottolinea', type: 'submit', primary: true,
        },],
      });
    },
  });
}

export const innerModalStyle = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '100%',
  height: '100%',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 2,
};

export const fixedButtons = {
  position: 'fixed',
  background: '#ebefef',
  bottom: '110px',
  width: '65%',
  zIndex: 10,
  borderRadius: '5px',
  padding: '5px',
};

export const modalStyle = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 2,
};

export const modalStylePreview = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  height: '60%',
  width: '80%',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 2,
  overflowY: 'auto',
};

export const fetchPlaceholders = async (
  servizi?: string,
  idUdienza?: string | string[],
  params?: string | string[]
) => {
  const { data } = await axios.get(
    `${servizi}/provvedimento/placeholder/${idUdienza}/${params}`
  );

  const mappedData: PlaceholdersProps = {};
  if (data.context.length > 0) {
    data.context.map((placeholder: any) => {
      mappedData[placeholder.key] = placeholder.value;
      return mappedData;
    });
  }
  mappedData.AUTORITAAPPELLO = mappedData.AUTORITAAPPELLO.includes(
    'CORTE APPELLO'
  )
    ? `Corte d'Appello`
    : mappedData.AUTORITAAPPELLO;

  mappedData.AUTORITAPRONUNCIA = mappedData.AUTORITAPRONUNCIA.includes(
    'CORTE APPELLO'
  )
    ? `Corte d'Appello`
    : mappedData.AUTORITAPRONUNCIA;

  mappedData.CITTAAPPELLO = formatCity(mappedData.CITTAAPPELLO);
  mappedData.CITTAPRONUNCIA = formatCity(mappedData.CITTAPRONUNCIA);

  mappedData.DATAPRONUNCIA = formatDate(mappedData.DATAPRONUNCIA, 'DD/MM/YYYY');
  mappedData.DATASENTENZA = formatDate(mappedData.DATASENTENZA, 'DD/MM/YYYY');
  return { ...mappedData };
};

export const formatCity = (city: string) => {
  let lowercaseCity = city.toLowerCase();
  return lowercaseCity.charAt(0).toUpperCase() + lowercaseCity.slice(1);
};

export const fetchTemplates = async (servizi?: string, tipologia?: string) => {
  const { data } = await axios.post(`${servizi}/template/search`, {
    tipologia,
  });
  return data;
};

export const userRole = () => {
  return JSON.parse(localStorage.getItem('roles') as string);
};

export const backToUdienza = (
  router: NextRouter,
  idProvv: string,
  nrg: number
) => {
  return router.push(`/calendario?id=${idProvv}&params=${nrg}`);
};

export function convertTipoMagPreToString(tipoMag: string) {
  return tipoMag === 'PRE' ? 'Presidente' : '';
}
export const validationStyle = {
  padding: '10px',
  borderLeft: '5px solid #D4351C',
  backgroundColor: '#FDF6F5',
  color: '#D4351C',
};

export const tipoProvvedimentoToString = (
  tipoProvvedimento: ProvvedimentiTipoEnum | null
) => {
  switch (tipoProvvedimento) {
    case 'ORDINANZA':
      return 'ORDINANZA';
    case 'SENTENZA':
      return 'SENTENZA';
    case 'MINUTA_ORDINANZA':
      return 'MINUTA ORDINANZA';
    case 'MINUTA_SENTENZA':
      return 'MINUTA SENTENZA';
    default:
      return null;
  }
};

export const convertTipoProvvedimentoForRedazione = (
  tipoProvvedimento: string | string[] | undefined
) => {
  switch (tipoProvvedimento) {
    case 'ORDINANZA':
    case 'MINUTA_ORDINANZA':
      return 'ORDINANZA';
    case 'SENTENZA':
    case 'MINUTA_SENTENZA':
      return 'SENTENZA';
    default:
      return null;
  }
};
export const convertTipoProvvedimentoForRedazioneOnlinePresidente = (
  tipoProvvedimento: string | string[] | undefined
) => {
  switch (tipoProvvedimento) {
    case 'ORDINANZA':
    case 'MINUTA_ORDINANZA':
      return 'MINUTA_ORDINANZA';
    case 'SENTENZA':
    case 'MINUTA_SENTENZA':
      return 'MINUTA_SENTENZA';
    default:
      return null;
  }
};
export const enabledStepOscuramento = (oscuramentoDati: string) => {
  return Boolean(oscuramentoDati && oscuramentoDati === 'si');
};

export interface IconRender {
  id: number;
  backgroundColor?: string;
  actionName: EstensoreActionName;
  // ci andrebbe un elemento da tipizzare con la classe coretta
  icon: JSX.Element;
}

export enum EstensoreActionName {
  REDAZIONE,
  FIRMA_DEPOSITA_ACTION,
  PUSH_INTO_DEPOSIT_QUEUE,
  DOWNLOAD_PDF,
  DOWNLOAD_PDF_OSCURATO,
  DELETE, //Usato per upload del file pdf
  IMPORT_DOCUMENT,
  DUPLICA_PROVV_AND_GOTO_REDAZIONE, //Viene usata quando si  deve duplicare un file pdf non redazione online
  DUPLICATE_DOCUMENT_LOCAL,
  TRACKING,
  NOTE,
  STATO,
  DOWNLOAD_EPURATO,
  DOWNLOAD_EPURATO_OSCURATO,
  PREVIEW_EPURATO,
  PREVIEW_EPURATO_OSCURATO,
}

export const updateElementById = (display: string, idName: string) => {
  const element = document.getElementById(idName);
  if (element) {
    element.style.display = display;
  }
};

export const stepsRelatore = [
  {
    label: 'Minuta in revisione',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_IN_REVISIONE,
    completed: false,
  },
  {
    label: 'In bozza',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.BOZZA,
    completed: false,
  },
  {
    label: 'Inviata in cancelleria',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.INVIATO_IN_CANCELLERIA_RELATORE,
    completed: false,
  },
  {
    label: 'Busta rifutata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.BUSTA_RIFIUTATA,
    completed: false,
  },
  {
    label: 'Minuta accettata e inviata al Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_ACCETTATA,
    completed: false,
  },
  {
    label: 'Richiesta modifica',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE,
    completed: false,
  },
  {
    label: 'Minuta modificata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_MODIFICATA,
    completed: false,
  },
  {
    label: 'Inviata in cancelleria dal Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE,
    completed: false,
  },
  {
    label: 'Pubblicata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.PUBBLICATA,
    completed: false,
  },
  {
    label: 'In coda di firma',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.IN_CODE_FIRMA_REL,
    completed: false,
  },
];

export const stepsPresidente = [
  {
    label: 'Minuta in revisione',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_IN_REVISIONE,
    completed: false,
  },
  {
    label: 'Busta rifutata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.BUSTA_RIFIUTATA,
    completed: false,
  },
  {
    label: 'Minuta accettata e inviata al Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_ACCETTATA,
    completed: false,
  },
  {
    label: 'Richiesta modifica',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_DA_MODIFICARE,
    completed: false,
  },
  {
    label: 'Minuta modificata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.MINUTA_MODIFICATA,
    completed: false,
  },
  {
    label: 'Inviata in cancelleria dal Presidente',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.INVIATO_IN_CANCEL_PRESIDENTE,
    completed: false,
  },
  {
    label: 'Pubblicata',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.PUBBLICATA,
    completed: false,
  },
  {
    label: 'In coda di firma',
    description: '',
    dateChange: '',
    stato: StatoProvvedimentiEnum.CODA_DI_FIRMA,
    completed: false,
  },
];

export const getActionDatiFascicolo = (translate: any): Array<IconRender> => {
  return [
    {
      id: 1,
      actionName: EstensoreActionName.DOWNLOAD_EPURATO,
      icon: (
        <NsTooltip
          title={translate('fascicolo.datiFascicolo.downloadprovvedimento')}
          icon={
            <FileDownloadIcon
              fontSize="small"
              sx={{ color: '#2E5A60', backgroundColor: '#e0eeec' }}
            />
          }
        />
      ),
    },
    {
      id: 2,
      actionName: EstensoreActionName.PREVIEW_EPURATO,
      icon: (
        <NsTooltip
          title={translate('fascicolo.datiFascicolo.visualizzaProvvedimento')}
          icon={
            <VisibilityIcon
              fontSize="small"
              sx={{ color: '#2E5A60', backgroundColor: '#e0eeec' }}
            />
          }
        />
      ),
    },
    {
      id: 3,
      actionName: EstensoreActionName.DOWNLOAD_EPURATO_OSCURATO,
      icon: (
        <NsTooltip
          title={translate(
            'fascicolo.datiFascicolo.downloadprovvedimentoOscurato'
          )}
          icon={
            <FileDownloadOff
              fontSize="small"
              sx={{ color: '#2E5A60', backgroundColor: '#e0eeec' }}
            />
          }
        />
      ),
    },
    {
      id: 4,
      actionName: EstensoreActionName.PREVIEW_EPURATO_OSCURATO,
      icon: (
        <NsTooltip
          title={translate(
            'fascicolo.datiFascicolo.visualizzaProvvedimentoOscurato'
          )}
          icon={
            <VisibilityOff
              fontSize="small"
              sx={{ color: '#2E5A60', backgroundColor: '#e0eeec' }}
            />
          }
        />
      ),
    },
  ];
};

export function isNullOrUndefined(value: any) {
  return value === undefined || value === null;
}
export function clearHtmlTag(value: string | undefined | null) {
  if (value) {
    const regexCodeHtml = /(<([^>]+)>)/gi;
    const textRipulito = value?.replaceAll(regexCodeHtml, '');
    return textRipulito.trim();
  }
  return '';
}
export const getActionEstensore = (translate: any): Array<IconRender> => {
  return [
    {
      id: 1,
      actionName: EstensoreActionName.REDAZIONE,
      icon: (
        <NsTooltip
          title={translate(
            'fascicolo.provvedimentiTable.modificaProvvedimento'
          )}
          icon={<EditIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        />
      ),
    },
    {
      id: 3,
      actionName: EstensoreActionName.FIRMA_DEPOSITA_ACTION,
      icon: (
        <NsTooltip
          title={translate('fascicolo.provvedimentiTable.depositaMinuta')}
          icon={<RateReviewIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        />
      ),
    },
    {
      id: 4,
      actionName: EstensoreActionName.PUSH_INTO_DEPOSIT_QUEUE,
      icon: (
        <NsTooltip
          title={translate(
            'fascicolo.provvedimentiTable.inserisciInCodaDiFirma'
          )}
          icon={<ArticleIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        />
      ),
    },
    {
      id: 7,
      actionName: EstensoreActionName.DOWNLOAD_PDF,
      icon: (
        <NsTooltip
          title={translate('fascicolo.provvedimentiTable.scaricaProvvedimento')}
          icon={<FileDownloadIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        />
      ),
    },
    {
      id: 8,
      actionName: EstensoreActionName.DOWNLOAD_PDF_OSCURATO,
      icon: (
        <NsTooltip
          title={translate(
            'fascicolo.provvedimentiTable.scaricaProvvedimentoOscurato'
          )}
          icon={
            <FileDownloadOffIcon fontSize="small" sx={{ color: '#2E5A60' }} />
          }
        />
      ),
    },
    {
      id: 9,
      actionName: EstensoreActionName.DELETE,
      backgroundColor: '#f9e1dd',
      icon: (
        <NsTooltip
          title={translate('fascicolo.provvedimentiTable.eliminaProvvedimento')}
          icon={
            <DeleteOutlineIcon fontSize="small" sx={{ color: '#ff0000' }} />
          }
        />
      ),
    },
    {
      id: 2,
      actionName: EstensoreActionName.IMPORT_DOCUMENT,
      icon: (
        <NsTooltip
          title={translate('fascicolo.provvedimentiTable.caricaProvvedimento')}
          icon={<UploadIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        />
      ),
    },
    {
      id: 5,
      actionName: EstensoreActionName.DUPLICA_PROVV_AND_GOTO_REDAZIONE,
      icon: (
        <NsTooltip
          title={translate(
            'fascicolo.provvedimentiTable.modificaProvvedimento'
          )}
          icon={<SimCardAlertIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        />
      ),
    },
    {
      id: 6,
      actionName: EstensoreActionName.DUPLICATE_DOCUMENT_LOCAL,
      icon: (
        <NsTooltip
          title={translate(
            'fascicolo.provvedimentiTable.modificaProvvedimento'
          )}
          icon={<SimCardAlertIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
        />
      ),
    },
  ];
};

export const confirmModalStyle = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)', // height: '60%',
  width: 400,
  bgcolor: 'background.paper', // border: (useTheme() as any).custom.borders[0],
  boxShadow: 24,
  p: 2,
  overflowY: 'auto',
};

export interface MsalConfig {
  azureRedirectUri?: String;
  microsoftTenantId?: String;
  azureAdB2cHostName?: String;
  azureAdB2cTenantName?: String;
  azureAdB2cPrimaryUserFlow?: String;
}

export const getMsalLogoutUrl = (msalConfig: MsalConfig, token?: String) => {
  return (
    `https://${msalConfig.azureAdB2cHostName}/` +
    `${msalConfig.azureAdB2cTenantName}.onmicrosoft.com/` +
    `${msalConfig.azureAdB2cPrimaryUserFlow}/` +
    `oauth2/v2.0/logout?id_token_hint=${token}` +
    `&post_logout_redirect_uri=https://login.microsoftonline.com/` +
    `${msalConfig.microsoftTenantId}/oauth2/v2.0/logout` +
    `?post_logout_redirect_uri=${msalConfig.azureRedirectUri}`
  );
};
