import React, { useContext } from 'react';

export interface PortaleDeskCassPConfiguration {
  servizi?: string;
  serviziGraphQl?: string
  isDebug?: string;
  enableFirmaOtp?: boolean;
  autoSaveTimer?: number;
  azureAdB2cHostName?: string;
  azureRedirectUri?: string;
  azureAdB2cTenantName?: string;
  azureAdB2cPrimaryUserFlow?: string;
  azureAdB2cClientId?: string;
  azureAdB2cClientSecret?: string;
  digitalSignatureAuthorityAzure?: string;
  azureScopePortaleDeskCassp?: string;
  digitalSignatureClientIdAzure?: string;
  digitalSignatureRedirectUriAzure?: string;
  microsoftTenantId?: string;
  notifichationsCount?: any;
  setNotifichationsCount?: React.Dispatch<React.SetStateAction<any>>;
}

export const PortaleConfigurationContext =
  React.createContext<PortaleDeskCassPConfiguration>({});

export function useConfig() {
  return useContext(PortaleConfigurationContext);
}
