import { useState, useEffect } from 'react';
import axios from "axios";
import {useConfig} from "../components/shared/configuration.context";

export type TApiResponse<T> = {
  status: number;
  statusText: string;
  data: T | null | undefined;
  error: any;
  loading: boolean;
};
export enum MethodAxios{
  GET ="GET",
  POST = "POST",
  PUT = "PUT",
  DELETE ="DELETE"
}

/**
 * Hook che esegue la chiamata axios in base al verbo HTML
 * @param path l'url da chiamate passare solo parte senza dominio il dominio lo mette l'hooks.
 *             esempio http://localhost:3001/api/v1/provvedimenti/1/1 passare solo /provvedimenti/1/1
 * @param method enumerazione dei verbi HTML {@MethodAxios}
 * @param body la richiesta da inviare nel post o nel put di tipo O passato come generico
 * @param convert funzione che converte il risultato in un oggetto definito
 * @param params elenco dei parametri in ordine da passare alla funzione
 * @return ritorna {@TApiResponse} con detro data di tipo T che è l'oggetto passato nei generici <T>
 */
export const useApiAxios = <T, O>(path: string | Function, method: MethodAxios, body?: O, convert?: Function, params?: Array<any>, startCall = true,  retry?: boolean): TApiResponse<T> => {
  const [status, setStatus] = useState<number>(0);
  const [statusText, setStatusText] = useState<string>('');
  const [data, setData] = useState<T | null | undefined>();
  const [error, setError] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const { servizi } = useConfig();

  const callAxiosApi = async () => {
    setLoading(true);
    try {
      let apiResponse = null;
      const url = path instanceof Function ? servizi + path?.() : servizi + path
      switch (method){
        case MethodAxios.GET:
          apiResponse =  await axios.get(url);
          break;
        case MethodAxios.DELETE:
          apiResponse =  await axios.delete(url);
          break;
        case MethodAxios.POST:
          apiResponse = await axios.post(url, body);
          break;
        case MethodAxios.PUT:
          apiResponse =   await axios.put(url, body);
          break;
        default: throw new Error('Method not supported');
      }

      setStatus(apiResponse.status);
      setStatusText(apiResponse.statusText);
      if(convert && convert instanceof Function){
        if(params){
          apiResponse.data = convert?.(apiResponse.data, ...params);
        } else {
          apiResponse.data = convert?.(apiResponse.data);
        }
      }
      setData(apiResponse.data);
    } catch (error) {
      console.error(error)
      setError(error);
    }
    setLoading(false);
  };

  useEffect(  () => {
    if(startCall){
      callAxiosApi();
    }
  }, []);
  useEffect(  () => {
    if(retry){
      callAxiosApi();
    }
  }, [retry]);

  return {status, statusText, data, error, loading};
}
