import { UtentiComponentFragment$key } from '@/generated/UtentiComponentFragment.graphql';
import { SxProps } from '@mui/system';
import { ProvvedimentiStatoEnum } from '@/generated/relayQueriesFascicoloDetails_FascicoloDetailsQuery.graphql';
import {relayQueries_DatiGeneraliUdienzaQuery$data} from "@/generated/relayQueries_DatiGeneraliUdienzaQuery.graphql";
import {
  relayQueries_ricorsiUdienzaPaginatoQuery,
  relayQueries_ricorsiUdienzaPaginatoQuery$data
} from "@/generated/relayQueries_ricorsiUdienzaPaginatoQuery.graphql";
import {
  relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery,
  relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery$data
} from "@/generated/relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery.graphql";

interface LawyerProps {
  fullName: string;
  codiceFiscale: string;
}

export interface PartiProps {
  fullName: string;
  codiceFiscale: string;
  type: string;
  lawyers: LawyerProps[];
}

export interface ReatiProps {
  displayReati: string;
}

export interface MenuNames {
  title: string;
  value: string;
}

export interface MenuOptionsProps {
  value: string;
  label: string;
}

export interface ScrivaniaFilterProps {
  filteredResearch: boolean;
  setFilteredResourceData: (filteredResourceData: any) => any;
  closeModal?: () => void;
}

export interface RelayTableProps {
  rows: any;
  columns: Column[];
  sorting?: boolean;
  createSortHandler?: (property: any) => void;
  orderBy?: string;
  order?: string;
  changePageOrRowsEmitter?: (page:number, rowPerPage?: number) => void
  pageInfo?: {page: number, size: number, totalElement: number}
}

export interface Column {
  id: string;
  label?: string;
  minWidth?: number;
  disabledSort?: boolean;
  align?: 'left' | 'right' | 'center';
  format?: (value: number) => string;
  render?: (cell: any, row?: any, index?: number) => any;
  renderHeadCell?: (cell: any, rows?: any, index?: number) => any;
}

export interface CalendarEvent {
  id: string;
  title: string | null;
  start: Date | null;
  end: Date | null;
  type?: string;
  sigla?: string;
  refreshed?: boolean | null;
}

export interface MinuteUdienzaProps {
  filteredResearch?: boolean;
  dataUdienza?: string | null;
  sezione?: string | null;
  tipoUdienza?: string | null;
  collegio?: string | null;
  onlyMinuteNew?: boolean;
  onlyProvvedimentiNew?: boolean;
}

export interface BoxCalendarioUdienzeProps {
  selectedEvent: CalendarEvent | null;
}

export interface DatiGeneraliEventProps {
  selectedEvent: CalendarEvent;
  dayEvents: CalendarEvent[];
  closeDatiUdienza?: () => void;
}

export interface DetailPartiProps {
  parti?: PartiProps[] | null;
}
export interface DetailRiunitiProps {
  nrgPrincipale?: number | null;
  idUdienzaPrincipale?: number | null;
  idRicorsoUdienza?: number | null;
}

export interface DetailReatiProps {
  reati?: ReatiProps[] | null;
}

export interface MagType {
  [key: string]: string;
}

export interface CustomToolbarProps {
  view: string;
  views: string[];
  label: string;
  localizer: any;
  onNavigate: (data: string) => void;
  onView: (data: string) => void;
}

export interface MainCalendarProps {
  onSelectEvent?: (
    event: CalendarEvent,
    currentDayEvents: CalendarEvent[]
  ) => void;
}

export interface ButtonProps {
  name: string;
  menuNames: MenuNames[];
  endIcon: any;
  handleButton?: (name: string) => void;
  disabled?: boolean;
  disableImport?: boolean;
}

export interface DropdownMenuProps {
  icon?: React.ReactNode;
  title: string;
  entries: { link: string; label: string; onClick?: any }[];
  color?: string;
  sx?: SxProps;
  fullWidth?: boolean;
}

export interface HeaderProps {
  logo: string;
  renderUserMenu: () => React.ReactElement;
  entries: MenuEntry[];
}
export interface MenuValue {
  kind: 'single';
  link: string;
  label: string;
}

export interface MenuDropdown {
  kind: 'multiple';
  label: string;
  values: MenuValue[];
}

export type MenuEntry = MenuValue | MenuDropdown;

export interface HeaderProps {
  logo: string;
  renderUserMenu: () => React.ReactElement;
  entries: MenuEntry[];
}

export interface MainModalProps {
  modal: boolean;
  closeModal?: () => void;
  closeConfirmModal?: () => void;
  style: object;
  title?: string;
  body?: JSX.Element;
}

export interface MenuItemData {
  title: string;
  subtitle: string;
  link: string;
  icon: React.ReactElement;
  group?: string;
}

export interface MenuItemProps {
  item: MenuItemData;
}

export interface UdienzaDatiHeaderProps {
  showValore?: boolean;
  openModal?: () => void;
  mainCollegio?: any;
  termineDeposito?: any;
  valPondale?: number;
  valPondComplessivo?: number;
  idUdienza?: number;
  nrg?: number;
  selectedEvent?: { sezione: string; aula: string; descrizione: string };
}

export interface DashboardProps {
  cardGroups: { [key: string]: MenuItemData[] };
}

export interface UtentiComponentProps {
  data: UtentiComponentFragment$key;
}

export interface DetailStatoProps {
  id: string;
  roles: string;
}
type CheckStatoSicType = {
  dataMinuta: any | null;
  dataPubblicazione: any | null;
  idUdienza: number | null;
  nrg: number | null;
  numRaccoltaGenerale: number | null;
  numRaccoltaGeneraleString: string | null;
  statoProvvedimento: ProvvedimentiStatoEnum | null;
};
export interface TrackingStatoProps {
  id: string;
  roles: string;
  sicCheckStato: CheckStatoSicType | null | undefined;
  isEstensorePresidente: boolean | null | undefined;
}

export interface RedazioneOnlineProps {
  data: any;
  closeModal: () => void;
}

export interface UploadProvvedimentoProps {
  data: any;
  closeModal?: () => void;
}

export interface UploadProvvedimentoDuplicateProps {
  data: any;
  idProvvOld: string;
  closeModal?: () => void;
}

export interface CardUdienzaProps {
  record: any;
}

export interface MainPaginationProps {
  aggregate: {
    count: number;
    total: number;
  };
  pageNumber: (data: number) => void;
  rowsPerPageFunction: (data: number) => void;
}

export interface TableUpdateInfo {
  pageSize: number;
  after?: string;
  orderDirection?: 'asc' | 'desc';
  term?: null | string;
}

export interface FirmaDepositaProps {
  idUdienza?: any;
  tipologiaProvvedimento?: any;
  type?: string;
  closeModal?: () => void;
  daDepositare?: string[];
  provvedimentiResult?: (provvedimenti: []) => void;
}

export interface CodaDepositoProps {
  idProvv: string;
  numORdine?: number | null;
  nrg?: string | null;
  dataUdienza?: string | null;
  tipoProvvedimento?: string | null;
  udienza?: string | null;
  stato: string | null;
}

export class TemplateProps {
  id?: string ;
  title?: string;
  description?: string;
  content?: string;
  constructor(init?: Partial<TemplateProps>) {
    Object.assign(this, init);
  }
}

export class EditorTemplates {
  introduzione: Array<TemplateProps> | undefined;
  finaleDeposito: Array<TemplateProps> | undefined;
  motivoRicorso: Array<TemplateProps> | undefined;
}
export interface EditorValue {
  introduzione?: string,
  idProvvedimento?: string | null,
  pqm?: string,
  finaleDeposito?: string,
  motivoRicorso?: string
  textLibero?: string,
  textOscurato?: string,
  oscurato?: boolean,
  strutturato?: boolean,
  idSezionale?: string,
  tipoProvvedimento?: string;
  semplificata?: boolean;
}

export interface LoginProps {
  type?: string;
}
export interface FascitoloDetExtraInfo{
  valorePonderaleTotale: number | undefined;
  totalCount: number | undefined;
}

export interface CalendarioTableProps {
  idUdienza: number;
  fascitoloDetExtraInfo: FascitoloDetExtraInfo;
  onRowClick?: (data: any) => void;
  getSelected?: (data: any) => void;
}
export interface CalendarioTableProps2 {
  idUdienza: number;
  query: relayQueriesUdienzeFragment_CalendarioTable_ricorsiUdienzaQuery$data;
  onRowClick?: (data: any) => void;
  getSelected?: (data: any) => void;
}

export interface VerificaProvvedimentoProps {
  onRowClick: (data: any) => void;
}

export interface ConfermaProps {
  body: string;
  closeModal: () => void;
  confirm?: () => void;
}

export interface PlaceholdersProps {
  [key: string]: any;
}

export interface NotifichationsProps {
  read: [];
  unread: [];
}

export type VisualTableProps = {
  filteredResourceData: {
    filteredResearch: boolean;
    dataUdienza: string;
    sezione: string;
    tipoUdienza: string;
    collegio: string;
  };
  onlyMinuteNew: boolean;
  onlyProvvedimentiNew: boolean;
};

export interface CodaButtonProps {
  refreshCoda?: boolean;
  refreshPage?: () => void;
  ruolo: string;
}

export interface DetailFascicoloProps {
  ruolo: string;
}
