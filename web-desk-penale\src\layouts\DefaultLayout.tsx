import { Container } from '@mui/material';
import { styled } from '@mui/material/styles';
import { makeStyles } from '@mui/styles';
import Head from 'next/head';
import { useTranslation } from 'react-i18next';

const StyledHeader = styled('header')({
  backgroundColor: 'black',
});

const useStyles = makeStyles((theme) => ({
  fullWidthContainer: {
    padding: 0,
  },
}));

export interface LayoutProps {
  children: React.ReactNode;
}

const links = [
  { href: '#', text: 'Accessibility' },
  { href: '#', text: 'Sitemap' },
  { href: '#', text: 'Cookies' },
  { href: '#', text: 'Privacy' },
];

/**
 * Default layout for pages
 */
export const DefaultLayout = ({ children }: LayoutProps) => {
  const { t } = useTranslation();
  const classes = useStyles();

  return (
    <>
      <Head>
        <title>{t('meta.title')}</title>
        <meta name="description" content={t('meta.description')!} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <Container className={classes.fullWidthContainer} maxWidth={false}>
        <main>{children}</main>
      </Container>
    </>
  );
};
