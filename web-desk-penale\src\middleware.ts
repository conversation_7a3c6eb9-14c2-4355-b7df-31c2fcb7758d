import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(req: any) {

  // These constant values are later used inside the middleware function for authorization logic.
  const CONSTANTS = {
    JOB_TITLE: 'MAGISTRATO',
    OFFICE_LOCATION: 'CORTE SUPREMA DI CASSAZIONE ROMA',
  };

  const isAuth = await getToken({ req });
  const { pathname } = req.nextUrl;
  const excludedStaticPaths = ['/api','/locales','/_next/static','/_next/image', '/favicon.ico', '/images'];
  const regex = new RegExp(/^!?401|403|login|404|503|500$/);

  if(pathname && excludedStaticPaths.some((exc => pathname.startsWith(exc)))){
    return NextResponse.next();
  }

  /*If the user is not authenticated and the path does not match the regex, the user is redirected to the '/login' page. */
  if (!isAuth && !regex.test(pathname)) {
    req.nextUrl.pathname = '/login';
    return NextResponse.redirect(req.nextUrl);
  }

  const isAccessAllowed = (authUser: any) => {
    return authUser?.user?.jobTitle?.includes(CONSTANTS.JOB_TITLE) && authUser?.user?.officeLocation?.includes(CONSTANTS.OFFICE_LOCATION);
  }

  /*If the user accessed specific paths (401, 403, login, 404, 503, 500) and not from specific location, then it's redirected to the '/401' page. */
  if (isAuth && !isAccessAllowed(isAuth) && regex.test(pathname)) {
    if (pathname !== '/401') {
      req.nextUrl.pathname = '/401';
      return NextResponse.redirect(req.nextUrl);
    }
  }

  /*If the user is already authenticated, then the user will be redirected to the homepage from '/login' page */
  const LOGIN_PATH = '/login';
  const HOME_PATH = '/';

  if (pathname === LOGIN_PATH && isAuth) {
    req.nextUrl.pathname = HOME_PATH;
    return NextResponse.redirect(req.nextUrl);
  }

  return NextResponse.next();
}

