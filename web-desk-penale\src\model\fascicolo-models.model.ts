import {
  relayQueriesRiunitDetails_GetRiunitiDetailsQuery$data
} from "@/generated/relayQueriesRiunitDetails_GetRiunitiDetailsQuery.graphql";
export interface DatiFascicoloDetailsProps {
  ricorsoUdienza: any;
  checkStatoOnSIC?: any;
}
export interface DatiFascicoloProps extends  DatiFascicoloDetailsProps{
  relatore: any;
  isRelatore: boolean;
  isEstensore: boolean;
  checkStatoOnSIC: any;
  nomeFile?: string;
  provvedimento?: any;
}
export interface TabPanelRiunitiProps {
  idsRicudienRiunito: number[];
  riuniti: relayQueriesRiunitDetails_GetRiunitiDetailsQuery$data | null
  value: number | null | undefined;
}
