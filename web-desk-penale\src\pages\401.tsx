import {Grid, Box, Typography, useTheme, Button} from "@mui/material";
import {useTranslation} from "next-i18next";
import {useRouter} from "next/router";
import {GetAuthUser} from "../components/shared/Utils";
import {useConfig} from "../components/shared/configuration.context";
import {signOut} from "next-auth/react";


/* The HTTP 401 Unauthorized client error response status code indicates that a request was not successful
*  because it lacks valid authentication credentials for the requested resource.
*  This status code is sent with an HTTP WWW-Authenticate response header that contains
*  information on the authentication scheme the server expects the client to include to make the request successfully.
*  A 401 Unauthorized is similar to the 403 Forbidden response, except that a 403 is returned when
*  a request contains valid credentials, but the client does not have permissions to perform a certain action.
*
*
* */
export const Custom401 = () => {

  // const
  const { t } = useTranslation();
  const theme: any = useTheme();
  const router = useRouter();
  const token  = GetAuthUser();
  const {
    azureRedirectUri,
    microsoftTenantId,
    azureAdB2cHostName,
    azureAdB2cTenantName,
    azureAdB2cPrimaryUserFlow,
  } = useConfig();

  async function redirectTo() {
    try {
      localStorage.clear();
      await signOut();

      const logoutUrl = `https://${azureAdB2cHostName}/${azureAdB2cTenantName}.onmicrosoft.com/${azureAdB2cPrimaryUserFlow}/oauth2/v2.0/logout?id_token_hint=${token}&post_logout_redirect_uri=https://login.microsoftonline.com/${microsoftTenantId}/oauth2/v2.0/logout?post_logout_redirect_uri=${azureRedirectUri}`;

      await router.push(logoutUrl);
    } catch (error) {
      console.error("Errore durante il logout:", error);
    }
  }

  // funzione che mi riporta alla pagina di login IAM
  const goToLogin = async () => {
    await redirectTo();
  };

  return(
    <Grid p={2}>
      <Box border={theme.custom.borders[1]} p={3} width={'30%'} borderRadius="5px">
        <Typography color={'primary'} sx={{ fontSize: '4rem !important', fontWeight: 'bold' }} mb={2}>
          {t('pages.custom401.401')}
        </Typography>

        <Typography mb={2} variant="body1" sx={{ fontWeight: 'bold' }}>
          {t('pages.custom401.messageOf401')}
        </Typography>

        <Typography mb={2} variant="body1">
          {t('pages.custom401.deskCassazionePenale1')}
        </Typography>

        <Typography mb={2} variant="body1">
          {t('pages.custom401.deskCassazionePenale2')}
        </Typography>

        <Typography mb={2} variant="body1">
          {t('pages.custom401.deskCassazionePenale3')}
        </Typography>

        <Box mb={2} mt={4}>
          <Button variant="contained" color="primary" onClick={goToLogin}>
            {t('pages.custom401.goToLoginPage')}
          </Button>
        </Box>
      </Box>
    </Grid>
  );
}
export default Custom401
