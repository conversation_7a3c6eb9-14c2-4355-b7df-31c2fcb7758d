import { Box, Button, Grid, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import {signOut} from "next-auth/react";
import {useConfig} from "../components/shared/configuration.context";
import {GetAuthUser} from "../components/shared/Utils";

/*
  The HTTP 403 Forbidden response status code indicates
  that the server understands the request but refuses to authorize it.
  This status is similar to 401, but for the 403 Forbidden status code, re-authenticating makes no difference.
  The access is tied to the application logic, such as insufficient rights to a resource.
*/

export const Custom403 = () => {

  // const
  const { t } = useTranslation();
  const theme: any = useTheme();
  const router = useRouter();
  const token  = GetAuthUser();
  const {
    azureRedirectUri,
    microsoftTenantId,
    azureAdB2cHostName,
    azureAdB2cTenantName,
    azureAdB2cPrimaryUserFlow,
  } = useConfig();

  async function redirectTo() {
    try {
      localStorage.clear();
      await signOut();

      const logoutUrl = `https://${azureAdB2cHostName}/${azureAdB2cTenantName}.onmicrosoft.com/${azureAdB2cPrimaryUserFlow}/oauth2/v2.0/logout?id_token_hint=${token}&post_logout_redirect_uri=https://login.microsoftonline.com/${microsoftTenantId}/oauth2/v2.0/logout?post_logout_redirect_uri=${azureRedirectUri}`;

      await router.push(logoutUrl);
    } catch (error) {
      console.error("Errore durante il logout:", error);
    }
  }

  // funzione che mi riporta alla pagina di login IAM
  const goToLogin = async () => {
    await redirectTo();
  };

  return (
    <Grid p={2}>
      <Box
        border={theme.custom.borders[1]}
        p={3}
        width={'30%'}
        borderRadius="5px"
      >
        <Typography
          color={'primary'}
          sx={{ fontSize: '4rem !important', fontWeight: 'bold' }}
          mb={2}
        >
          {t('pages.custom403.403')}
        </Typography>
        <Typography mb={2} variant="body1" sx={{ fontWeight: 'bold' }}>
          {t('pages.custom403.messageOf403')}
          </Typography>
          <Typography
            mb={2}
            variant="body1"

          >
            {t('pages.custom403.deskCassazionePenale1')}
        </Typography>
        <Typography
            mb={2}
            variant="body1"

          >
            {t('pages.custom403.deskCassazionePenale2')}
        </Typography>
        <Typography
            mb={2}
            variant="body1"

          >
            {t('pages.custom403.deskCassazionePenale3')}
        </Typography>
        <Box mb={2} mt={4}>
        <Button variant="contained" color="primary" onClick={goToLogin}>
          {t('pages.custom403.goToLoginPage')}
        </Button>
        </Box>
      </Box>
    </Grid>
  );
};
export default Custom403;
