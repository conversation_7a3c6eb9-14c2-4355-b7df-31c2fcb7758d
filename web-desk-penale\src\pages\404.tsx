import { Box, Button, Grid, Typography, useTheme } from '@mui/material';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { NsButton } from '@netservice/astrea-react-ds';
import {GetAuthUser} from "../components/shared/Utils";
import {useConfig} from "../components/shared/configuration.context";
import {signOut} from "next-auth/react";

/*
 * This component handles http 404 error
 *   The server cannot find the requested resource.
 *   In the browser, this means the URL is not recognized.
 *   In an API, this can also mean that the endpoint is valid but the resource itself does not exist.
 *   Servers may also send this response instead of 403 Forbidden to hide the existence of a resource
 *   from an unauthorized client. This response code is probably the most well known due to its frequent
 *   occurrence on the web.
 * */
const Custom404 = () => {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const router = useRouter();

  const { data: user, token } = GetAuthUser();
  const {
    azureRedirectUri,
    microsoftTenantId,
    azureAdB2cHostName,
    azureAdB2cTenantName,
    azureAdB2cPrimaryUserFlow,
  } = useConfig();

  async function redirectTo() {
    try {
      localStorage.clear();
      await signOut();

      const logoutUrl = `https://${azureAdB2cHostName}/${azureAdB2cTenantName}.onmicrosoft.com/${azureAdB2cPrimaryUserFlow}/oauth2/v2.0/logout?id_token_hint=${token}&post_logout_redirect_uri=https://login.microsoftonline.com/${microsoftTenantId}/oauth2/v2.0/logout?post_logout_redirect_uri=${azureRedirectUri}`;

      await router.push(logoutUrl);
    } catch (error) {
      console.error("Errore durante il logout:", error);
    }
  }

// funzione che mi riporta alla pagina di login IAM
  const goToLogin = async () => {
    await redirectTo();
  };

  const handleGoBack = async () => {
    await goToLogin();
  };

  return (
    <Grid p={2}>
      <Box
        border={theme.custom.borders[1]}
        p={3}
        width={'30%'}
        borderRadius="5px"
      >
        <Typography
          color={'primary'}
          sx={{ fontSize: '4rem !important', fontWeight: 'bold' }}
          mb={2}
        >
          {t('pages.custom404.404')}
        </Typography>
        <Typography variant="h2" mb={2}>
          {t('pages.custom404.paginaORisorsaNonTrovata')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.404.indirizzoWebCorretto')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.custom404.seIndirizzoIncollatoControllalo')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.custom404.erroreAncoraPresenteContattaHelpDesk')}
        </Typography>
        <NsButton variant="contained" color="primary" onClick={handleGoBack}>
          {t('pages.custom404.tornaAllaPaginaPrecedente')}
        </NsButton>
      </Box>
    </Grid>
  );
};

export default Custom404;
