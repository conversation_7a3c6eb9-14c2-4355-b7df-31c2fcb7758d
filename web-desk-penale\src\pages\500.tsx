import { Button, Grid, Typography, useTheme } from '@mui/material';
import { Box } from '@mui/system';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

/*
 * This component handles http 500 error
 *   The server has encountered a situation it does not know how to handle.
 * */
const Custom500 = () => {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const router = useRouter();
  const handleGoBack = () => {
    router.back();
  };
  return (
    <Grid p={3}>
      <Box
        border={theme.custom.borders[1]}
        p={3}
        width={'30%'}
        borderRadius="5px"
      >
        <Typography
          color={'primary'}
          sx={{ fontSize: '4rem !important', fontWeight: 'bold' }}
          mb={2}
        >
          500
        </Typography>
        <Typography variant="h4">
          {t('pages.500.erroreInternoAlServer')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.500.indirizzoWebCorretto')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.500.seIndirizzoIncollatoControllalo')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.500.erroreAncoraPresenteContattaHelpDesk')}
        </Typography>
        <Button variant="contained" color="primary" onClick={handleGoBack}>
          {t('pages.500.tornaAllaPaginaPrecedente')}
        </Button>
      </Box>
    </Grid>
  );
};

export default Custom500;
