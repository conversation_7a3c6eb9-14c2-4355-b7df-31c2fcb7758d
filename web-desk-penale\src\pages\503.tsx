import { Box, Grid, Typography, useTheme } from '@mui/material';
import {useTranslation} from "next-i18next";

/*
* This component handles http 503 error
*   The HyperText Transfer Protocol (HTTP) 503 Service Unavailable
*   server error response code indicates that the server is not ready
*   to handle the request.
*   Common causes are a server that is down for maintenance or that is overloaded.
* */
export const Custom503 = () => {
  const {t} = useTranslation();
  const theme: any = useTheme();
  return (
    <Grid p={2}>
      <Box border={theme.custom.borders[1]}
           p={3}
           width={'30%'}
           borderRadius="5px"
      >
        <Typography
          color={'primary'}
          sx={{ fontSize: '4rem !important', fontWeight: 'bold' }}
          mb={2}
        >
          503
        </Typography>
        <Typography variant="h2" mb={2}>
          {t('pages.custom503.servizioNonDisponibile')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.custom503.messageOf503')}
        </Typography>
        <Typography mb={2} variant="body1">
          {t('pages.custom503.contattareCED')}
        </Typography>
      </Box>
    </Grid>
  );
};
export default Custom503;
