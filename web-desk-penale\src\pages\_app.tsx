import '@fontsource/titillium-web/300.css';
import '@fontsource/titillium-web/400.css';
import '@fontsource/titillium-web/600.css';
import '@fontsource/titillium-web/700.css';

import '../styles/globals.css';
import '../styles/Calendar.css';
import {
  Backdrop,
  Box,
  CircularProgress,
  CssBaseline,
  ThemeProvider,
} from '@mui/material';
import { NotificationProvider } from 'src/utils/NotificationProvider';
import axios from 'axios';
import { AppProps } from 'next/app';
import { NextPage } from 'next';
import { SessionProvider, useSession } from 'next-auth/react';
import { appWithTranslation, useTranslation } from 'next-i18next';
import React, { useEffect, useState } from 'react';
import { RelayEnvironmentProvider } from 'relay-hooks';
import CalendarMenu from 'src/components/MenuHeader/CalendarMenu';
import initEnvironment from 'src/components/relay/createRelayEnvironment';
import {
  PortaleConfigurationContext,
  PortaleDeskCassPConfiguration,
  useConfig,
} from 'src/components/shared/configuration.context';
import { DefaultLayout } from 'src/layouts/DefaultLayout';
import nextI18nConfig from 'i18n/next-i18next.config';
import { theme } from '../theme/portaleCassazione';
import { AxiosInteceptor } from 'src/axiosInterceptorInstance';
import { useGetRuolo } from 'src/components/shared/GetRuolo';
import { useRouter } from 'next/router';
import { NsFooter, NsFullPageSpinner } from '@netservice/astrea-react-ds';

async function loadConf() {
  const res = await axios.get<PortaleDeskCassPConfiguration>(
    `${process.env.NEXT_PUBLIC_BASE_PATH || ''}/api/configuration`
  );
  if (res.status === 200) {
    return res.data;
  } else {
    throw res.statusText;
  }
}

export type NextPageWithLayout = NextPage & {
  getLayout?: (page: React.ReactElement) => React.ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

const MyApp: React.FC<AppPropsWithLayout> = (appProps: AppPropsWithLayout) => {
  const [serviziGraphQl, setServiziGraphql] = useState<string>('');
  const [servizi, setServizi] = useState<string>('');
  const [isDebug, setIsDebug] = useState<any>(false);
  const [azureRedirectUri, setAzureRedirectUri] = useState<string>('');
  const [microsoftTenantId, setMicrosoftTenantId] = useState<string>('');
  const [azureAdB2cHostName, setAzureAdB2cHostName] = useState<string>('');
  const [azureAdB2cClientId, setAzureAdB2cClientId] = useState<string>('');
  const [azureAdB2cTenantName, setAzureAdB2cTenantName] = useState<string>('');
  const [enableFirmaOtp, setEnableFirmaOtp] = useState<boolean>(false);
  const [azureAdB2cPrimaryUserFlow, setAzureAdB2cPrimaryUserFlow] = useState<string>('');
  const [autoSaveTimer, setAutoSaveTimer] = useState<number>(60);
  const [notifichationsCount, setNotifichationsCount] = useState<{ read: number; unread: number }>({
    read: 0,
    unread: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isNavigating, setIsNavigating] = useState(false);

  const { i18n } = useTranslation();
  const router = useRouter();

  useEffect(() => {
    loadConf()
      .then((res: any) => {
        setServiziGraphql(res?.servizi as string);
        setServizi(res?.serviziApi as string);

        const debug = ['true', '1'].includes(
          res.isDebug?.toString().toLowerCase()
        );
        const firmaOtp = ['true', '1'].includes(
          res.enableFirmaOtp?.toString().toLowerCase()
        );

        setIsDebug(debug);
        setEnableFirmaOtp(firmaOtp);
        setAzureRedirectUri(res.azureRedirectUri);
        setMicrosoftTenantId(res.microsoftTenantId);
        setAzureAdB2cHostName(res.azureAdB2cHostName);
        setAzureAdB2cClientId(res.azureAdB2cClientId);
        setAzureAdB2cTenantName(res.azureAdB2cTenantName);
        setAzureAdB2cPrimaryUserFlow(res.azureAdB2cPrimaryUserFlow);
        setAutoSaveTimer(parseFloat(res.autoSaveTimer) || 60);
        setIsLoading(false);
      })
      .catch((err) => {
        console.error('Configuration loading error:', err);
        setIsLoading(false);
      });
  }, []);

  useEffect(() => {
    const handleStart = (url: string) => {
      if (url.split('?')[0] !== router.asPath.split('?')[0]) {
        setIsNavigating(true);
      }
    };
    const handleComplete = (url: string) => {
        setIsNavigating(false);
    };
    const handleError = (err: any) => {
      console.error('Route change error:', err);
      setIsNavigating(false);
    };

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleError);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleError);
    };
  }, [router.asPath, router.events]);

  const getLayout = appProps.Component.getLayout ?? ((page: React.ReactElement) => <DefaultLayout>{page}</DefaultLayout>);

  if (isLoading || !i18n.hasLoadedNamespace('translation') || !servizi) {
    return (
      <Backdrop
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
        }}
        open={true}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    );
  }

  return (
    <>
      <CssBaseline />
      <ThemeProvider theme={theme}>
        <SessionProvider session={appProps.pageProps.session}>
          <NotificationProvider>
            <PortaleConfigurationContext.Provider
              value={{
                servizi,
                serviziGraphQl,
                isDebug,
                autoSaveTimer,
                azureRedirectUri,
                microsoftTenantId,
                notifichationsCount,
                setNotifichationsCount,
                azureAdB2cHostName,
                azureAdB2cClientId,
                azureAdB2cTenantName,
                azureAdB2cPrimaryUserFlow,
                enableFirmaOtp,
              }}
            >
              {isNavigating && (
                <Box position={'fixed'} top={0} left={0} width={'100%'} height={'100%'} zIndex={2000}>
                  <NsFullPageSpinner value={1} isOpen={true} />
                </Box>
              )}
              {getLayout(<AppInner {...appProps} />)}
            </PortaleConfigurationContext.Provider>
          </NotificationProvider>
        </SessionProvider>
      </ThemeProvider>
    </>
  );
};

const AppInner = ({ Component, pageProps }: AppPropsWithLayout) => {
  const { data: session, status } = useSession();
  const getRuolo = useGetRuolo();
  const { serviziGraphQl, isDebug } = useConfig();
  const [isReady, setIsReady] = useState(false);
  const router = useRouter();

  const links = [
    { href: '#', text: 'Accessibility' },
    { href: '#', text: 'Sitemap' },
    { href: '#', text: 'Cookies' },
    { href: '#', text: 'Privacy' },
  ];

  AxiosInteceptor();

  useEffect(() => {
    if (status === "authenticated" && !isReady) {
      getRuolo().then(() => {
        setIsReady(true);
      });
    } else if (status === "unauthenticated") {
      setIsReady(true);
    }
  }, [status, getRuolo, isReady]);

  useEffect(() => {
    if (isReady) {
      const handleRouteChange = () => {
        window.scrollTo(0, 0);
      };
      router.events.on('routeChangeComplete', handleRouteChange);
      return () => {
        router.events.off('routeChangeComplete', handleRouteChange);
      };
    }
  }, [isReady, router]);

  const { environment } = React.useMemo(
    () => initEnvironment({ endpoint: serviziGraphQl, isDebug }),
    [serviziGraphQl, isDebug]
  );

  if (!isReady) {
    return (
      <Backdrop
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
        }}
        open={true}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    );
  }

  return (
    <RelayEnvironmentProvider environment={environment}>
      {session?.accessToken && <CalendarMenu />}
      <Box
        sx={{
          overflowX: 'hidden',
          overflowY: 'auto',
          height: session?.accessToken ? `calc(100vh - 281px)` : '',
        }}
      >
        <Component {...pageProps} />
      </Box>
      {session?.accessToken && (
          <NsFooter
            type="simple"
            links={links}
            logoPath="../../images/logoMinisteroBlack.png"
          />
        )}
    </RelayEnvironmentProvider>
  );
};

export default appWithTranslation(MyApp, nextI18nConfig);
