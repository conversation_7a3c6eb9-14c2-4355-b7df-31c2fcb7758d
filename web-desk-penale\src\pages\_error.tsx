function Error({ statusCode }: any) {
  return (
    <p>
      {statusCode
        ? `An error ${statusCode} occurred on server`
        : 'An error occurred on client'}
    </p>
  );
}

/* Client error response 404: Not Found */
Error.getInitialProps = ({ res, err }: any) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

/* Server error response 500: Internal Server Error */
Error.getInitialProps = ({res, err}: any) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 500;
  return {statusCode};
}

/* Server error response 503: Service Unavailable */
Error.getInitialProps = ({res, err}: any) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 503;
  return {statusCode};
}

export default Error;
