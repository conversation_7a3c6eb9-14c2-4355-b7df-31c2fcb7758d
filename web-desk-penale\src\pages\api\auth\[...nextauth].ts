import { timeStamp } from 'console';
import NextAuth from 'next-auth';
import { DefaultSession, Session } from 'next-auth/core/types';
import { JWT } from 'next-auth/jwt';
import AzureADB2CProvider from 'next-auth/providers/azure-ad-b2c';
import KeycloakProvider from 'next-auth/providers/keycloak';
import fetch from 'node-fetch';

const azureAdB2cTenantName = process.env.AZURE_AD_B2C_TENANT_NAME;
const azureAdB2cHostName = process.env.AZURE_AD_B2C_HOST_NAME;

const azureAdB2cClientId = process.env.AZURE_AD_B2C_CLIENT_ID;
const azureAdB2cClientSecret = process.env.AZURE_AD_B2C_CLIENT_SECRET;

const azureAdB2cPrimaryUserFlow = process.env.AZURE_AD_B2C_PRIMARY_USER_FLOW;

const azureScopePortaleDeskCassp = process.env.AZURE_SCOPE_PORTALE_DESKCASSP;

const azureScopeFirmaRemota = process.env.AZURE_SCOPE_FIRMA_REMOTA;

// Funzione per ottenere la data corrente formattata 
function getCurrentDate() { const now = new Date(); return now.toISOString() + '--';} 



console.log(getCurrentDate(), 'Inizio inizializzazione del provider di autenticazione su Azure AD B2C - Tenant Name: ' + azureAdB2cTenantName);

let HttpsProxyAgent = require('https-proxy-agent');

async function refreshAccessToken(token: JWT) {
  try {
    console.log(getCurrentDate(), 'Inizio refresh del token');
    //TODO vecchia configurazione
    // const url = `https://${azureAdB2cTenantName}.b2clogin.com/${azureAdB2cTenantName}.onmicrosoft.com/${azureAdB2cPrimaryUserFlow}/oauth2/v2.0/token`;
    const url = `https://${azureAdB2cHostName}/${azureAdB2cTenantName}.onmicrosoft.com/${azureAdB2cPrimaryUserFlow}/oauth2/v2.0/token`;

    let proxyAgent = undefined;
    if (process.env.http_proxy) {
      proxyAgent = new HttpsProxyAgent.HttpsProxyAgent(process.env.http_proxy);
    }

    console.log(getCurrentDate(), 'Chiamata al servizio di refresh token');
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      method: 'POST',
      agent: proxyAgent,
      body: `client_id=${azureAdB2cClientId}&grant_type=refresh_token&refresh_token=${token.refreshToken}&client_secret=${azureAdB2cClientSecret}`
    });

    console.log(getCurrentDate(), 'Attesa della risposta dal servizio di refresh token da Microsoft');
    const refreshedTokens = await response.json();

    if (!response.ok) {
      throw refreshedTokens;
    }

    console.log(getCurrentDate(), 'Refresh del token jwt andato a buon fine');
    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: Date.now() + refreshedTokens.expires_in * 1000,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken // Fall back to old refresh token
    };
  } catch (error) {
    console.log(getCurrentDate(), 'Richiesta refresh token jwt non riuscita', error);

    return {
      ...token,
      error: 'RefreshAccessTokenError'
    };
  }
}

const Auth = (req: any, res: any) => {
  return NextAuth(req, res, {
    providers: [
      //Provider ufficiale di DGSIA
      AzureADB2CProvider({        
        checks: ['none'],
        tenantId: azureAdB2cTenantName as string,
        clientId: azureAdB2cClientId as string,
        clientSecret: azureAdB2cClientSecret as string,
        primaryUserFlow: azureAdB2cPrimaryUserFlow as string,
        issuer: `https://${azureAdB2cHostName}/${azureAdB2cTenantName}.onmicrosoft.com/${azureAdB2cPrimaryUserFlow}/v2.0`,
        authorization: {
          params: {
            scope:
              'offline_access openid' +
              ' ' +
              azureScopePortaleDeskCassp +
              ' ' +
              azureScopeFirmaRemota,
            //grant_type: 'authorization_code',
          },
        },

        profileUrl: 'https://graph.microsoft.com/oidc/userinfo',
        profile: (profile) => {

          console.log(getCurrentDate(), 'Profile', profile.ADN_SamAccountName + ' - ' + profile.jobTitle + ' - ' + profile.officeLocation);
          return {
            id: profile.sub,
            cf: profile.fiscalNumber,
            ADN_user: profile.ADN_user,
            jobTitle: profile.jobTitle,
            fName: profile.given_name,
            lName: profile.family_name,
            officeLocation: profile.officeLocation,
            email:
              profile.emails != null && profile.emails.length
                ? profile.emails[0]
                : null,
          };
        },
        //checks: ['none'],
        // client: {
        //   token_endpoint_auth_method: 'none',
        // },
      }),
      // Provider Net interno
      KeycloakProvider({
         clientId: 'CassazioneDeskPenale-Dev',
         clientSecret: 'vxkH9CJVzpq63tvANbiOD6ZNZZp8jee8',
         issuer: 'http://dockerpa3.netserv.it:8091/realms/casspenale',
         profileUrl: 'http://dockerpa3.netserv.it:8091/realms/casspenale/protocol/openid-connect/userinfo',
         profile: (profile) => {
             console.log(profile);
 
           console.log(getCurrentDate(), 'Profile', profile.ADN_SamAccountName + ' - ' + profile.jobTitle + ' - ' + profile.officeLocation);
           return {
             id: profile.sub,
             cf: profile.fiscalNumber,
             ADN_user: profile.ADN_user,
             jobTitle: profile.jobTitle,
             fName: profile.given_name,
             lName: profile.family_name,
             officeLocation: profile.officeLocation,
             email:
               profile.emails != null && profile.emails.length
                 ? profile.emails[0]
                 : null,
           };
         },
       }),
    ],
    secret: process.env.NEXTAUTH_SECRET,
    callbacks: {
      async jwt({ token, account, user, trigger, profile }) {
        if (trigger === 'update') {
          return refreshAccessToken(token);
        }

        if (account && user) {
          token.user = user;
          token.accessToken = account.access_token;

          token.accessTokenExpires = (account.expires_at as any) * 1000;
          token.refreshToken = account.refresh_token;
        }

        if (typeof token.accessTokenExpires === 'number') {
          if (Date.now() < token.accessTokenExpires) {
            console.log(getCurrentDate(), 'Token jwt non ancora scaduto')
            return token;
          }
        }

        console.log(getCurrentDate(), 'Chiamata al refresh del token jwt')
        return refreshAccessToken(token);
      },
      async session({ session, token}) {
        // Send properties to the client, like an access_token and user id from a provider.
        session.user = token.user as any;
        session.cf = (token.user as any).cf as string;
        session.name = (token.user as any).fName as string;
        session.accessToken = token.accessToken as string;
        if (typeof token.accessTokenExpires === 'number') {
          session.accessTokenExpires = token.accessTokenExpires;
          //console.log('session.accessToken', session.accessToken);
        }
        return session as Session | DefaultSession;
      },
    },
     pages: {
       signIn: '/login',
     },
  });
};

export default Auth;