import { NextApiRequest, NextApiResponse } from 'next';
import * as process from 'process';

const conf = {
  servizi: process.env.SERVIZI_PORTALE_URL,
  serviziApi: (process.env.SERVIZI_PORTALE_URL || '') + (process.env.GLOBAL_PATH ? '/'+ process.env.GLOBAL_PATH : ''),
  isDebug: process.env.IS_DEBUG,
  enableFirmaOtp: process.env.ENABLE_FIRMA_OTP,
  autoSaveTimer: process.env.AUTOSAVE_TIMER,
  azureAdB2cHostName: process.env.AZURE_AD_B2C_HOST_NAME,
  azureRedirectUri: process.env.AZURE_REDIRECT_URI,
  microsoftTenantId: process.env.MISCROSOFT_TENANT_ID,
  azureAdB2cClientId: process.env.AZURE_AD_B2C_CLIENT_ID,
  azureAdB2cClientSecret: process.env.AZURE_AD_B2C_CLIENT_SECRET,
  azureAdB2cTenantName: process.env.AZURE_AD_B2C_TENANT_NAME,
  azureAdB2cPrimaryUserFlow: process.env.AZURE_AD_B2C_PRIMARY_USER_FLOW,
  digitalSignatureAuthorityAzure: process.env.DIGITAL_SIGNATURE_AUTHORITY_AZURE,
  digitalSignatureClientIdAzure: process.env.DIGITAL_SIGNATURE_CLIENT_ID_AZURE,
  digitalSignatureRedirectUriAzure:
    process.env.DIGITAL_SIGNATURE_REDIRECT_URI_AZURE,
  azureScopePortaleDeskCassp: process.env.AZURE_SCOPE_PORTALE_DESK_CASSP,
};

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  res.status(200).json(conf);
}
