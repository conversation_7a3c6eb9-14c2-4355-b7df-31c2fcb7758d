import Impostazioni from "../../components/impostazioni/Impostazioni";
import { useEffect, useState } from 'react';
import { useGetRuolo } from 'src/components/shared/GetRuolo';
import { userRole } from "src/components/shared/Utils";

export default function ImpostazioniIndex() {
    
   
    const getRuolo = useGetRuolo();
    const [ruolo] = useState<any>(userRole() ?? getRuolo());

    console.log('ruolo', ruolo);


    useEffect(() => {
      getRuolo();
    }, []);

    return  <Impostazioni ruolo={ruolo}/>
}
