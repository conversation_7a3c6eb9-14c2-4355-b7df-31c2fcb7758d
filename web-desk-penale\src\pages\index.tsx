import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import Scrivania from 'src/components/scrivania/Scrivania';
import Calendario from 'src/components/calendar/Calendario';
import {userRole } from 'src/components/shared/Utils';
import { useState } from 'react';
import { useGetRuolo } from 'src/components/shared/GetRuolo';

export default function Home() {
  const allowedRoles = ['RELATORE', 'ESTENSORE'];
  const getRuolo = useGetRuolo();
  const [ruolo] = useState<any>(userRole() ?? getRuolo());

  if (ruolo === null) {
    return <NsFullPageSpinner isOpen={true} value={1} />;
  }

  if (ruolo?.length > 0) {
    if (ruolo.some((role: string) => role === 'PRESIDENTE')) {
      return <Scrivania />;
    } else if (ruolo.some((role: string) => allowedRoles.includes(role))) {
      return <Calendario />;
    }
  } else {
    return <Calendario />;
  }
}
