import React from 'react';
import { Typo<PERSON>, Box, Container, Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { STORE_OR_NETWORK, useQuery } from 'relay-hooks';
import { versionQuery } from '@/generated/versionQuery.graphql';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NsFullPageSpinner,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { versionQuerySchema } from '../../components/relay/versionQuery';
import packageJson from '../../../package.json';
import { errorLogger, Severity } from '../../utils/errorLogger';

const ROOT_QUERY = versionQuerySchema;

const InfoPage: React.FC = () => {
  const { t } = useTranslation();

  const { notify } = useNotifier();

  const deskFrontendVersion = packageJson.version;

  const { data, isLoading } = useQuery<versionQuery>(
    ROOT_QUERY,
    {},
    {
      fetchPolicy: STORE_OR_NETWORK,
    }
  );

  const downloadErrors = () => {
    try {
      const errors = errorLogger.getLogs();
      if (errors.length === 0) {
        notify({
          type: 'warning',
          message: t('info.noErrors'),
        });
        return;
      }

      const blob = new Blob([JSON.stringify(errors, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = 'errors.json';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notify({
        type: 'success',
        message: t('info.errorsDownloaded'),
      });
    } catch (error) {
      errorLogger.log({
        message: 'Error downloading errors',
        severity: Severity.ERROR,
        context: { error },
        component: 'InfoPage',
      });
      notify({
        type: 'error',
        message: t('info.errorDownloadingErrors'),
      });
    }
  };

  const backendVersion = data?.appVersions?.appVersion;
  const serviziDepositoVersion = data?.appVersions?.serviziDepositoVersion;

  if (isLoading) {
    return <NsFullPageSpinner isOpen={true} value={1} />;
  }

  return (
    <Container maxWidth="md">
      <Box my={4} border={1} borderColor="grey.300" p={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('info.versioni')}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1">
              Desk Cassazione Penale (Frontend):{' '}
              <b>{deskFrontendVersion}</b>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1">
              Servizi Desk (Backend): <b>{backendVersion || 'N/A'}</b>
            </Typography>
            <Typography variant="body1">
              Servizi Deposito Portale:{' '}
              <b>{serviziDepositoVersion || 'N/A'}</b>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <NsButton
              variant="contained"
              color="primary"
              onClick={downloadErrors}
            >
              {t('info.downloadErrors')}
            </NsButton>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default InfoPage;