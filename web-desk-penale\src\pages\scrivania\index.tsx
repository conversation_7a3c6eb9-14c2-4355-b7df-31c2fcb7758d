import Scrivania from 'src/components/scrivania/Scrivania';
import { Typography } from '@mui/material';
import { useTranslation } from 'next-i18next';
import { userRole } from 'src/components/shared/Utils';
import { useGetRuolo } from 'src/components/shared/GetRuolo';

const ALLOWED_ROLES = ['PRESIDENTE'] as const;

export default function UdienzaIndex() {
  const { t } = useTranslation();
  const getRuolo = useGetRuolo();
  const ruolo = userRole() ?? getRuolo();

  if (ruolo?.some((role: typeof ALLOWED_ROLES[number]) => ALLOWED_ROLES.includes(role))) {
    return <Scrivania />;
  }

  return (
    <Typography p={2} variant="h2">
      {t('pages.scrivania.index.nessunaUdienzaPresenteComePresidenteDiCollegio')}
    </Typography>
  );
}
