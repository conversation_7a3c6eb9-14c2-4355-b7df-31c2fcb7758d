table .ns-custom-tr  > td  {
    padding: 16px 8px;
    text-align: left;
}
table .ns-custom-tr  > td > div p{
    white-space: nowrap;
    /*width: 50px;*/
    overflow: hidden;
    text-overflow: ellipsis;
}
table .ns-custom-tr  > td > div.ns-display-2col{
    display: grid;
    grid-template-columns: auto 70px;
}
table .ns-custom-tr  > td > div.ns-display-2col button{
    align-content: end;
    justify-content: end;
}

/*table .ns-custom-tr  > td > div > div:nth-child(2){
    float: right;
    max-width: 50px;
}*/

/*
table .ns-custom-tr  > td > div{
    display: grid;
    grid-auto-columns: minmax(0, 1fr);
    grid-auto-flow: column;
}
*/
/*.parent {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
}

.child {
    grid-area: sidebar;
    background-color: #eee;
}

div {
    border: 1px solid grey;
    border-radius: 0.25rem;
    padding: 1rem;
}*/
