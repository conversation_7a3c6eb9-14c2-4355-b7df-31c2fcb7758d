* {
  box-sizing: border-box;
  font-family: 'Titillium Web SemiBold', 'Titillium Web', sans-serif;
}

body::-webkit-scrollbar {
  display: none;
}

.MuiInputBase-root {
  border-radius: 0;
}

.css-19kzrtu {
  height: 100%;
}

.selectedEvent {
  background-color: #ffe300;
}

.ButtonWithCustomBoardHover:hover,
.ButtonWithCustomBoardHoverActive {
  background-color: white !important;
  color: black !important;
  border: 1px solid rgb(230, 230, 230) !important;
}

Button:hover {
  cursor: pointer;
}

.rbc-month-header {
  background-color: rgb(230, 230, 230);
  text-transform: capitalize;
}
.rbc-header {
  font-weight: 600; /*Semibold*/
}

@media (max-width: 767px) {
  .segnelazioniSearch {
    height: 40px;
    font-size: 13px;
  }
}
@media (max-width: 657px) {
  .gri-imp-ns {
    margin-right: 1rem;
  }
}
@media (max-width: 523px) {
  .box-media-ns {
    margin-top: 1rem;
    margin-left: 0 !important;
    padding-right: 2rem;
  }
}
.MuiSwitch-thumb {
  border: 1px solid #d3d3d3;
}
.rbc-agenda-time-cell-scadenza {
  color: transparent;
}
.rbc-agenda-view table.rbc-agenda-table tbody > tr > td {
  min-width: 127px;
}
.rbc-agenda-date-cell-scadenza {
  padding-left: 22px !important;
}
s {
  color: black !important;
}
s > span {
  color: black !important;
}
s {
  background: black !important;
}
.editor > div .editor-intestazione p {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
  font-size: 1rem;
}

.editor .msgEpigrafe {
  background: #0054b44d;
  height: 41px;
  margin-top: 7px;
  padding: 5px;
  color: #0054b4;
  display: flex;
  align-items: center;
  width: 100%;
  border-left: 5px solid;
}

/* TODO: da rimuovere al rilascio di astrea v2.4.0 */
#minute-cards svg {
  display: inherit;
  vertical-align: inherit;
  max-height: inherit;
  margin-left: inherit;
}

.header-title {
  font-size: 1.7rem !important;
  line-height: 1.2 !important;
  font-weight: bold !important;
}

@media (max-width: 960px) {
  .NsCustomLogin .MuiGrid-grid-sm-8 .MuiPaper-root {
    width: 50vw !important;
  }
}

.NsCustomLogin > div p {
  text-align: center;
}

.MuiBreadcrumbs-root {
  color: #308a7d !important;
  font-size: 20px !important;
  float: left !important;
  line-height: 1.2 !important;
}

.nsLoader > div {
  z-index: 13000 !important;
}

.svgInfoVal {
  display: inline-block !important;
}

.svgInfoVal > svg {
  font-size: 18px;
  fill: #308a7d !important;
  margin-left: 5px;
}
.provvedimenti-verifica table {
  border: 1px solid #e0e0e0 !important;
  width: 100% !important;
  margin: auto;
}

.provvedimenti-verifica table td,
.provvedimenti-verifica table th {
  flex: unset !important;
  min-width: unset !important;
  max-width: unset !important;
  width: 100% !important;
}

.provvedimenti-verifica table td:last-of-type,
.provvedimenti-verifica table th:last-of-type {
  width: 70% !important;
}

.provvedimenti-verifica table td:first-of-type,
.provvedimenti-verifica table th:first-of-type {
  width: 40% !important;
}

.provvedimenti-verifica table td:nth-of-type(2),
.provvedimenti-verifica table th:nth-of-type(2) {
  width: 70% !important;
}

.provvedimenti-verifica table td:nth-of-type(6),
.provvedimenti-verifica table th:nth-of-type(6) {
  width: 130% !important;
}

.provvedimenti-verifica table tbody tr {
  background-color: transparent !important;
}

.provvedimenti-verifica table tbody tr td {
  background-color: white !important;
  border-bottom: none !important;
}

@media (max-width: 768px) {
  header div.open > nav {
    flex-direction: row !important;
  }

  header nav > a {
    display: block !important;
    min-height: 2em !important;
  }

  header div.open > div.MuiBox-root {
    justify-content: end !important;
  }

  header > div.open > div.MuiBox-root > nav {
    display: flex !important;
    width: 100% !important;
  }
}

#\:r5\:,
#\:rb\: {
  display: none !important;
}

.calendario-table {
  position: relative;
  height: '600px';
}

.calendario-table table {
  width: 100% !important;
}

.calendario-table table tr {
  container-type: inline-size;
}

@container (min-width: 1550px) {
  .calendario-table table td,
  .calendario-table table th {
    flex: unset !important;
    min-width: unset !important;
    max-width: unset !important;
    width: 100% !important;
  }

  .calendario-table table td:first-of-type,
  .calendario-table table th:first-of-type,
  .calendario-table table td:nth-of-type(3),
  .calendario-table table th:nth-of-type(3) {
    width: 40% !important;
  }

  .calendario-table table td:nth-of-type(7),
  .calendario-table table th:nth-of-type(7),
  .calendario-table table td:nth-of-type(9),
  .calendario-table table th:nth-of-type(9) {
    width: 50% !important;
  }

  .calendario-table table td:nth-of-type(5),
  .calendario-table table th:nth-of-type(5),
  .calendario-table table td:nth-of-type(10),
  .calendario-table table th:nth-of-type(10),
  .calendario-table table td:last-of-type,
  .calendario-table table th:last-of-type {
    width: 70% !important;
  }
}

#account-menu div.MuiPaper-root {
  margin-top: 40px;
}
