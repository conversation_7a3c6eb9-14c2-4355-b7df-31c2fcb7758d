// types.ts
import { ReactNode } from 'react';

// Enumerazioni già esistenti nel file originale
export enum StatoProvvedimentiEnum {
  IN_BOZZA = 'IN_BOZZA', // Usato nella ricerca per stato
  BOZZA = 'BOZZA',
  IN_CODE_FIRMA_REL = 'IN_CODE_FIRMA_REL',
  ERRORE_DI_PUBBLICAZIONE = 'ERRORE_DI_PUBBLICAZIONE',
  INVIATO_IN_CANCELLERIA_RELATORE = 'INVIATO_IN_CANCELLERIA_RELATORE',
  BUSTA_RIFIUTATA = 'BUSTA_RIFIUTATA',
  BUSTA_RIFIUTATA_AL_PRESIDENTE = 'BUSTA_RIFIUTATA_AL_PRESIDENTE',
  MINUTA_ACCETTATA = 'MINUTA_ACCETTATA',
  MINUTA_DA_MODIFICARE = 'MINUTA_DA_MODIFICARE',
  MINUTA_MODIFICATA = 'MINUTA_MODIFICATA',
  INVIATO_IN_CANCEL_PRESIDENTE = 'INVIATO_IN_CANCEL_PRESIDENTE',
  MINUTA_DEPOSITATA = 'MINUTA_DEPOSITATA',
  MINUTA_DEPOSITATA_SIC = 'MINUTA_DEPOSITATA_SIC',
  PUBBLICATA = 'PUBBLICATA',
  PROVV_DEPOSITATO_SIC = 'PROVV_DEPOSITATO_SIC',
  PUBBLICATO_SIC = 'PUBBLICATO_SIC',
  CODA_DI_FIRMA = 'CODA_DI_FIRMA',
  MINUTA_IN_REVISIONE = 'MINUTA_IN_REVISIONE',
  MINUTA_MODIFICATA_PRESIDENTE = 'MINUTA_MODIFICATA_PRESIDENTE',
  BOZZA_PRESIDENTE = 'BOZZA_PRESIDENTE',
  RIUNITO = 'RIUNITO',
  RIUNITO_CARTACEO = 'RIUNITO_CARTACEO',
  DA_REDIGERE = 'DA_REDIGERE',
  IN_RELAZIONE_ESTENSORE = 'IN_RELAZIONE_ESTENSORE',
}

export enum ProvvedimentiTipoEnum {
  MINUTA_SENTENZA = 'MINUTA_SENTENZA',
  MINUTA_ORDINANZA = 'MINUTA_ORDINANZA',
  SENTENZA = 'SENTENZA',
  ORDINANZA = 'ORDINANZA',
}

export enum SezioneEnum {
  S1 = 'S1',
  S2 = 'S2',
  S3 = 'S3',
  S4 = 'S4',
  S5 = 'S5',
  S6 = 'S6',
  S7 = 'S7',
  SU = 'SU',
}

export enum TipoUdienzaEnum {
  CC = 'Camera di Consiglio',
  PU = 'Pubblica Udienza',
}

// Interfacce per i dati
export interface Provvedimento {
  idProvvedimento: string;
  numOrdine: number;
  nrg: string;
  nrgNumeric?: number;
  relatore: string;
  oscuratoSIC: boolean;
  oscuratoDESKCSP: boolean;
  stato: StatoProvvedimentiEnum;
  tipologia: ProvvedimentiTipoEnum;
  dataDeposito?: string;
  scadenza?: string;
  isEstensore?: boolean;
  riunitoView?: RiunitoView;
  idRicUdien?: number;
}

export interface RiunitoView {
  isPrincipalRicorsoRiunito?: boolean;
  ricorsoRiunito?: {
    numero: string;
    anno: string;
  };
}

export interface Udienza {
  idUdienza: number;
  dataUdienza: string;
  sezione: SezioneEnum;
  tipoUdienza: string;
  aula: string | null; // Permettere null
}

export interface VerificaProvvedimentoProps {
  onRowClick?: (row: any) => void;
}
// Definizione del tipo per il riferimento della tabella
export interface ProvvedimentiTableRef {
  refreshData: () => void;
}

// Interfaccia aggiornata per ProvvedimentiTableProps
export interface ProvvedimentiTableProps {
  queryFragment: any;
  idUdienza: number;
  selected: string[];
  onSelect: (selected: string[]) => void;
  onRowClick?: (row: any) => void;
  onViewClick: (provvId: string, nrg: any, actionId: number) => void;
  onModalOpen: (param: string, data?: any) => void;
}

export interface TableRenderersProps {
  theme: any;
  t: (key: string) => string;
  handleViewClick: (provvId: string, nrg: any, actionId: number) => void;
  handleModal: (param: string, data?: any) => void;
  selected: string[];
  handleClick: (
    event: React.MouseEvent<unknown>,
    index: number,
    row: any
  ) => void;
  handleSelectAllClick: (event: React.MouseEvent<unknown>, rows: any[]) => void;
}

export interface BulkActionsProps {
  selected: string[];
  onVerificaSelezionati: () => void;
  disabled: boolean;
  t: (key: string) => string;
}

export interface ProvvedimentiPreviewProps {
  showPdfPreview: boolean;
  showBulkPdfPreview: boolean;
  selectedProvv?: string | null;
  selected: string[];
  nrg?: string;
  idUdienza?: number;
  refreshCoda: boolean;
  setRefreshCoda: (value: boolean) => void;
  refreshPage: () => void;
  refreshTableOnly?: () => void;
}

export interface BreadcrumbHeaderProps {
  udienza?: Udienza;
  t: (key: string) => string;
}

export interface UdienzaInfoProps {
  udienza?: Udienza;
  t: (key: string) => string;
}

export interface FilterOption {
  KeyName: string;
  value: string;
}

// Stato del componente principale
export interface VerificaProvvedimentoState {
  filteredFascicoli: Provvedimento[];
  selectedOption: string | null;
  selected: string[];
  fascicoli: Provvedimento[];
  showPdfPreview: boolean;
  showBulkPdfPreview: boolean;
  selectedProvv: string | null;
  refreshCoda: boolean;
  orderBy?: string;
  order: 'asc' | 'desc';
  modalProps: {
    isOpen: boolean;
    title: string;
    content: ReactNode;
    openFromParent: boolean;
    onClose: (
      event: React.MouseEvent<HTMLButtonElement>,
      reason: string
    ) => void;
  };
}
