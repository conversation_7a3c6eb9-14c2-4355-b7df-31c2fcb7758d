import React from 'react';
import { SnackbarProvider } from 'notistack';
// import { minWidth } from '@mui/system';

// Wrap your app with this provider
export const NotificationProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
    return <SnackbarProvider
        maxSnack={3}
        anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
        }}
        autoHideDuration={7000}
    >
        {children}
    </SnackbarProvider>
};
