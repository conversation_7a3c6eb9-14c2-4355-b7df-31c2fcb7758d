import React from 'react';
import {Modal} from "@mui/material";
import {modalStyle} from "../components/shared/Utils";

export interface ModalProps{
  id?:string;
  isOpen: boolean;
  body?: React.ReactElement;
  title?: string ;
  onClose?: () => {};
  style?: any;
}

export const NsGenericModal = (props: Readonly<ModalProps>) => {
  const { id, isOpen, body, title, onClose, style} = props;
  const defaultClose =  () => {};
  return (
    <Modal id={id || 'genericModal'} open={isOpen} title={title || '' } style={style || modalStyle} onClose={onClose ||  defaultClose}>
      {body || <></>}
    </Modal>
  );
};
