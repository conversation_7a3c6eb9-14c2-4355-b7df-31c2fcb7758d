import { useCallback } from 'react';
import { useSnackbar, VariantType, SnackbarKey } from 'notistack';

interface NotifyOptions {
  type: VariantType;
  message: string;
}
export const useNotifier = () => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const notify = useCallback(({ type, message }: NotifyOptions) => {
    enqueueSnackbar(message, {
      variant: type,
      style: {
        whiteSpace: 'nowrap',
        paddingRight: '110px'
      }
    });
  }, [enqueueSnackbar]);

  const dismiss = useCallback((key: SnackbarKey) => {
    closeSnackbar(key);
  }, [closeSnackbar]);

  return { notify, dismiss };
};
