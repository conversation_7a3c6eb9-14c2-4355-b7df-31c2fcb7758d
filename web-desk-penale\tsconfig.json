{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "importHelpers": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "incremental": true, "paths": {"@/generated/*": ["./__generated__/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "cookies.d.ts", "**/*.ts", "**/*.tsx", "__generated__/*.ts", "pages/_app.js", "src/*/", "src/raw-loader.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}